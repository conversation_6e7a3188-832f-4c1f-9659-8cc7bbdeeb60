{"name": "galaxy-pay-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:staged": "eslint --fix $(git diff --name-only --cached --relative -- '*.js' '*.jsx' '*.ts' '*.tsx')", "preview": "vite preview", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules/.vite"}, "dependencies": {"@microsoft/signalr": "^8.0.7", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.76.1", "antd": "^5.25.2", "axios": "^1.9.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "i18next": "^25.2.0", "localforage": "^1.10.0", "moment": "^2.30.1", "qs": "^6.14.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-router-dom": "^6.28.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.2", "@tailwindcss/vite": "^4.1.7", "@types/crypto-js": "^4.2.2", "@types/node": "^22.15.24", "@types/qs": "^6.14.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "sass-embedded": "^1.89.1", "tailwindcss": "^4.1.7", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}}