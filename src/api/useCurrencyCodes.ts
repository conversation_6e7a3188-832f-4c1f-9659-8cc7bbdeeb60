import { apiClient } from '@/api';
import { queryKeys } from '@/constants';
import { useMasterQuery, type UseMasterQueryProps } from '@/hooks';

type CurrencyCodesProps = unknown;

type CurrencyCode = {
  code: string;
  name: string;
  countryCode: string;
  emojiFlag: string;
};
type CurrencyCodeRes = Array<CurrencyCode>;

type Other = unknown;

const useCurrencyCodes = (
  useProps: UseMasterQueryProps<Other, CurrencyCodesProps, CurrencyCodeRes>,
) => {
  const { params, ...config } = useProps;

  const query = useMasterQuery<CurrencyCodeRes, CurrencyCodesProps>({
    ...config,
    queryKey: queryKeys.common.currencyCodes,
    qf: () => {
      const request = apiClient
        .get('/admin/staffs/currency-codes')
        .then(({ data }) => data);
      return request;
    },
  });
  return { ...query };
};

export { useCurrencyCodes };
export type { CurrencyCode };
