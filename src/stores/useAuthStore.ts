import { create } from 'zustand';
import type { LoginRes } from '@/pages/Login/apis/useLogin';
import { forage } from '@/utils/forageUtils';

type AuthStoreInit = {
  loginRes: LoginRes | null;
  isAuthenticated: boolean;
  /**
   * Indicates whether the auth store has been hydrated with persisted data.
   * This becomes `true` after loading user session info from local storage.
   * Useful to avoid rendering UI or triggering effects before data is ready.
   */
  hydrated: boolean;
};

interface IAuthStoreOptions extends AuthStoreInit {
  setLoginRes: (loginRes: AuthStoreInit['loginRes']) => void;
  clearLoginRes: () => void;
}

const authStoreInit: AuthStoreInit = {
  loginRes: null,
  isAuthenticated: false,
  /**
   * Indicates whether the auth store has been hydrated with persisted data.
   * This becomes `true` after loading user session info from local storage.
   * Useful to avoid rendering UI or triggering effects before data is ready.
   */
  hydrated: false,
};

const useAuthStore = create<IAuthStoreOptions>((set) => {
  set({ hydrated: false });
  forage<AuthStoreInit['loginRes']>()
    .getItem('loginRes', (_, loginRes) => {
      set({ loginRes, isAuthenticated: !!loginRes });
    })
    .finally(() => {
      set({ hydrated: true });
    });

  return {
    ...authStoreInit,
    setLoginRes: (loginRes) => {
      forage<AuthStoreInit['loginRes']>().setItem('loginRes', loginRes, () => {
        set({ loginRes, isAuthenticated: !!loginRes });
      });
    },
    clearLoginRes: () => {
      forage<AuthStoreInit['loginRes']>().setItem('loginRes', null, () => {
        set({ loginRes: null, isAuthenticated: false });
      });
    },
  };
});

export { useAuthStore };
