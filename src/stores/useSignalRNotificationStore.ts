import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  type NotificationPayload,
  NotificationSeverity,
  NotificationType,
  type UINotification,
} from '@/types/notifications';

interface SignalRNotificationState {
  // Notifications
  notifications: UINotification[];
  unreadCount: number;

  // UI state
  isNotificationPanelOpen: boolean;
  maxNotifications: number;

  // Settings
  soundEnabled: boolean;
  showDesktopNotifications: boolean;
  autoMarkAsRead: boolean;
  notificationDuration: number;
}

interface SignalRNotificationActions {
  // Notification actions
  addNotification: (notification: NotificationPayload) => void;
  removeNotification: (notificationId: string) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  clearNotifications: () => void;
  clearOldNotifications: () => void;

  // UI actions
  toggleNotificationPanel: () => void;
  setNotificationPanelOpen: (open: boolean) => void;

  // Settings actions
  setSoundEnabled: (enabled: boolean) => void;
  setShowDesktopNotifications: (enabled: boolean) => void;
  setAutoMarkAsRead: (enabled: boolean) => void;
  setNotificationDuration: (duration: number) => void;
  setMaxNotifications: (max: number) => void;
}

type SignalRNotificationStore = SignalRNotificationState & SignalRNotificationActions;

const initialState: SignalRNotificationState = {
  notifications: [],
  unreadCount: 0,
  isNotificationPanelOpen: false,
  maxNotifications: 100,
  soundEnabled: true,
  showDesktopNotifications: true,
  autoMarkAsRead: false,
  notificationDuration: 5000,
};

// Helper function to convert notification payload to UI notification
const convertToUINotification = (payload: NotificationPayload): UINotification => {
  const baseNotification: UINotification = {
    ...payload,
    autoClose: payload.severity !== NotificationSeverity.Error,
    duration: payload.severity === NotificationSeverity.Error ? 0 : 5000,
  };

  // Add default titles and descriptions based on message key and type
  switch (payload.type) {
    case NotificationType.Order:
      baseNotification.title = 'Order Update';
      baseNotification.actionUrl = `/orders/${payload.metadata?.orderId || ''}`;
      baseNotification.actionText = 'View Order';
      break;
    case NotificationType.User:
      baseNotification.title = 'User Update';
      baseNotification.actionUrl = `/user-management`;
      baseNotification.actionText = 'View Users';
      break;
    case NotificationType.Staff:
      baseNotification.title = 'Staff Update';
      baseNotification.actionUrl = `/management`;
      baseNotification.actionText = 'View Staff';
      break;
    case NotificationType.Counter:
      baseNotification.title = 'Counter Update';
      baseNotification.actionUrl = `/management`;
      baseNotification.actionText = 'View Counters';
      break;
    case NotificationType.System:
      baseNotification.title = 'System Notification';
      break;
    case NotificationType.Warning:
      baseNotification.title = 'Warning';
      break;
    case NotificationType.Info:
      baseNotification.title = 'Information';
      break;
    case NotificationType.Announcement:
      baseNotification.title = 'Announcement';
      break;
    default:
      baseNotification.title = 'Notification';
  }

  return baseNotification;
};

export const useSignalRNotificationStore = create<SignalRNotificationStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Notification actions
      addNotification: (payload) => {
        const uiNotification = convertToUINotification(payload);
        
        set((state) => {
          const newNotifications = [uiNotification, ...state.notifications];
          
          // Limit notifications to maxNotifications
          const limitedNotifications = newNotifications.slice(0, state.maxNotifications);
          
          const unreadCount = limitedNotifications.filter(n => !n.isRead).length;

          return {
            notifications: limitedNotifications,
            unreadCount,
          };
        });
      },

      removeNotification: (notificationId) => {
        set((state) => {
          const notifications = state.notifications.filter(n => n.notificationId !== notificationId);
          const unreadCount = notifications.filter(n => !n.isRead).length;
          
          return { notifications, unreadCount };
        });
      },

      markAsRead: (notificationId) => {
        set((state) => {
          const notifications = state.notifications.map(n =>
            n.notificationId === notificationId ? { ...n, isRead: true } : n
          );
          const unreadCount = notifications.filter(n => !n.isRead).length;
          
          return { notifications, unreadCount };
        });
      },

      markAllAsRead: () => {
        set((state) => ({
          notifications: state.notifications.map(n => ({ ...n, isRead: true })),
          unreadCount: 0,
        }));
      },

      clearNotifications: () => {
        set({ notifications: [], unreadCount: 0 });
      },

      clearOldNotifications: () => {
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
        
        set((state) => {
          const notifications = state.notifications.filter(n => n.createdAt > oneDayAgo);
          const unreadCount = notifications.filter(n => !n.isRead).length;
          
          return { notifications, unreadCount };
        });
      },

      // UI actions
      toggleNotificationPanel: () => {
        set((state) => ({ isNotificationPanelOpen: !state.isNotificationPanelOpen }));
      },

      setNotificationPanelOpen: (open) => {
        set({ isNotificationPanelOpen: open });
      },

      // Settings actions
      setSoundEnabled: (enabled) => {
        set({ soundEnabled: enabled });
      },

      setShowDesktopNotifications: (enabled) => {
        set({ showDesktopNotifications: enabled });
      },

      setAutoMarkAsRead: (enabled) => {
        set({ autoMarkAsRead: enabled });
      },

      setNotificationDuration: (duration) => {
        set({ notificationDuration: duration });
      },

      setMaxNotifications: (max) => {
        set({ maxNotifications: max });
      },
    }),
    {
      name: 'signalr-notification-store',
      partialize: (state) => ({
        // Only persist settings, not the actual notifications
        soundEnabled: state.soundEnabled,
        showDesktopNotifications: state.showDesktopNotifications,
        autoMarkAsRead: state.autoMarkAsRead,
        notificationDuration: state.notificationDuration,
        maxNotifications: state.maxNotifications,
      }),
    }
  )
);
