import { create } from 'zustand';
import { WebLanguageGalaxyEnum } from '@/utils/enums';
import { storageHelper } from '@/utils/forageUtils';

type UserStoreInit = {
  locale: WebLanguageGalaxyEnum;
  isDark: boolean;
  isLoading: boolean;
};

interface IUserStoreOptions extends UserStoreInit {
  setLocale: (locale: UserStoreInit['locale']) => void;
  setIsDark: (isDark: UserStoreInit['isDark']) => void;
  setIsLoading: (isLoading: UserStoreInit['isLoading']) => void;
}

const userStoreInit: UserStoreInit = {
  locale:
    storageHelper<WebLanguageGalaxyEnum>('locale').getItem() ??
    WebLanguageGalaxyEnum.En_US,
  isDark: storageHelper<boolean>('theme').getItem() ?? false,
  isLoading: false,
};

const useUserStore = create<IUserStoreOptions>((set) => {
  return {
    ...userStoreInit,
    setLocale: (locale: UserStoreInit['locale']) => {
      storageHelper<string>('locale').setItem(locale);
      set({ locale });
    },
    setIsDark: (isDark: UserStoreInit['isDark']) => {
      storageHelper<boolean>('theme').setItem(isDark);
      set({ isDark });
    },
    setIsLoading: (isLoading: UserStoreInit['isLoading']) => set({ isLoading }),
  };
});

export { useUserStore };
export type { UserStoreInit, IUserStoreOptions };
