import type { StaffsProps } from '@/pages/Management/apis';
import type { ExchangeRateProps } from '@/pages/Management/apis/managementApis';

// React Query keys for centralized cache management
export const queryKeys = {
  // Common queries
  common: {
    currencyCodes: ['currency', 'codes'],
  },

  // Dashboard queries
  dashboard: {
    summary: ['dashboard', 'summary'],
    stats: ['dashboard', 'stats'] as const,
  },

  // Members queries
  members: {
    all: ['members'] as const,
    list: (filters?: any) => ['members', 'list', filters] as const,
    detail: (id: string) => ['members', 'detail', id] as const,
  },

  // Orders queries
  orders: {
    all: ['orders'] as const,
    list: (filters?: any) => ['orders', 'list', filters] as const,
    detail: (id: string) => ['orders', 'detail', id] as const,
    byMember: (memberId: string) => ['orders', 'byMember', memberId] as const,
  },

  // Management queries
  management: {
    staffs: (params?: StaffsProps) => [
      'staffs',
      ...Object.values(params || {}),
    ],
    stats: ['management', 'stats'] as const,

    // Employee queries
    employees: {
      all: ['management', 'employees'] as const,
      list: (filters?: any) =>
        ['management', 'employees', 'list', filters] as const,
      detail: (id: string) =>
        ['management', 'employees', 'detail', id] as const,
    },

    // Cash Register queries
    registers: {
      all: ['management', 'registers'] as const,
      list: (filters?: any) =>
        ['management', 'registers', 'list', filters] as const,
      detail: (id: string) =>
        ['management', 'registers', 'detail', id] as const,
    },

    // Transaction queries
    transactions: {
      all: ['management', 'transactions'] as const,
      list: (filters?: any) =>
        ['management', 'transactions', 'list', filters] as const,
      detail: (id: string) =>
        ['management', 'transactions', 'detail', id] as const,
      byRegister: (registerId: string) =>
        ['management', 'transactions', 'byRegister', registerId] as const,
    },

    // Shift queries
    shifts: {
      all: ['management', 'shifts'] as const,
      list: (filters?: any) =>
        ['management', 'shifts', 'list', filters] as const,
      detail: (id: string) => ['management', 'shifts', 'detail', id] as const,
      active: ['management', 'shifts', 'active'] as const,
    },

    // Store queries
    stores: {
      all: ['management', 'stores'] as const,
      list: (filters?: any) =>
        ['management', 'stores', 'list', filters] as const,
      detail: (id: string) => ['management', 'stores', 'detail', id] as const,
    },

    // Product queries
    products: {
      all: ['management', 'products'] as const,
      list: (filters?: any) =>
        ['management', 'products', 'list', filters] as const,
      detail: (id: string) => ['management', 'products', 'detail', id] as const,
      byStore: (storeId: string) =>
        ['management', 'products', 'byStore', storeId] as const,
      lowStock: ['management', 'products', 'lowStock'] as const,
    },

    // ExchangeRates queries
    rates: {
      all: (filters?: ExchangeRateProps) =>
        ['management', 'rates', filters] as const,
    },
  },

  // Roles and Permissions queries
  roles: {
    all: ['roles'] as const,
    list: (filters?: any) => ['roles', 'list', filters] as const,
    detail: (id: string) => ['roles', 'detail', id] as const,
  },

  permissions: {
    all: ['permissions'] as const,
    categories: ['permissions', 'categories'] as const,
  },

  userRoles: {
    all: ['userRoles'] as const,
    list: (filters?: any) => ['userRoles', 'list', filters] as const,
  },
} as const;
