export default {
  geneSumMbie: '產生摘要',
  generateTransactionSummary: '產生交易摘要',
  confiPara: '設定參數',
  cancel: '取消',
  generate: '產生',
  configureSummaryParameters: '設定摘要產生參數',
  generateSummary: '產生摘要',
  summaryPeriod: '摘要期間',
  dateRangeRequired: '請選擇日期範圍',
  start: '開始日期',
  end: '結束日期',
  startDate: '開始日期',
  endDate: '結束日期',
  breakdowns: '分類明細',
  includeBreakdowns: '包含分類明細',
  byType: '依類型',
  byTransactionTypeDescription: '依銷售、退款、作廢等分類',
  byTransactionType: '依交易類型',
  byPayment: '依付款方式',
  byPaymentMethod: '依付款方式',
  byPaymentMethodDescription: '依現金、信用卡、電子錢包等分類',
  byEmployee: '依員工',
  byRegister: '依收銀機',
  byEmployeeDescription: '依個別員工分類',
  byRegisterMb: '依收銀機',
  byRegisterDescription: '依收銀設備分類',
  filterOptions: '篩選選項',
  minimumAmount: '最小金額',
  selectMinimumAmount: '選擇最小金額',
  noMinimum: '無最小限制',
  includeTransactionTypes: '包含交易類型',
  selectTransactionTypes: '選擇交易類型',
  sale: '銷售',
  refund: '退款',
  void: '作廢',
  cash_in: '現金收入',
  cash_out: '現金支出',
};
