export default {
  save: '儲存',
  cancel: '取消',
  enterTrans: '輸入交易描述',
  desRq: '描述為必填項',
  description: '描述',
  enterAmount: '輸入金額',
  amountRq: '金額為必填項',
  amount: '金額',
  sale: '銷售',
  refund: '退款',
  void: '作廢',
  cash_in: '現金收入',
  cash_out: '現金支出',
  transactionType: '類型',
  selectE: '選擇員工',
  employee: '員工',
  selectRegister: '選擇收銀機',
  registerRq: '收銀機為必填項',
  register: '收銀機',
  addTransaction: '新增交易',
  closeRegister: '關閉收銀機',
  enterActual: '輸入實際結餘',
  closeBalnceRq: '結帳餘額為必填項',
  closingBalance: '結帳餘額',
  openRegister: '開啟收銀機',
  enterOpenB: '輸入開機餘額',
  openBalanceRq: '開機餘額為必填項',
  openingBalance: '開機餘額',
  eRequired: '員工為必填項',
  shifts: '班次',
  items: '項目',
  of: '之',
  filByType: '依類型篩選',
  transactions: '交易紀錄',
  open: '開啟',
  closed: '已關閉',
  maintenance: '維護中',
  filByStt: '依狀態篩選',
  registers: '收銀機',
  endShift: '結束班次',
  actions: '操作',
  active: '啟用中',
  complete: '已完成',
  status: '狀態',
  totalSales: '總銷售額',
  shiftEnd: '班次結束',
  shiftStart: '班次開始',
  shiftId: '班次編號',
  timestamp: '時間戳記',
  receiptNumber: '收據號碼',
  openedAt: '開啟時間',
  closedAt: '關閉時間',
  currentBalance: '目前餘額',
  registerName: '收銀機名稱',
  sureEndShift: '確定要結束此班次嗎？',
  start: '開始',
  end: '結束',
  startDate: '開始日期',
  endDate: '結束日期',
  selectType: '選擇類型',
  currBalance: '目前餘額：',
};
