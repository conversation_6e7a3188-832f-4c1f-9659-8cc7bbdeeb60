export default {
  title: '訂單管理',
  description: '追蹤與管理賭場交易與付款',
  refresh: '刷新',
  totalOrders: '總訂單數',
  completed: '已完成',
  pending: '待處理',
  revenue: '收入',
  searchOrders: '搜尋訂單',
  searchDescription: '透過會員姓名或會員ID查詢訂單',
  statusPlaceholder: '全部狀態',
  searchPlaceholder: '輸入會員姓名或會員ID搜尋...',
  type: '類型',
  orderDetails: '訂單詳情',
  amount: '金額',
  status: '狀態',
  paymentStatus: '付款狀態',
  date: '日期',
  qrCode: 'QR碼',
  actions: '操作',
  transaction: '交易',
  processing: '處理中',
  failed: '失敗',
  cancelled: '已取消',
  view: '檢視',
  edit: '編輯',
  review: '審核',
  of: '共',
  items: '項',

  // Column headers
  type: '類型',
  orderDetails: '訂單詳情',
  amount: '金額',
  status: '狀態',
  quantity: '數量',
  date: '日期',
  staffId: '員工ID',
  actions: '操作',

  // Status options
  pending: '待處理',
  confirmed: '已確認',
  processing: '處理中',
  completed: '已完成',
  cancelled: '已取消',
  rejected: '已拒絕',

  // Other text
  user: '用戶',
  counter: '櫃檯',
};
