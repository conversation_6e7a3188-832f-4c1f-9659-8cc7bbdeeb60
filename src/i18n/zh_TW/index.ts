import {
  dropdownLocale,
  notificationDropdown,
  notificationItem,
  header,
  mainLayout,
  sidebar,
  orderStatusTag,
  dayPicker,
  errorBoundary,
  galaxyTable,
  loadingPage,
} from './components';
import {
  dashboard,
  newMembers,
  recentOrders,
  login,
  memberForm,
  memberDetailsModal,
  members,
  notFound,
  orderDetailsModal,
  orders,
  orderReview,
  profileSettings,
  securitySettings,
  profile,
  backupSettings,
  generalSettings,
  notificationSettings,
  systemSettings,
  settings,
  accounting,
  financialStatements,
  reconciliationReport,
  transactionSummaries,
  generateReportModal,
  generateStatementModal,
  generateSummaryModal,
  management,
  employeeDetailsModal,
  frontDeskManagement,
  storesManagement,
  addCounterModal,
  counterFilter,
  counterDetailsModal,
  counterManagement,
  useCounterColumns,
  editRateModal,
  rateManagement,
  rateSettingsModal,
  useRateManagementColumns,
  attendanceModal,
  editEmployeeModal,
  addStaffModal,
  staffFilters,
  staffManagement,
  useStaffManagementColumns,
  employeesManagement,
  rolePermissionManagement,
  permissionsList,
  rolesList,
  userRolesList,
  assignRoleModal,
  createRoleModal,
  editRoleModal,
} from './pages';
import { options } from './utils';

export default {
  dropdownLocale,
  notificationDropdown,
  notificationItem,
  errorBoundary,
  header,
  mainLayout,
  sidebar,
  orderStatusTag,
  dayPicker,
  galaxyTable,
  loadingPage,
  dashboard,
  newMembers,
  recentOrders,
  login,
  memberForm,
  memberDetailsModal,
  members,
  notFound,
  orderDetailsModal,
  orders,
  orderReview,
  profileSettings,
  securitySettings,
  options,
  profile,
  backupSettings,
  generalSettings,
  notificationSettings,
  systemSettings,
  settings,

  accounting,
  financialStatements,
  reconciliationReport,
  transactionSummaries,
  generateReportModal,
  generateStatementModal,
  generateSummaryModal,
  management,
  employeeDetailsModal,
  frontDeskManagement,
  storesManagement,
  addCounterModal,
  counterFilter,
  counterDetailsModal,
  counterManagement,
  useCounterColumns,
  editRateModal,
  rateManagement,
  rateSettingsModal,
  useRateManagementColumns,
  attendanceModal,
  editEmployeeModal,

  addStaffModal,
  staffFilters,
  staffManagement,
  useStaffManagementColumns,
  employeesManagement,
  rolePermissionManagement,
  permissionsList,
  rolesList,
  userRolesList,
  assignRoleModal,
  createRoleModal,
  editRoleModal,
};
