import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import en_US from './en_US';
import zh_TW from './zh_TW';
import { WebLanguageGalaxyEnum } from '@/utils/enums';
import { storageHelper } from '@/utils/forageUtils';

const resources = {
  'en-US': en_US,
  'zh-TW': zh_TW,
};

i18n.use(initReactI18next).init({
  resources,
  lng:
    storageHelper<WebLanguageGalaxyEnum>('locale').getItem() ??
    WebLanguageGalaxyEnum.En_US,
  fallbackLng: 'en-US',
  debug: false,
  interpolation: {
    escapeValue: false,
  },
});
export default { i18n, resources };
