export default {
  saveSuccess: 'Backup settings saved successfully',
  saveError: 'Failed to save backup settings',
  backupSuccess: 'Backup completed successfully',
  backupError: 'Backup failed',
  deleteBackup: 'Delete Backup',
  deleteBackupConfirm: 'Delete Backup',
  backupDeleted: 'Backup deleted successfully',
  backupStatus: 'Backup Status',
  totalBackups: 'Total Backups',
  lastBackupSize: 'Last Backup Size',
  successRate: 'Success Rate',
  createBackup: 'Create Backup',
  automaticBackup: 'Automatic Backup',
  enableAutoBackup: 'Enable Auto Backup',
  enableAutoBackupDesc: 'Automatically backup the system on a schedule',
  frequencyLabel: 'Frequency',
  hourly: 'Hourly',
  daily: 'Daily',
  weekly: 'Weekly',
  monthly: 'Monthly',
  backupTimeLabel: 'Backup Time',
  retentionDaysLabel: 'Retention Days',
  days: 'Days',
  backupContent: 'Backup Content',
  includeDatabase: 'Include Database',
  includeDatabaseDesc: 'Include database in backup',
  includeFiles: 'Include Files',
  includeFilesDesc: 'Include system files in backup',
  includeSettings: 'Include Settings',
  includeSettingsDesc: 'Include system settings in backup',
  cloudBackup: 'Cloud Backup',
  enableCloudBackup: 'Enable Cloud Backup',
  enableCloudBackupDesc: 'Store backups in the cloud',
  cloudProvider: 'Cloud Provider',
  cloudWarning: 'Cloud backup is experimental',
  cloudWarningDesc: 'Ensure proper cloud credentials are set before enabling',
  cancel: 'Cancel',
  saveChanges: 'Save Changes',
  backupHistory: 'Backup History',
  type: 'Type',
  status: 'Status',
  date: 'Date',
  size: 'Size',
  duration: 'Duration',
  actions: 'Actions',
  auto: 'Auto',
  manual: 'Manual',
  completed: 'Completed',
  failed: 'Failed',
};
