export default {
  saveSucces: 'Settings saved successfully',
  saveError: 'Failed to save settings',
  resetSuccess: 'Settings reset to defaults',
  siteInfo: 'Site Information',
  siteNameLabel: 'Site Name',
  siteNameError: 'Site Name is required',
  siteDescriptionLabel: 'Site Description',
  regionalSettings: 'Regional Settings',
  timezoneLabel: 'Timezone',
  timezoneError: 'Timezone is required',
  dateFormatLabel: 'Date Format',
  dateFormatError: 'Date Format is required',
  timeFormatLabel: 'Time Format',
  timeFormatError: 'Time Format is required',
  hour: 'Hour',
  currencySettings: 'Currency Settings',
  currencyLabel: 'Currency',
  currencyError: 'Currency is required',
  currencySymbolLabel: 'Currency Symbol',
  currencySymbolError: 'Currency Symbol is required',
  decimalPlacesLabel: 'Decimal Places',
  decimalPlacesError: 'Decimal Places is required',
  businessHours: 'Business Hours',
  openingTimeLabel: 'Opening Time',
  openingTimeError: 'Opening Time is required',
  closingTimeLabel: 'Closing Time',
  closingTimeError: 'Closing Time is required',
  systemOptions: 'System Options',
  maintenanceMode: 'Maintenance Mode',
  maintenanceModeDesc: 'Put the system in maintenance mode',
  debugMode: 'Debug Mode',
  debugModeDesc: 'Enable detailed error logging',
  autoSave: 'Auto Save',
  autoSaveDesc: 'Automatically save changes every x minutes',
  autoSaveIntervalLabel: 'Auto Save Interval',
  minutes: 'Minutes',
  reset: 'Reset',
  cancel: 'Cancel',
  saveChanges: 'Save Changes',
};
