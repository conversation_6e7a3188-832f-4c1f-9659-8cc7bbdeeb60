export default {
  saveSuccess: 'Notification settings saved successfully',
  saveError: 'Failed to save notification settings',
  newOrders: 'New Orders',
  newOrdersDesc: 'Notify when new orders are placed',
  memberRegistrations: 'Member Registrations',
  memberRegistrationsDesc: 'Notify when new members register',
  systemAlerts: 'System Alerts',
  systemAlertsDesc: 'Important system status updates',
  securityAlerts: 'Security Alerts',
  securityAlertsDesc: 'Security-related notifications',
  paymentAlerts: 'Payment Alerts',
  paymentAlertsDesc: 'Payment processing notifications',
  employeeUpdates: 'Employee Updates',
  employeeUpdatesDesc: 'Employee status changes',
  maintenanceAlerts: 'Maintenance Alerts',
  maintenanceAlertsDesc: 'System maintenance notifications',
  reportGeneration: 'Report Generation',
  reportGenerationDesc: 'When reports are generated',
  deliveryMethods: 'Delivery Methods',
  email: 'Email',
  emailDesc: 'Receive notifications via email',
  sms: 'SMS',
  smsDesc: 'Receive notifications via SMS"',
  push: 'Push',
  pushDesc: 'Receive notifications via browser/app',
  soundSettings: 'Sound Settings',
  soundEnabled: 'Sound Notifications',
  soundEnabledDesc: 'Play sound when notifications arrive',
  quietHours: 'Quite Hours Start',
  quietHoursDesc: 'Do not disturb during these hours',
  quietStart: 'Quite Hours Start',
  quietEnd: 'Quite Hours End',
  notificationTypes: 'Notification Types',
  frequency: 'Frequency',
  immediate: 'Immediate',
  immediateDesc: 'Receive notifications immediately',
  daily: 'Daily Digest',
  dailyDesc: 'Receive a daily summary of notifications',
  weekly: 'Weekly Summary',
  weeklyDesc: 'Receive a weekly summary of notifications',
  cancel: 'Cancel',
  saveChanges: 'Save Changes',
};
