export default {
  saveSuccess: 'System settings saved successfully',
  saveError: 'Failed to save system settings',
  restartWarning: 'System restart required for changes to take effect',
  systemStatus: 'System Status',
  cpuUsage: 'CPU Usage',
  memoryUsage: 'Memory Usage',
  diskUsage: 'Disk Usage',
  networkUsage: 'Network Usage',
  uptime: 'Uptime',
  activeConnections: 'Active Connections',
  errorRate: 'Error Rate',
  serverConfig: 'Server Config',
  serverName: 'Server Name',
  serverNameError: 'Server Name is required',
  maxConnections: 'Max Connections',
  maxConnectionsError: 'Max Connections is required',
  connectionTimeout: 'Connection Timeout',
  connectionTimeoutError: 'Connection Timeout is required',
  seconds: 'Seconds',
  sslPort: 'SSL Port',
  loggingConfig: 'Loggin Configuration',
  enableLogging: 'Enable Logging',
  enableLoggingDesc: 'Record system activity for debugging and auditing',
  logLevel: 'Log Level',
  performanceConfig: 'Performance Configuration',
  enableCaching: 'Enable Caching',
  enableCachingDesc: 'Store frequently accessed data in memory',
  cacheSize: 'Cache Size',
  enableCompression: 'Enable Compression',
  enableCompressionDesc: 'Reduce data size for faster transfers',
  compressionLevel: 'Compression Level',
  fastest: 'Fasted',
  fast: 'Fast',
  balanced: 'Balanced',
  best: 'Best',
  securityConfig: 'Security Configuration',
  enableSSL: 'Enable SSL',
  enableSSLDesc: 'Encrypt data in transit',
  securityWarning: 'Security Warning',
  securityWarningDesc:
    'Ensure proper SSL certificates are in place before enabling',
  restartServer: 'Restart Server',
  cancel: 'Cancel',
  saveChanges: 'Save Changes',
};
