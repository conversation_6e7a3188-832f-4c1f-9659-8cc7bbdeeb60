export default {
  title: 'Member Management',
  description: 'Manage casino member registrations and profiles',
  addMember: 'Add Member',
  totalMembers: 'Total Members',
  activeMembers: 'Active Members',
  vipMembers: 'VIP Members',
  membershipTierDistribution: 'Membership Tier Distribution',
  membershipDescription: 'Member distribution across different tiers',
  platinum: 'Platinum',
  gold: 'Gold',
  silver: 'Silver',
  bronze: 'Bronze',
  searchMembers: 'Search Members',
  searchDescription: 'Find members by name or passport',
  searchPlaceholder: 'Search by name or passport...',
  member: 'Member',
  passport: 'Passport',
  membership: 'Membership',
  registrationDate: 'Registration Date',
  status: 'Status',
  actions: 'Actions',
  id: 'ID',
  vip: 'VIP',
  orders: 'Orders',
  active: 'Active',
  inactive: 'Inactive',
  view: 'View',
  of: 'of',
  items: 'items',
};
