export default {
  registerMember: 'Register Member',
  description: 'Add a new member to the casino system',
  nameLabel: 'Name',
  nameError: 'Name is required',
  nameLengthError: 'Name must be at least 2 characters',
  namePlaceholder: 'Enter Member Name',
  passportLabel: 'Passport',
  passportError: 'Passport is required',
  passportLengthError: 'Passport must be at least 5 characters',
  passportPlaceholder: 'Enter Passport Number',
  cancel: 'Cancel',
  registering: 'Registering...',
  register: 'Register',
};
