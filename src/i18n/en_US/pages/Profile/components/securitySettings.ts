export default {
  changePassword: 'Change Password',
  passwordRequirements: 'For security reasons, please use a strong password',
  passwordRequirementsDesc:
    'Password must be at least 8 characters long and contain both uppercase and lowercase letters.',
  currentPasswordLabel: 'Current Password',
  currentPasswordError: 'Current Password is required',
  currentPasswordPlaceholder: 'Enter Current Password',
  newPasswordLabel: 'New Password',
  newPasswordError: 'New Password is required',
  newPasswordLengthError:
    'Password must be at least 8 characters long and contain both uppercase and lowercase letters.',
  newPasswordPlaceholder: 'Enter New Password',
  confirmPasswordLabel: 'Confirm Password',
  confirmPasswordError: 'Confirm Password is required',
  passwordMismatch: 'Passwords do not match',
  confirmPasswordPlaceholder: 'Confirm Your New Password',
  changePasswordSuccessDesc: 'Password changed successfully',
  twoFactorAuth: 'Two-Factor Authentication',
  enable2FA: 'Enable Two-Factor Authentication',
  enable2FADesc: 'Require a verification code at login',
  authenticatorApp: 'Authenticator App',
  authenticatorAppDesc: 'Scan the QR code with your authenticator app',
  setupAuthenticator: 'Setup Authenticator',
  sessionManagement: 'Session Management',
  sessionTimeout: 'Session Timeout',
  sessionTimeoutError: 'Session Timeout is required',
  minutes: 'Minutes',
  hour: 'Hour',
  hours: 'Hours',
  passwordExpiryLabel: 'Password Expiry',
  passwordExpiryError: 'Password Expiry is required',
  days: 'Days',
  year: 'Year',
  loginNotifications: 'Login Notifications',
  loginNotificationsDesc: 'Notify on new login attempts',
  activeSessions: 'Active Sessions',
  activeSessionsDesc: 'Manage and terminate active user sessions',
  manageSessions: 'Manage Sessions',
  ipWhitelist: 'IP Whitelist',
  enableIPWhitelist: 'Enable IP Whitelist',
  enableIPWhitelistDesc: 'Only allow logins from specific IPs',
  allowedIPsLabel: 'Allowed IP Addresses',
  allowedIPsPlaceholder: 'Enter IP Address',
  cancel: 'Cancel',
  saveChanges: 'Save Changes',
};
