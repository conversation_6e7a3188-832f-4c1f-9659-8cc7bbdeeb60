export default {
  counterWithId: 'Counter With ID',
  editedSuccessfully: 'Edited Successfully',
  noChangeDescription: 'No changes detected',
  counterManagement: 'Counter Management',
  counterDetails: 'Counter Details',
  counterName: 'Counter Name',
  location: 'Location',
  noLocation: 'No Location',
  createTime: 'Create Time',
  updateTime: 'Update Time',
  paymentStatus: 'Payment Status',
  unavailableNow: 'Unavailable Now',
  actions: 'Actions',
  edit: 'Edit',
  notes: 'Notes',
  noDescription: 'No Description',
  counterNameLabel: 'Counter Name',
  counterNameRequired: 'Please Enter Counter Name',
  counterNamePlaceholder: 'Enter Counter Name',
  locationLabel: 'Location',
  locationRequired: 'Please Enter Location',
  locationPlaceholder: 'Enter Location',
  statusLabel: 'Status',
  statusRequired: 'Please select status',
  statusPlaceholder: 'Select status',
  descriptionLabel: 'Description (Optional)',
  descriptionPlaceholder: 'Enter some description',
  submit: 'Submit',
  cancel: 'Cancel',
};
