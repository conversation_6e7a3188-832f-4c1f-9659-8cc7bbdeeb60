export default {
  view: 'View Details',
  edit: 'Edit Store',
  activate: 'Activate',
  deactivate: 'Deactivate',
  maintenanceSet: 'Set Maintenance',
  storeName: 'Store Name',
  manager: 'Manager',
  status: 'Status',
  active: 'Active',
  inactive: 'Inactive',
  maintenance: 'Maintenance',
  employeeCount: 'Employee Count',
  monthlyRevenue: 'Monthly Revenue',
  totalRevenue: 'Total Revenue',
  productName: 'Product Name',
  store: 'Store',
  stores: 'Stores',
  price: 'Price',
  stockLevel: 'Stock Level',
  minStockLevel: 'Min Stock Level',
  lastRestocked: 'Last Restocked',
  updateStock: 'Update Stock',
  searchStore: 'Search stores...',
  filByStt: 'Filter by status',
  addStore: 'Add New Store',
  inventory: 'Inventory',
  searchPct: 'Search products...',
  filByCate: 'Filter by category',
  gaming: 'Gaming',
  beve: 'Beverages',
  food: 'Food',
  merchant: 'Merchandise',
  lowStockAlert: 'Low Stock Alert',
  messAl: 'Low Stock Items',
  desAl:
    'Showing products that are below minimum stock level and need restocking.',
  storeNameRq: 'Store name is required',
  enterStName: 'Enter store name',
  loca: 'Location',
  ltRequire: 'Location is required',
  enterLoca: 'Enter location',
  address: 'Address',
  addRequired: 'Address is required',
  enterFullAdd: 'Enter full address',
  phone: 'Phone',
  phoneRq: 'Phone is required',
  enterPhone: 'Enter phone number',
  mail: 'Email',
  mailRq: 'Email is required',
  invaMail: 'Invalid email format',
  enterMail: 'Enter email address',
  managerRq: 'Manager is required',
  selectMananger: 'Select Manager',
  openingHours: 'Opening Hours',
  monday: 'Monday',
  tuesday: 'Tuesday',
  wednesday: 'Wednesday',
  thursday: 'Thursday',
  friday: 'Friday',
  saturday: 'Saturday',
  sunday: 'Sunday',
  cancel: 'Cancel',
  save: 'Save',
  currStock: 'Current Stock:',
  minStock: 'Minium Stock:',
  newStockLv: 'New Stock Level',
  stockRq: 'New stock level is required',
  messStock: 'Stock level must be positive',
  enterStock: 'Enter new stock level',
};
