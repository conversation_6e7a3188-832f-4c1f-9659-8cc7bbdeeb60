export default {
  save: 'Save',
  cancel: 'Cancel',
  enterTrans: 'Enter transaction description',
  desRq: 'Description is required',
  description: 'Description',
  enterAmount: 'Enter amount',
  amountRq: 'Amount is required',
  amount: 'Amount',
  sale: 'Sale',
  refund: 'Refund',
  void: 'Void',
  cash_in: 'Cash In',
  cash_out: 'Cash Out',
  transactionType: 'Type',
  selectE: 'Select employee',
  employee: 'Employee',
  selectRegister: 'Select register',
  registerRq: 'Register is required',
  register: 'Register',
  addTransaction: 'Add Transaction',
  closeRegister: 'Close Register',
  enterActual: 'Enter actual closing balance',
  closeBalnceRq: 'Closing balance is required',
  closingBalance: 'Closing Balance',
  openRegister: 'Open Register',
  enterOpenB: 'Enter opening balance',
  openBalanceRq: 'Opening balance is required',
  openingBalance: 'Opening Balance',
  eRequired: 'Employee is required',
  shifts: 'Shifts',
  items: 'items',
  of: 'of',
  filByType: 'Filter by type',
  transactions: 'Transactions',
  open: 'Open',
  closed: 'Closed',
  maintenance: 'Maintenance',
  filByStt: 'Filter by status',
  registers: 'Cash Registers',
  endShift: 'End Shift',
  actions: 'Actions',
  active: 'Active',
  complete: 'Complete',
  status: 'Status',
  totalSales: 'Total Sales',
  shiftEnd: 'Shift End',
  shiftStart: 'Shift Start',
  shiftId: 'Shift ID',
  timestamp: 'Timestamp',
  receiptNumber: 'Receipt #',
  openedAt: 'Opened At',
  closedAt: 'Closed At',
  currentBalance: 'Current Balance',
  registerName: 'Register Name',
  sureEndShift: 'Are you sure you want to end this shift?',
  start: 'Start',
  end: 'End',
  startDate: 'Start Date',
  endDate: 'End Date',
  selectType: 'Select type',
  currBalance: 'Current Balance:',
};
