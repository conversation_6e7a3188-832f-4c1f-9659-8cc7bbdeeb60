export default {
  edit: 'Edit',
  attendance: 'Attendance',
  view: 'View',
  activate: 'Activate',
  deactivate: 'Deactivate',
  suspend: 'Suspend',
  name: 'Name',
  role: 'Role',
  department: 'Department',
  management: 'Management',
  gaming: 'Gaming',
  security: 'Security',
  maintenance: 'Maintenance',
  customer_service: 'Customer Service',
  status: 'Status',
  active: 'Active',
  inactive: 'Inactive',
  suspended: 'Suspended',
  performance: 'Performance',
  salary: 'Salary',
  hireDate: 'Hire Date',
  actions: 'Actions',
  sreachE: 'Search employees...',
  rolePlc: 'Role',
  admin: 'Admin',
  manager: 'Manager',
  cashier: 'Cashier',
  pclStt: 'Status',
  addEmployee: 'Add New Employee',
  emStatis: 'Employee Statistics',
  perMetric: 'Performance metrics and analytics',
  totalEmployees: 'Total Employees',
  perActive: '% active',
  avgPerformance: 'Avg Performance',
  hight: 'high',
  highPerformers: 'high performers',
  avgAttendance: 'Avg Attendance',
  excellentAttendance: 'excellent attendance',
  excellent: 'excellent',
  totalSalary: 'Total Salary',
  avg: 'Avg',
  departmentDistribution: 'Department Distribution',
  roleDistribution: 'Role Distribution',
  statusOverview: 'Employee Status Overview',
  activeEmployees: 'Active Employees',
  perTotal: '% of total',
  inactiveEmployees: 'Inactive Employees',
  suspendedEmployees: 'Suspended Employees',
  nameRq: 'Name is required',
  namePlc: 'Enter employee name',
  email: 'Email',
  mailRq: 'Email is required',
  invMail: 'Invalid email format',
  enterMail: 'Enter email address',
  phoneRq: 'Phone is required',
  enterPhone: 'Enter phone number',
  phone: 'Phone',
  salaryRq: 'Salary is required',
  enterSalary: 'Enter salary',
  roleRq: 'Role is required',
  roleSelect: 'Select role',
  depaRq: 'Department is required',
  depaSelect: 'Select department',
  permissions: 'Permissions',
  perRq: 'At least one permission is required',
  perSelect: 'Select permissions',
  allPer: 'All Permissions',
  manageE: 'Manage Employees',
  viewR: 'Manage Shifts',
  manageShift: 'Manage Shifts',
  operateRes: 'Operate Register',
  processTrans: 'Process Transactions',
  minitorPre: 'Monitor Premises',
  equiMain: 'Equipment Maintenance',
  facilityR: 'Facility Repairs',
  save: 'Save',
  cancel: 'Cancel',
};
