export default {
  createRole: 'Create Role',
  cancel: 'Cancel',
  createMb: 'Create',
  basicInformation: 'Basic Information',
  basicIf: 'Basic Info',
  roleName: 'Role Name',
  roleNameRequired: 'Role name is required',
  roleNameMinLength: 'Role name must be at least 2 characters',
  roleNameMaxLength: 'Role name cannot exceed 50 characters',
  roleNamePlaceholder: 'Enter role name',
  roleDescriptionRequired: 'Role description is required',
  roleDescriptionMaxLength: 'Description cannot exceed 200 characters',
  roleDescriptionPlaceholder: 'Enter role description',
  roleDescription: 'Role Description',
  selectPermissions: 'Select Permissions',
  perMb: 'Permissions',
  selected: 'Selected',
  selectPermissionsDescription:
    'Choose the permissions that users with this role will have',
  permissions: 'Permissions',
  perms: 'perms',
};
