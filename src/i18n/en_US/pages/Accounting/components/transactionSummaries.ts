export default {
  period: 'Period',
  totalTransactions: 'Total Transactions',
  totalAmount: 'Total Amount',
  averageTransaction: 'Average Transaction',
  generatedAt: 'Generated At',
  actions: 'Actions',
  view: 'View',
  download: 'Download',
  transactionId: 'Transaction ID',
  timestamp: 'Timestamp',
  amount: 'Amount',
  paymentMethod: 'Payment Method',
  employee: 'Employee',
  register: 'Register',
  description: 'Description',
  reference: 'Reference',
  byTransactionType: 'By Transaction Type',
  byPaymentMethod: 'By Payment Method',
  byEmployee: 'By Employee',
  byRegister: 'By Register',
  transactionSummary: 'Transaction Summary',
  transaction: 'Transactions',
  total: 'Total',
  average: 'Average',
  rate: 'Rate',
  transactionRate: 'Transaction Rate',
  breakdown: 'Breakdown',
  detailedBreakdown: 'Detailed Breakdown',
  startDate: 'Start Date',
  endDate: 'End Date',
  generateSummary: 'Generate Summary',
  summaryDetails: 'Summary Details',
  close: 'Close',
  transactionSummaries: 'Transaction Summaries',
  summaries: 'summaries',
  viewTransactionDetails: 'View Transaction Details',
  details: 'Details',
  of: 'of',
  transactions: 'Transactions',
  cash: 'Cash',
  card: 'Credit Card',
  digital_wallet: 'Digital Wallet',
  crypto: 'Cryptocurrency',
  noTransactionsFound: 'No transactions found for this type',
  sale: 'Sale',
  refund: 'Refund',
  void: 'Void',
  cash_in: 'Cash In',
  cash_out: 'Cash Out',
  perOfTotal: ' % of total',
  avg: 'Avg:',
};
