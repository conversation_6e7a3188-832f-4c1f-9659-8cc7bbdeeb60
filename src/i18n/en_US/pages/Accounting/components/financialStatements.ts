export default {
  statementType: 'Statement Type',
  income_statement: 'Income Statement',
  balance_sheet: 'Balance Sheet',
  cash_flow: 'Cash Flow Statement',
  trial_balance: 'Trial Balance',
  period: 'Period',
  daily: 'Daily',
  weekly: 'Weekly',
  monthly: 'Monthly',
  quarterly: 'Quarterly',
  yearly: 'Yearly',
  revenue: 'Revenue',
  expenses: 'Expenses',
  netIncome: 'Net Income',
  generatedAt: 'Generated At',
  actions: 'Actions',
  view: 'View',
  gaming: 'Gaming',
  food: 'Food',
  beverages: 'Beverages',
  other: 'Other',
  totalRevenue: 'Total Revenue',
  utilities: 'Utilities',
  supplies: 'Supplies',
  maintenance: 'Maintenance',
  totalExpenses: 'Total Expenses',
  profitMargin: 'Profit Margin',
  assets: 'Assets',
  cash: 'Cash',
  inventory: 'Inventory',
  equipment: 'Equipment',
  totalAssets: 'Total Assets',
  liabilities: 'Liabilities',
  accountsPayable: 'Accounts Payable',
  loans: 'Loans',
  totalLiabilities: 'Total Liabilities',
  equity: 'Equity',
  capital: 'Capital',
  retainedEarnings: 'Retained Earnings',
  totalEquity: 'Total Equity',
  incomeStatement: 'Income Statement',
  selectStatementType: 'Select Statement Type',
  income: 'Income',
  balance: 'Balance',
  balanceSheet: 'Balance Sheet',
  cashFlowOG: 'Cash Flow',
  cashFlow: 'Cash Flow Statement',
  trial: 'Trial',
  trialBalance: 'Trial Balance',
  selectPeriod: 'Select Period',
  generate: 'Generate',
  generateStatement: 'Generate Statement',
  statementDetails: 'Statement Details',
  close: 'Close',
  financialStatements: 'Financial Statements',
  dowload: 'Download',
  print: 'Print',
  of: 'of',
  statements: 'statements',
};
