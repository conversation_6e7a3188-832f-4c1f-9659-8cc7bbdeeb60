export default {
  geneSumMbie: 'Generate Summary',
  generateTransactionSummary: 'Generate Transaction Summary',
  confiPara: 'Configure parameters',
  cancel: 'Cancel',
  generate: 'Generate',
  configureSummaryParameters: 'Configure summary generation parameters',
  generateSummary: 'Generate Summary',
  summaryPeriod: 'Summary Period',
  dateRangeRequired: 'Date range is required',
  start: 'Start Date',
  end: 'End Date',
  startDate: 'Start Date',
  endDate: 'End Date',
  breakdowns: 'Breakdowns',
  includeBreakdowns: 'Include Breakdowns',
  byType: 'By Type',
  byTransactionTypeDescription: 'Break down by sale, refund, void, etc.',
  byTransactionType: 'By Transaction Type',
  byPayment: 'By Payment',
  byPaymentMethod: 'By Payment Method',
  byPaymentMethodDescription: 'Break down by cash, card, digital wallet, etc.',
  byEmployee: 'By Employee',
  byRegister: 'By Register',
  byEmployeeDescription: 'Break down by individual employees',
  byRegisterMb: 'By Register',
  byRegisterDescription: 'Break down by cash registers',
  filterOptions: 'Filter Options',
  minimumAmount: 'Minimum Amount',
  selectMinimumAmount: 'Select minimum amount',
  noMinimum: 'No minimum',
  includeTransactionTypes: 'Include Transaction Types',
  selectTransactionTypes: 'Select transaction types',
  sale: 'Sale',
  refund: 'Refund',
  void: 'Void',
  cash_in: 'Cash In',
  cash_out: 'Cash Out',
};
