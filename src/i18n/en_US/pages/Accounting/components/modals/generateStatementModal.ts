export default {
  geneStatement: 'Generate Statement',
  generateFinancialStatement: 'Generate Financial Statement',
  configurePara: 'Configure parameters',
  configureStatementParameters: 'Configure statement generation parameters',
  cancel: 'Cancel',
  generate: 'Generate',
  generateStatement: 'Generate Statement',
  statementType: 'Statement Type',
  statementTypeRequired: 'Statement type is required',
  selectStatementType: 'Select Statement Type',
  income: 'Income',
  incomeStatement: 'Income Statement',
  balance: 'Balance',
  balanceSheet: 'Balance Sheet',
  cashFlowMb: 'Cash Flow',
  cashFlow: 'Cash Flow Statement',
  trial: 'Trial',
  trialBalance: 'Trial Balance',
  periodType: 'Period Type',
  daily: 'Daily',
  weekly: 'Weekly',
  monthly: 'Monthly',
  quarterly: 'Quarterly',
  yearly: 'Yearly',
  reportingPeriod: 'Reporting Period',
  dateRangeRequired: 'Date range is required',
  startDate: 'Start Date',
  endDate: 'End Date',
  start: 'Start Date',
  end: 'End Date',
  options: 'Options',
  additionalOptions: 'Additional Options',
  incluComparisons: 'Include Comparisons',
  includeComparisons: 'Include Period Comparisons',
  includeComparisonsDescription: 'Compare with previous period data',
  includesNoteMb: 'Include Notes',
  includeNotes: 'Include Explanatory Notes',
  includeNotesDescription: 'Add detailed explanations for major variances',
  detailedBreak: 'Detailed Breakdown',
  detailedBreakdownDescription: 'Include detailed category breakdowns',
  detailedBreakdown: 'Detailed Breakdown',
};
