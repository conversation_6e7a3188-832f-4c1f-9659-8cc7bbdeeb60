export default {
  // Page Title and Navigation
  title: 'Order Review',
  description: 'Review and verify order details before approval',
  backToOrders: 'Back to Orders',
  
  // Order Information
  orderInformation: 'Order Information',
  orderInformationDescription: 'Complete order details and transaction information',
  orderId: 'Order ID',
  orderCode: 'Order Code',
  uxmOrderId: 'UXM Order ID',
  orderType: 'Order Type',
  status: 'Status',
  currency: 'Currency',
  quantity: 'Quantity',
  pricePerUnit: 'Price per Unit',
  totalAmount: 'Total Amount',
  createdAt: 'Created At',
  updatedAt: 'Updated At',
  orderNotes: 'Order Notes',
  
  // Related Information
  userInformation: 'User Information',
  staffInformation: 'Staff Information',
  counterInformation: 'Counter Information',
  userId: 'User ID',
  staffId: 'Staff ID',
  counterId: 'Counter ID',
  
  // Actions
  verifyOrder: 'Verify Order',
  rejectOrder: 'Reject Order',
  verify: 'Verify',
  reject: 'Reject',
  cancel: 'Cancel',
  
  // Status Messages
  orderNotFound: 'Order Not Found',
  orderNotFoundDescription: "The order you're looking for doesn't exist or has been removed.",
  orderReviewNotAvailable: 'Order Review Not Available',
  orderReviewNotAvailableDescription: "This order cannot be reviewed as it's not in pending status.",
  
  // Verify Modal
  verifyModalTitle: 'Verify Order',
  verifyModalDescription: 'Approve this order for processing',
  confirmOrderVerification: 'Confirm Order Verification',
  confirmOrderVerificationDescription: 'Are you sure you want to verify this order? This action will approve the order for processing.',
  adminPassword: 'Admin Password',
  adminPasswordPlaceholder: 'Enter your admin password',
  adminPasswordRequired: 'Please enter your admin password',
  adminPasswordMinLength: 'Password must be at least 6 characters',
  additionalNotes: 'Additional Notes (Optional)',
  additionalNotesPlaceholder: 'Add any verification notes or comments...',
  
  // Reject Modal
  rejectModalTitle: 'Reject Order',
  rejectModalDescription: 'Decline this order with reason',
  confirmOrderRejection: 'Confirm Order Rejection',
  confirmOrderRejectionDescription: 'Are you sure you want to reject this order? This action cannot be undone.',
  reasonForRejection: 'Reason for Rejection *',
  reasonForRejectionPlaceholder: 'Please provide a detailed reason for rejecting this order...',
  reasonRequired: 'Please provide a reason for rejection',
  reasonMinLength: 'Reason must be at least 10 characters',
};
