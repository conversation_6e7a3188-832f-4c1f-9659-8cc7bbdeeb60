export default {
  title: 'Order Management',
  description: 'Track and manage casino transactions and payments',
  refresh: 'Refresh',
  totalOrders: 'Total Orders',
  completed: 'Completed',
  pending: 'Pending',
  revenue: 'Revenue',
  searchOrders: 'Search Orders',
  searchDescription: "Find Orders by Member's name or MemberID",
  statusPlaceholder: 'All Statuses',
  searchPlaceholder: "Search by Member's name or MemberID...",
  type: 'Type',
  orderDetails: 'Order Details',
  amount: 'Amount',
  status: 'Status',
  paymentStatus: 'Paymen Status',
  date: 'Date',
  qrCode: 'QR Code',
  actions: 'Actions',
  transaction: 'Transaction',
  processing: 'Processing',
  failed: 'Failed',
  cancelled: 'Cancelled',
  view: 'View',
  edit: 'Edit',
  review: 'Review',
  of: 'of',
  items: 'items',
};
