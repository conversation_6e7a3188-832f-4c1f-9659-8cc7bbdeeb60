// Notification enums based on the API specification
export enum NotificationMessageKey {
  // Broadcast
  
  // System
  System_Error = 200,

  // Background Job
  Job_OrderCreated = 300,

  // Order module
  Order_CreatedComplete = 400,
  Order_CreatedFail = 401,
  Order_StatusUpdated = 402,
  Order_PaymentReceived = 403,
  Order_Cancelled = 404,

  // User module
  User_Registered = 500,
  User_StatusChanged = 501,
  User_ProfileUpdated = 502,

  // Staff module
  Staff_Added = 600,
  Staff_StatusChanged = 601,
  Staff_RoleUpdated = 602,

  // Counter module
  Counter_Created = 700,
  Counter_StatusChanged = 701,
  Counter_Updated = 702,

  // Security
  Security_LoginAttempt = 800,
  Security_UnauthorizedAccess = 801,
  Security_PasswordChanged = 802,

  // System maintenance
  System_MaintenanceStart = 900,
  System_MaintenanceEnd = 901,
  System_BackupComplete = 902,
  System_BackupFailed = 903,
}

export enum NotificationType {
  System = 0,
  Warning = 1,
  Info = 2,
  Announcement = 3,
  Order = 4,
  User = 5,
  Staff = 6,
  Counter = 7,
}

export enum NotificationSeverity {
  Info = 0,
  Success = 1,
  Warning = 2,
  Error = 3,
}

// Notification payload interface
export interface NotificationPayload {
  notificationId: string;
  messageKey: NotificationMessageKey;
  type: NotificationType;
  severity: NotificationSeverity;
  metadata: Record<string, any>;
  isRead: boolean;
  isBroadcast: boolean;
  createdAt: string;
}

// Extended notification interface for UI
export interface UINotification extends NotificationPayload {
  title?: string;
  description?: string;
  actionUrl?: string;
  actionText?: string;
  autoClose?: boolean;
  duration?: number;
}

// Handler function type
export type NotificationHandler = (notification: NotificationPayload) => void;
