// Notification enums based on the API specification
export enum NotificationMessageKey {
  // Broadcast
  
  // System
  System_Error = 'System_Error',

  // Background Job
  Job_OrderCreated = 'Job_OrderCreated',

  // Order module
  Order_CreatedComplete = 'Order_CreatedComplete',
  Order_CreatedFail = 'Order_CreatedFail',
  Order_StatusUpdated = 'Order_StatusUpdated',
  Order_PaymentReceived = 'Order_PaymentReceived',
  Order_Cancelled = 'Order_Cancelled',

  // User module
  User_Registered = 'User_Registered',
  User_StatusChanged = 'User_StatusChanged',
  User_ProfileUpdated = 'User_ProfileUpdated',

  // Staff module
  Staff_Added = 'Staff_Added',
  Staff_StatusChanged = 'Staff_StatusChanged',
  Staff_RoleUpdated = 'Staff_RoleUpdated',

  // Counter module
  Counter_Created = 'Counter_Created',
  Counter_StatusChanged = 'Counter_StatusChanged',
  Counter_Updated = 'Counter_Updated',

  // Security
  Security_LoginAttempt = 'Security_LoginAttempt',
  Security_UnauthorizedAccess = 'Security_UnauthorizedAccess',
  Security_PasswordChanged = 'Security_PasswordChanged',

  // System maintenance
  System_MaintenanceStart = 'System_MaintenanceStart',
  System_MaintenanceEnd = 'System_MaintenanceEnd',
  System_BackupComplete = 'System_BackupComplete',
  System_BackupFailed = 'System_BackupFailed',
}

export enum NotificationType {
  System = 'System',
  Warning = 'Warning',
  Info = 'Info',
  Announcement = 'Announcement',
  Order = 'Order',
  User = 'User',
  Staff = 'Staff',
  Counter = 'Counter',
}

export enum NotificationSeverity {
  Info = 'Info',
  Success = 'Success',
  Warning = 'Warning',
  Error = 'Error',
}

// Notification payload interface
export interface NotificationPayload {
  notificationId: string;
  messageKey: NotificationMessageKey;
  type: NotificationType;
  severity: NotificationSeverity;
  metadata: Record<string, any>;
  isRead: boolean;
  isBroadcast: boolean;
  createdAt: string;
}

// Extended notification interface for UI
export interface UINotification extends NotificationPayload {
  title?: string;
  description?: string;
  actionUrl?: string;
  actionText?: string;
  autoClose?: boolean;
  duration?: number;
}

// Handler function type
export type NotificationHandler = (notification: NotificationPayload) => void;
