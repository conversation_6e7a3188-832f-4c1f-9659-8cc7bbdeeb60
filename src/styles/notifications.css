/* SignalR Notification Styles */

/* Notification Toast Animations */
.notification-toast {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Notification Panel Styles */
.notification-tabs .ant-tabs-tab {
  padding: 8px 12px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
}

.notification-tabs .ant-tabs-tab-active {
  background: rgba(24, 144, 255, 0.1) !important;
  border-radius: 6px !important;
}

.notification-tabs .ant-tabs-content-holder {
  padding: 0 !important;
}

/* Notification Dropdown Styles */
.notification-dropdown .ant-dropdown {
  border-radius: 12px !important;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(0, 0, 0, 0.06) !important;
  overflow: hidden !important;
  background: white !important;
}

/* Dropdown menu backgrounds */
.ant-dropdown-menu {
  background: white !important;
}

.ant-dropdown-menu-item {
  background: white !important;
}

/* Notification Item Hover Effects */
.notification-item {
  transition: all 0.2s ease !important;
  border-radius: 8px !important;
}

.notification-item:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Unread notification pulse animation */
.notification-unread {
  position: relative;
}

.notification-unread::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.05));
  border-radius: 10px;
  animation: pulse 2s infinite;
  z-index: -1;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.02);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Connection Status Styles */
.connection-status {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.9));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.connection-status.connected {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.05));
  border-color: rgba(34, 197, 94, 0.2);
}

.connection-status.connecting {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.05));
  border-color: rgba(59, 130, 246, 0.2);
}

.connection-status.disconnected {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.05));
  border-color: rgba(239, 68, 68, 0.2);
}

/* Notification Badge Styles */
.notification-badge {
  background: linear-gradient(135deg, #ff4d4f, #ff7875) !important;
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3) !important;
  border: 2px solid white !important;
  font-weight: 600 !important;
  font-size: 10px !important;
}

/* Notification Button Pulse */
.notification-button-pulse {
  animation: buttonPulse 2s infinite;
}

@keyframes buttonPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

/* Severity-based styling */
.notification-severity-info {
  border-left-color: #1890ff !important;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.05), rgba(24, 144, 255, 0.02)) !important;
}

.notification-severity-success {
  border-left-color: #52c41a !important;
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.05), rgba(82, 196, 26, 0.02)) !important;
}

.notification-severity-warning {
  border-left-color: #faad14 !important;
  background: linear-gradient(135deg, rgba(250, 173, 20, 0.05), rgba(250, 173, 20, 0.02)) !important;
}

.notification-severity-error {
  border-left-color: #ff4d4f !important;
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.05), rgba(255, 77, 79, 0.02)) !important;
}

/* Dark mode support */
.dark .notification-item {
  background: rgba(31, 41, 55, 0.8) !important;
  border-color: rgba(75, 85, 99, 0.3) !important;
  color: #f9fafb !important;
}

.dark .connection-status {
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.9), rgba(17, 24, 39, 0.9));
  border-color: rgba(75, 85, 99, 0.3);
}

.dark .notification-dropdown .ant-dropdown {
  background: #1f2937 !important;
  border-color: rgba(75, 85, 99, 0.3) !important;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .notification-dropdown .ant-dropdown {
    width: 90vw !important;
    max-width: 350px !important;
  }
  
  .notification-item {
    padding: 12px !important;
  }
  
  .notification-tabs .ant-tabs-tab {
    padding: 6px 8px !important;
    font-size: 11px !important;
  }
}

/* Smooth scrollbar for notification list */
.notification-list::-webkit-scrollbar {
  width: 4px;
}

.notification-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
}

.notification-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.notification-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Loading animation for connection status */
.connection-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Notification action buttons */
.notification-action-btn {
  transition: all 0.2s ease !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
}

.notification-action-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Modern Modal Styles */
.modern-modal .ant-modal-content {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.modern-modal .ant-modal-header {
  border-bottom: 1px solid #f0f0f0 !important;
  padding: 24px !important;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9) !important;
}

.modern-modal .ant-modal-body {
  padding: 0 !important;
}

.modern-modal .ant-form-item-label > label {
  font-weight: 600 !important;
  color: #374151 !important;
}

.modern-modal .ant-input,
.modern-modal .ant-input-password {
  border-radius: 8px !important;
  border: 2px solid #e5e7eb !important;
  transition: all 0.2s ease !important;
}

.modern-modal .ant-input:focus,
.modern-modal .ant-input-password:focus,
.modern-modal .ant-input-focused,
.modern-modal .ant-input-password-focused {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.modern-modal .ant-btn {
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.modern-modal .ant-btn:hover {
  transform: translateY(-1px) !important;
}
