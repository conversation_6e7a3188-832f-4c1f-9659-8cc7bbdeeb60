enum WebLanguageGalaxyEnum {
  Zh_TW = 'zh-TW',
  En_US = 'en-US',
}

// Updated to match backend API values
enum OrderTypeEnums {
  Buy = 'BUY',
  Sell = 'SELL',
}

enum OrderStatusEnums {
  Pending = 'PENDING',
  Confirmed = 'CONFIRMED',
  Processing = 'PROCESSING',
  Completed = 'COMPLETED',
  Cancelled = 'CANCELLED',
  Rejected = 'REJECTED',
}
// Backend API currency codes
enum CurrencyCodeEnums {
  TWD = 'TWD', // New Taiwan Dollar
  HKD = 'HKD', // Hong Kong Dollar
  JPY = 'JPY', // Japanese Yen
  CNY = 'CNY', // Chinese Yuan
  SGD = 'SGD', // Singapore Dollar
  MYR = 'MYR', // Malaysian Ringgit
  KRW = 'KRW', // South Korean Won
  THB = 'THB', // Thai Baht
  USD = 'USD', // US Dollar
  GBP = 'GBP', // British Pound
  VND = 'VND', // Vietnamese Don
  PHP = 'PHP', // Philippine Peso
}

enum MemberStatusEnums {
  Inactive = 0,
  Active = 1,
}
enum CounterStatusEnums {
  Inactive = 0,
  Active = 1,
}

enum StaffStatusEnums {
  Inactive = 0,
  Active = 1,
}

enum PassportTypeEnum {
  Ordinary, // 普通護照
  Diplomatic, // 外交護照
  Official, // 官方護照
  Special, // 難民護照
  Temporary, // 臨時護照
}

export {
  WebLanguageGalaxyEnum,
  OrderTypeEnums,
  OrderStatusEnums,
  MemberStatusEnums,
  StaffStatusEnums,
  CurrencyCodeEnums,
  PassportTypeEnum,
  CounterStatusEnums,
};
