import { OrderStatusEnums } from '@/utils/enums';

/**
 * Get currency symbol for display
 */
export const getCurrencySymbol = (currencyCode: string): string => {
  const symbols: Record<string, string> = {
    TWD: 'NT$',
    HKD: 'HK$',
    JPY: '¥',
    CNY: '¥',
    SGD: 'S$',
    MYR: 'RM',
    KRW: '₩',
    THB: '฿',
    USD: '$',
    GBP: '£',
    VND: '₫',
    PHP: '₱',
  };

  return symbols[currencyCode] || currencyCode;
};

/**
 * Format currency amount with symbol
 */
export const formatOrderAmount = (
  amount: number,
  currencyCode: string,
): string => {
  const symbol = getCurrencySymbol(currencyCode);
  return `${symbol}${amount.toLocaleString()}`;
};

/**
 * Get status color for UI components
 */
export const getOrderStatusColor = (
  status: OrderStatusEnums | string,
): string => {
  const statusUpper =
    typeof status === 'string' ? status.toUpperCase() : status;

  switch (statusUpper) {
    case OrderStatusEnums.Completed:
      return 'success';
    case OrderStatusEnums.Confirmed:
      return 'processing';
    case OrderStatusEnums.Processing:
      return 'processing';
    case OrderStatusEnums.Pending:
      return 'warning';
    case OrderStatusEnums.Cancelled:
      return 'default';
    case OrderStatusEnums.Rejected:
      return 'error';
    default:
      return 'default';
  }
};
