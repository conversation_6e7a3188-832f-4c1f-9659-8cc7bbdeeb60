import {
  NotificationMessageKey,
  type NotificationPayload,
  NotificationSeverity,
  NotificationType,
  type UINotification
} from "@/types";


/**
 * Transform a raw notification payload into a UI-friendly notification
 */
export const transformNotificationForUI = (notification: NotificationPayload): UINotification => {
  const { title, description, actionUrl, actionText } = generateNotificationContent(notification);

  return {
    ...notification,
    title,
    description,
    actionUrl,
    actionText,
    autoClose: notification.severity === NotificationSeverity.Info,
    duration: getDurationBySeverity(notification.severity),
  };
};

/**
 * Generate notification content based on type and message key
 */
export const generateNotificationContent = (notification: NotificationPayload) => {
  const { type, messageKey, metadata } = notification;

  switch (type) {
    case NotificationType.Order:
      return generateOrderNotificationContent(messageKey, metadata);
    case NotificationType.User:
      return generateUserNotificationContent(messageKey, metadata);
    case NotificationType.Staff:
      return generateStaffNotificationContent(messageKey, metadata);
    case NotificationType.Counter:
      return generateCounterNotificationContent(messageKey, metadata);
    case NotificationType.System:
      return generateSystemNotificationContent(messageKey, metadata);
    default:
      return {
        title: 'New Notification',
        description: 'You have received a new notification',
        actionUrl: undefined,
        actionText: undefined,
      };
  }
};

/**
 * Generate order-specific notification content
 */
const generateOrderNotificationContent = (messageKey: NotificationMessageKey, metadata: any) => {
  const orderCode = metadata?.UxmOrderId || metadata?.OrderCode || 'Unknown';
  const orderType = metadata?.OrderType || '';
  const amount = metadata?.PricePerUnit && metadata?.Quantity 
    ? `${metadata.CurrencyUnit} ${(metadata.PricePerUnit * metadata.Quantity).toFixed(2)}`
    : '';

  switch (messageKey) {
    case NotificationMessageKey.Order_CreatedComplete:
      return {
        title: 'Order Created Successfully',
        description: `${orderType} order ${orderCode} has been created successfully${amount ? ` for ${amount}` : ''}`,
        actionUrl: `/orders/review/${metadata?.OrderId}`,
        actionText: 'Review Order',
      };
    case NotificationMessageKey.Order_CreatedFail:
      return {
        title: 'Order Creation Failed',
        description: `Failed to create ${orderType} order ${orderCode}`,
        actionUrl: '/orders',
        actionText: 'View Orders',
      };
    case NotificationMessageKey.Order_StatusUpdated:
      return {
        title: 'Order Status Updated',
        description: `Order ${orderCode} status has been updated to ${metadata?.OrderStatus || 'Unknown'}`,
        actionUrl: `/orders/review/${metadata?.OrderId}`,
        actionText: 'View Order',
      };
    case NotificationMessageKey.Order_PaymentReceived:
      return {
        title: 'Payment Received',
        description: `Payment received for order ${orderCode}${amount ? ` (${amount})` : ''}`,
        actionUrl: `/orders/review/${metadata?.OrderId}`,
        actionText: 'View Order',
      };
    case NotificationMessageKey.Order_Cancelled:
      return {
        title: 'Order Cancelled',
        description: `Order ${orderCode} has been cancelled`,
        actionUrl: `/orders/review/${metadata?.OrderId}`,
        actionText: 'View Order',
      };
    default:
      return {
        title: 'Order Update',
        description: `Order ${orderCode} has been updated`,
        actionUrl: `/orders/review/${metadata?.OrderId}`,
        actionText: 'View Order',
      };
  }
};

/**
 * Generate user-specific notification content
 */
const generateUserNotificationContent = (messageKey: NotificationMessageKey, metadata: any) => {
  const userName = metadata?.UserName || metadata?.Name || 'Unknown User';
  const userId = metadata?.UserId || '';

  switch (messageKey) {
    case NotificationMessageKey.User_Registered:
      return {
        title: 'New User Registered',
        description: `${userName} has registered successfully`,
        actionUrl: '/user-management',
        actionText: 'View Users',
      };
    case NotificationMessageKey.User_StatusChanged:
      return {
        title: 'User Status Changed',
        description: `${userName}'s status has been updated`,
        actionUrl: '/user-management',
        actionText: 'View Users',
      };
    case NotificationMessageKey.User_ProfileUpdated:
      return {
        title: 'User Profile Updated',
        description: `${userName} has updated their profile`,
        actionUrl: '/user-management',
        actionText: 'View Users',
      };
    default:
      return {
        title: 'User Update',
        description: `User ${userName} has been updated`,
        actionUrl: '/user-management',
        actionText: 'View Users',
      };
  }
};

/**
 * Generate staff-specific notification content
 */
const generateStaffNotificationContent = (messageKey: NotificationMessageKey, metadata: any) => {
  const staffName = metadata?.StaffName || metadata?.Name || 'Unknown Staff';

  switch (messageKey) {
    case NotificationMessageKey.Staff_Added:
      return {
        title: 'New Staff Added',
        description: `${staffName} has been added to the team`,
        actionUrl: '/management/employees',
        actionText: 'View Staff',
      };
    case NotificationMessageKey.Staff_StatusChanged:
      return {
        title: 'Staff Status Changed',
        description: `${staffName}'s status has been updated`,
        actionUrl: '/management/employees',
        actionText: 'View Staff',
      };
    case NotificationMessageKey.Staff_RoleUpdated:
      return {
        title: 'Staff Role Updated',
        description: `${staffName}'s role has been updated`,
        actionUrl: '/management/employees',
        actionText: 'View Staff',
      };
    default:
      return {
        title: 'Staff Update',
        description: `Staff member ${staffName} has been updated`,
        actionUrl: '/management/employees',
        actionText: 'View Staff',
      };
  }
};

/**
 * Generate counter-specific notification content
 */
const generateCounterNotificationContent = (messageKey: NotificationMessageKey, metadata: any) => {
  const counterId = metadata?.CounterId || 'Unknown Counter';

  switch (messageKey) {
    case NotificationMessageKey.Counter_Created:
      return {
        title: 'New Counter Created',
        description: `Counter ${counterId} has been created`,
        actionUrl: '/management/counters',
        actionText: 'View Counters',
      };
    case NotificationMessageKey.Counter_StatusChanged:
      return {
        title: 'Counter Status Changed',
        description: `Counter ${counterId} status has been updated`,
        actionUrl: '/management/counters',
        actionText: 'View Counters',
      };
    case NotificationMessageKey.Counter_Updated:
      return {
        title: 'Counter Updated',
        description: `Counter ${counterId} has been updated`,
        actionUrl: '/management/counters',
        actionText: 'View Counters',
      };
    default:
      return {
        title: 'Counter Update',
        description: `Counter ${counterId} has been updated`,
        actionUrl: '/management/counters',
        actionText: 'View Counters',
      };
  }
};

/**
 * Generate system-specific notification content
 */
const generateSystemNotificationContent = (messageKey: NotificationMessageKey, metadata: any) => {
  switch (messageKey) {
    case NotificationMessageKey.System_Error:
      return {
        title: 'System Error',
        description: metadata?.ErrorMessage || 'A system error has occurred',
        actionUrl: '/system/logs',
        actionText: 'View Logs',
      };
    case NotificationMessageKey.System_MaintenanceStart:
      return {
        title: 'Maintenance Started',
        description: 'System maintenance has begun',
        actionUrl: undefined,
        actionText: undefined,
      };
    case NotificationMessageKey.System_MaintenanceEnd:
      return {
        title: 'Maintenance Completed',
        description: 'System maintenance has been completed',
        actionUrl: undefined,
        actionText: undefined,
      };
    case NotificationMessageKey.System_BackupComplete:
      return {
        title: 'Backup Completed',
        description: 'System backup has been completed successfully',
        actionUrl: undefined,
        actionText: undefined,
      };
    case NotificationMessageKey.System_BackupFailed:
      return {
        title: 'Backup Failed',
        description: 'System backup has failed',
        actionUrl: '/system/logs',
        actionText: 'View Logs',
      };
    default:
      return {
        title: 'System Notification',
        description: 'System notification received',
        actionUrl: undefined,
        actionText: undefined,
      };
  }
};

/**
 * Get notification duration based on severity
 */
const getDurationBySeverity = (severity: NotificationSeverity): number => {
  switch (severity) {
    case NotificationSeverity.Error:
      return 0; // Don't auto-close errors
    case NotificationSeverity.Warning:
      return 8000; // 8 seconds
    case NotificationSeverity.Success:
      return 5000; // 5 seconds
    case NotificationSeverity.Info:
    default:
      return 4000; // 4 seconds
  }
};
