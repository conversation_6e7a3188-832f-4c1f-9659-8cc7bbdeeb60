import type { Api<PERSON>ilter, <PERSON>piSort, ApiFiltersParams } from '@/types';

/**
 * Builds query string parameters for API filtering and sorting
 * Based on the API specification:
 * - Filter template: {key}~{operator}~{value}
 * - Sort template: {key}:{direction}
 */

/**
 * Convert camelCase to PascalCase
 * @param str - The camelCase string
 * @returns PascalCase string
 *
 * Examples:
 * - orderStatus -> OrderStatus
 * - userId -> UserId
 * - createdAt -> CreatedAt
 * - totalPrice -> TotalPrice
 */
export const toPascalCase = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * Converts an ApiFilter to query string format with Pascal<PERSON>ase keys
 * @param filter - The filter object
 * @returns Filter string in format: PascalCaseKey~operator~value
 */
export const buildFilterString = (filter: ApiFilter): string => {
  const pascalCaseKey = toPascalCase(filter.key);
  return `${pascal<PERSON>aseKey}~${filter.operator}~${filter.value}`;
};

/**
 * Converts an ApiSort to query string format with PascalCase keys
 * @param sort - The sort object
 * @returns Sort string in format: PascalCaseKey:direction
 */
export const buildSortString = (sort: ApiSort): string => {
  const pascalCaseKey = toPascalCase(sort.key);
  return `${pascalCaseKey}:${sort.direction}`;
};

/**
 * Builds complete query parameters object for API requests
 * @param params - The API filters parameters
 * @returns URLSearchParams object ready for API requests
 */
export const buildApiQueryParams = (
  params: ApiFiltersParams,
): URLSearchParams => {
  const queryParams = new URLSearchParams();

  // Add filters
  if (params.filters && params.filters.length > 0) {
    params.filters.forEach((filter) => {
      queryParams.append('filter', buildFilterString(filter));
    });
  }

  // Add sorts
  if (params.sorts && params.sorts.length > 0) {
    params.sorts.forEach((sort) => {
      queryParams.append('sort', buildSortString(sort));
    });
  }

  // Add pagination
  if (params.page !== undefined) {
    queryParams.append('page', params.page.toString());
  }

  if (params.pageSize !== undefined) {
    queryParams.append('pageSize', params.pageSize.toString());
  }

  return queryParams;
};

/**
 * Builds query string from API filters parameters
 * @param params - The API filters parameters
 * @returns Complete query string (without leading ?)
 */
export const buildApiQueryString = (params: ApiFiltersParams): string => {
  const queryParams = buildApiQueryParams(params);
  return queryParams.toString();
};

/**
 * Validates filter operator based on field type
 * @param type - The field type
 * @param operator - The operator to validate
 * @returns Whether the operator is valid for the field type
 */
export const isValidOperatorForType = (
  type: 'string' | 'number' | 'date' | 'boolean' | 'uuid' | 'enum',
  operator: string,
): boolean => {
  const operatorsByType = {
    string: ['eq', 'ne', 'c', 'sw', 'ew'],
    number: ['eq', 'ne', 'gt', 'gte', 'lt', 'lte'],
    date: ['eq', 'ne', 'gt', 'gte', 'lt', 'lte'],
    boolean: ['eq', 'ne'],
    uuid: ['eq', 'ne'],
    enum: ['eq', 'ne'],
  };

  return operatorsByType[type]?.includes(operator) ?? false;
};

/**
 * Gets available operators for a field type
 * @param type - The field type
 * @returns Array of valid operators for the field type
 */
export const getOperatorsForType = (
  type: 'string' | 'number' | 'date' | 'boolean' | 'uuid' | 'enum',
): string[] => {
  const operatorsByType = {
    string: ['eq', 'ne', 'c', 'sw', 'ew'],
    number: ['eq', 'ne', 'gt', 'gte', 'lt', 'lte'],
    date: ['eq', 'ne', 'gt', 'gte', 'lt', 'lte'],
    boolean: ['eq', 'ne'],
    uuid: ['eq', 'ne'],
    enum: ['eq', 'ne'],
  };

  return operatorsByType[type] || [];
};

/**
 * Parses a filter string back to ApiFilter object
 * @param filterString - Filter string in format: key~operator~value
 * @returns ApiFilter object or null if invalid format
 */
export const parseFilterString = (filterString: string): ApiFilter | null => {
  const parts = filterString.split('~');
  if (parts.length !== 3) {
    return null;
  }

  const [key, operator, valueStr] = parts;

  // Try to parse value as number or boolean, otherwise keep as string
  let value: string | number | boolean = valueStr;

  // Check if it's a boolean
  if (valueStr === 'true' || valueStr === 'false') {
    value = valueStr === 'true';
  } else {
    // Check if it's a number
    const numValue = Number(valueStr);
    if (!isNaN(numValue) && isFinite(numValue)) {
      value = numValue;
    }
  }

  return {
    key,
    operator: operator as any, // Type assertion since we validate elsewhere
    value,
  };
};

/**
 * Parses a sort string back to ApiSort object
 * @param sortString - Sort string in format: key:direction
 * @returns ApiSort object or null if invalid format
 */
export const parseSortString = (sortString: string): ApiSort | null => {
  const parts = sortString.split(':');
  if (parts.length !== 2) {
    return null;
  }

  const [key, direction] = parts;

  if (direction !== 'asc' && direction !== 'desc') {
    return null;
  }

  return {
    key,
    direction,
  };
};
