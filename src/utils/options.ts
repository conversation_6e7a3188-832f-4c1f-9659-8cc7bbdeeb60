import {
  MemberStatusEnums,
  OrderStatusEnums,
  OrderTypeEnums,
  PassportTypeEnum,
  StaffStatusEnums,
  CurrencyCodeEnums,
  CounterStatusEnums,
} from './enums';

type EnumObjBase<
  T extends Record<string, string | number>,
  Other = object,
> = Record<keyof T, { label: string; value: ValueOf<T> } & Other>;

const eNumEntities = <T extends Record<string, string | number>>(
  originEnum: T,
) => {
  const keys = Object.keys(originEnum).filter((key) =>
    isNaN(Number(key)),
  ) as Array<keyof T>;

  const values = keys.map((key) => originEnum[key]) as Array<ValueOf<T>>;

  return { keys, values };
};

const orderTypeEnumsObj: EnumObjBase<typeof OrderTypeEnums> = {
  Buy: {
    label: 'buyOrderTypeEnums',
    value: OrderTypeEnums.Buy,
  },
  Sell: {
    label: 'sellOrderTypeEnums',
    value: OrderTypeEnums.Sell,
  },
};

const orderStatusEnumsObj: EnumObjBase<typeof OrderStatusEnums> = {
  Pending: {
    label: 'pendingOrderStatusEnums',
    value: OrderStatusEnums.Pending,
  },
  Confirmed: {
    label: 'confirmedOrderStatusEnums',
    value: OrderStatusEnums.Confirmed,
  },
  Processing: {
    label: 'processingOrderStatusEnums',
    value: OrderStatusEnums.Processing,
  },
  Completed: {
    label: 'completedOrderStatusEnums',
    value: OrderStatusEnums.Completed,
  },
  Cancelled: {
    label: 'cancelledOrderStatusEnums',
    value: OrderStatusEnums.Cancelled,
  },
  Rejected: {
    label: 'rejectedOrderStatusEnums',
    value: OrderStatusEnums.Rejected,
  },
};

const currencyCodeEnumsObj: EnumObjBase<typeof CurrencyCodeEnums> = {
  TWD: { label: 'twdCurrencyEnums', value: CurrencyCodeEnums.TWD },
  HKD: { label: 'hkdCurrencyEnums', value: CurrencyCodeEnums.HKD },
  JPY: { label: 'jpyCurrencyEnums', value: CurrencyCodeEnums.JPY },
  CNY: { label: 'cnyCurrencyEnums', value: CurrencyCodeEnums.CNY },
  SGD: { label: 'sgdCurrencyEnums', value: CurrencyCodeEnums.SGD },
  MYR: { label: 'myrCurrencyEnums', value: CurrencyCodeEnums.MYR },
  KRW: { label: 'krwCurrencyEnums', value: CurrencyCodeEnums.KRW },
  THB: { label: 'thbCurrencyEnums', value: CurrencyCodeEnums.THB },
  USD: { label: 'usdCurrencyEnums', value: CurrencyCodeEnums.USD },
  GBP: { label: 'gbpCurrencyEnums', value: CurrencyCodeEnums.GBP },
  VND: { label: 'vndCurrencyEnums', value: CurrencyCodeEnums.VND },
  PHP: { label: 'phpCurrencyEnums', value: CurrencyCodeEnums.PHP },
};

const orderTypeEnumsOptions = Object.keys(OrderTypeEnums).map((key) => {
  const enumKey = key as keyof typeof OrderTypeEnums;
  const { label = 'undefined', value } = orderTypeEnumsObj[enumKey];
  return { label, value };
});

const orderStatusEnumsOptions = eNumEntities<typeof OrderStatusEnums>(
  OrderStatusEnums,
).keys.map((enumKey) => {
  const { label = 'undefined', value } = orderStatusEnumsObj[enumKey];
  return { label, value };
});

const memberStatusEnumsObj: EnumObjBase<typeof MemberStatusEnums> = {
  Inactive: {
    label: 'inactiveStaffStatusEnums',
    value: MemberStatusEnums.Inactive,
  },
  Active: {
    label: 'activeStaffStatusEnums',
    value: MemberStatusEnums.Active,
  },
};
const memberStatusEnumsOptions = eNumEntities<typeof MemberStatusEnums>(
  MemberStatusEnums,
).keys.map((enumKey) => {
  const { label = 'undefined', value } = memberStatusEnumsObj[enumKey];
  return { label, value };
});

const staffStatusEnumsObj: EnumObjBase<typeof StaffStatusEnums> = {
  Inactive: {
    label: 'inactiveStaffStatusEnums',
    value: StaffStatusEnums.Inactive,
  },
  Active: {
    label: 'activeStaffStatusEnums',
    value: StaffStatusEnums.Active,
  },
};
const staffStatusEnumsOptions = eNumEntities<typeof StaffStatusEnums>(
  StaffStatusEnums,
).keys.map((enumKey) => {
  const { label = 'undefined', value } = staffStatusEnumsObj[enumKey];
  return { label, value };
});
const counterStatusEnumsObj: EnumObjBase<typeof CounterStatusEnums> = {
  Inactive: {
    label: 'inactiveCounterStatusEnums',
    value: CounterStatusEnums.Inactive,
  },
  Active: {
    label: 'activeCounterStatusEnums',
    value: CounterStatusEnums.Active,
  },
};
const counterStatusEnumsOptions = eNumEntities<typeof CounterStatusEnums>(
  CounterStatusEnums,
).keys.map((enumKey) => {
  const { label = 'undefined', value } = counterStatusEnumsObj[enumKey];
  return { label, value };
});

const currencyCodeEnumsOptions = Object.keys(CurrencyCodeEnums).map((key) => {
  const enumKey = key as keyof typeof CurrencyCodeEnums;
  const { label = 'undefined', value } = currencyCodeEnumsObj[enumKey];
  return { label, value };
});

const passportTypeEnumsObj: EnumObjBase<typeof PassportTypeEnum> = {
  Ordinary: {
    label: 'ordinaryPassportTypeEnum',
    value: PassportTypeEnum.Ordinary,
  },
  Diplomatic: {
    label: 'diplomaticPassportTypeEnum',
    value: PassportTypeEnum.Diplomatic,
  },
  Official: {
    label: 'officialPassportTypeEnum',
    value: PassportTypeEnum.Official,
  },
  Special: {
    label: 'specialPassportTypeEnum',
    value: PassportTypeEnum.Special,
  },
  Temporary: {
    label: 'temporaryPassportTypeEnum',
    value: PassportTypeEnum.Temporary,
  },
};

const passportTypeEnumsOptions = eNumEntities<typeof PassportTypeEnum>(
  PassportTypeEnum,
).keys.map((enumKey) => {
  const { label = 'undefined', value } = passportTypeEnumsObj[enumKey];
  return { label, value };
});
export {
  orderTypeEnumsOptions,
  orderStatusEnumsOptions,
  memberStatusEnumsOptions,
  staffStatusEnumsOptions,
  currencyCodeEnumsOptions,
  passportTypeEnumsOptions,
  counterStatusEnumsOptions,
};
