// Utility functions for table operations

export const sortByDate = (
  a: string | null | undefined,
  b: string | null | undefined,
): number => {
  const dateA = new Date(a ?? '').getTime();
  const dateB = new Date(b ?? '').getTime();
  return dateB - dateA;
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

export const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const formatCurrency = (
  amount: number,
  currency: string = 'USD',
): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
};

export const getStatusColor = (status: string, isDark: boolean): string => {
  const lightColors: Record<string, string> = {
    active: 'green',
    inactive: 'red',
    pending: 'orange',
    completed: 'green',
    failed: 'red',
    cancelled: 'gray',
    processing: 'blue',
  };

  const darkColors: Record<string, string> = {
    active: '#6EE7B7',
    inactive: '#FCA5A5',
    pending: '#FCD34D',
    completed: '#86EFAC',
    failed: '#F87171',
    cancelled: '#D4D4D8',
    processing: '#93C5FD',
  };

  const colorMap = isDark ? darkColors : lightColors;

  return colorMap[status] || (isDark ? '#A3A3A3' : 'default');
};

export const truncateText = (text: string, maxLength: number = 50): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};
