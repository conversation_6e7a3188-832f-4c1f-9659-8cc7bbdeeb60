import {
  BankOutlined,
  CalendarOutlined,
  DollarOutlined,
  EditOutlined,
  FileTextOutlined,
  NumberOutlined,
  SaveOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Row,
  Select,
  Typography,
} from 'antd';
import React, { useEffect } from 'react';
import { OrderStatusTag, OrderTypeTag, TextCompression } from '@/components';
import { useEditOrder } from '@/hooks/useOrders';
import { useUserStore } from '@/stores';
import type { Order } from '@/types';
import { formatOrderAmount, getCurrencySymbol } from '@/utils/currencyUtils';
import {
  CurrencyCodeEnums,
  OrderStatusEnums,
  OrderTypeEnums,
} from '@/utils/enums';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

interface EditOrderModalProps {
  visible: boolean;
  onCancel: () => void;
  order: Order | null;
}

const EditOrderModal: React.FC<EditOrderModalProps> = (props) => {
  const { visible, onCancel, order } = props || {};
  const { isDark } = useUserStore();
  const [form] = Form.useForm();
  const editOrderMutation = useEditOrder();

  useEffect(() => {
    if (visible && order) {
      form.setFieldsValue({
        quantity: order.quantity,
        priceOfUnit: order.pricePerUnit,
        currencyUnit: order.currencyUnit,
        orderStatus: order.orderStatus,
        notes: order.notes,
        metadata: order.metadata ? JSON.stringify(order.metadata, null, 2) : '',
      });
    }
  }, [visible, order, form]);

  const handleSubmit = async () => {
    if (!order) return;

    try {
      const values = await form.validateFields();

      let metadata = null;
      if (values.metadata && values.metadata.trim()) {
        try {
          metadata = JSON.parse(values.metadata);
        } catch (e) {
          console.log(e);
          // If JSON parsing fails, store as string
          metadata = values.metadata;
        }
      }

      await editOrderMutation.mutateAsync({
        orderId: order.orderId,
        orderData: {
          orderId: order.orderId,
          quantity: values.quantity,
          priceOfUnit: values.priceOfUnit,
          currencyUnit: values.currencyUnit,
          orderStatus: values.orderStatus,
          notes: values.notes || '',
          metadata,
        },
      });

      onCancel();
      form.resetFields();
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  const handleCancel = () => {
    onCancel();
    form.resetFields();
  };

  // Calculate total price in real-time
  const quantity = Form.useWatch('quantity', form) || 0;
  const priceOfUnit = Form.useWatch('priceOfUnit', form) || 0;
  const currencyUnit = Form.useWatch('currencyUnit', form) || 'USD';
  const totalPrice = quantity * priceOfUnit;

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={handleCancel}
      width={900}
      footer={null}
      className='edit-order-modal'
      styles={{
        body: { padding: 0 },
        header: { display: 'none' },
      }}
    >
      <div>
        {/* Header */}
        <div className='user px-8 py-6 border-b border-gray-200'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-4'>
              <div
                className={`p-3 ${isDark ? 'bg-blue-400' : 'bg-blue-100'} rounded-xl`}
              >
                <EditOutlined className='text-blue-600 text-2xl' />
              </div>
              <div>
                <Title level={3} className='!mb-1'>
                  Edit Order
                </Title>
                <div className='flex items-center gap-3'>
                  <div className='flex items-center gap-1'>
                    <Text className='text-gray-500'>#</Text>
                    <TextCompression
                      text={order?.uxmOrderId || ''}
                      maxLength={12}
                      size='default'
                      type='secondary'
                    />
                  </div>
                  <OrderTypeTag type={order?.orderType || OrderTypeEnums.Buy} />
                  <OrderStatusTag
                    status={order?.orderStatus || OrderStatusEnums.Pending}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Order Info Cards */}
        <div className='px-8 py-6 '>
          <Row gutter={24}>
            <Col span={8}>
              <Card className='text-center border-0 shadow-sm'>
                <div className='flex flex-col items-center gap-2'>
                  <div
                    className={`p-2 ${isDark ? 'bg-green-400' : 'bg-green-100'} rounded-lg`}
                  >
                    <UserOutlined className='!text-green-600' />
                  </div>
                  <Text className='!text-green-500 text-sm'>User ID</Text>
                  <Text className='font-semibold'>
                    {order?.userId?.slice(0, 8)}...
                  </Text>
                </div>
              </Card>
            </Col>
            <Col span={8}>
              <Card className='text-center border-0 shadow-sm'>
                <div className='flex flex-col items-center gap-2'>
                  <div
                    className={`p-2 ${isDark ? 'bg-purple-400' : 'bg-purple-100'} rounded-lg`}
                  >
                    <BankOutlined className='!text-purple-600' />
                  </div>
                  <Text className='!text-purple-500 text-sm'>Counter</Text>
                  <Text className='font-semibold'>{order?.counterId}</Text>
                </div>
              </Card>
            </Col>
            <Col span={8}>
              <Card className='text-center border-0 shadow-sm'>
                <div className='flex flex-col items-center gap-2'>
                  <div
                    className={`p-2 ${isDark ? 'bg-orange-400' : 'bg-orange-100'} rounded-lg`}
                  >
                    <CalendarOutlined className='!text-orange-600' />
                  </div>
                  <Text className='!text-orange-300 text-sm'>Created</Text>
                  <Text className='font-semibold'>
                    {order?.createdAt
                      ? new Date(order.createdAt).toLocaleDateString()
                      : '-'}
                  </Text>
                </div>
              </Card>
            </Col>
          </Row>
        </div>

        {/* Price Comparison */}
        <div className='px-8 py-4'>
          <Row gutter={24}>
            <Col span={12}>
              <div
                className={`text-center p-4 user rounded-xl shadow-sm ${isDark ? 'bg-gray-800' : 'bg-gray-200'}`}
              >
                <Text className='text-gray-500 block mb-2'>Current Total</Text>
                <Text className='text-2xl font-bold text-green-600'>
                  {order &&
                    formatOrderAmount(
                      order.quantity * order.pricePerUnit,
                      order.currencyUnit,
                    )}
                </Text>
                <Text className='text-sm text-gray-500 block mt-1'>
                  {order?.quantity} ×{' '}
                  {order &&
                    formatOrderAmount(order.pricePerUnit, order.currencyUnit)}
                </Text>
              </div>
            </Col>
            <Col span={12}>
              <div
                className={`text-center p-4 user rounded-xl shadow-sm ${isDark ? 'bg-gray-800' : 'bg-gray-200'}`}
              >
                <Text className='text-gray-500 block mb-2'>New Total</Text>
                <Text className='text-2xl font-bold text-blue-600'>
                  {formatOrderAmount(totalPrice, currencyUnit)}
                </Text>
                <Text className='text-sm text-gray-500 block mt-1'>
                  {quantity} × {formatOrderAmount(priceOfUnit, currencyUnit)}
                </Text>
              </div>
            </Col>
          </Row>
        </div>

        {/* Form Section */}
        <div className='px-8 py-6'>
          <Form
            form={form}
            layout='vertical'
            requiredMark={false}
            className='space-y-6'
          >
            {/* Main Fields */}
            <Card title='Order Details' className='border-0 shadow-sm'>
              <Row gutter={24}>
                {/* Quantity */}
                <Col span={12}>
                  <Form.Item
                    name='quantity'
                    label={
                      <div className='flex items-center gap-2 mb-2'>
                        <div
                          className={`p-1 rounded ${isDark ? 'bg-blue-400' : 'bg-blue-100'}`}
                        >
                          <NumberOutlined className='!text-blue-600 text-sm' />
                        </div>
                        <span className='font-medium'>Quantity</span>
                      </div>
                    }
                    rules={[
                      { required: true, message: 'Please enter quantity' },
                      {
                        type: 'number',
                        min: 1,
                        message: 'Quantity must be at least 1',
                      },
                    ]}
                  >
                    <InputNumber
                      placeholder='Enter quantity'
                      min={1}
                      className='w-full'
                      size='large'
                      style={{ borderRadius: '8px' }}
                    />
                  </Form.Item>
                </Col>

                {/* Price Per Unit */}
                <Col span={12}>
                  <Form.Item
                    name='priceOfUnit'
                    label={
                      <div className='flex items-center gap-2 mb-2'>
                        <div
                          className={`p-1 rounded ${isDark ? 'bg-green-400' : 'bg-green-100'}`}
                        >
                          <DollarOutlined className='!text-green-600 text-sm' />
                        </div>
                        <span className='font-medium'>Price Per Unit</span>
                      </div>
                    }
                    rules={[
                      {
                        required: true,
                        message: 'Please enter price per unit',
                      },
                      {
                        type: 'number',
                        min: 0.01,
                        message: 'Price must be greater than 0',
                      },
                    ]}
                  >
                    <InputNumber
                      placeholder='Enter price per unit'
                      min={0.01}
                      step={0.01}
                      precision={2}
                      className='w-full'
                      size='large'
                      style={{ borderRadius: '8px' }}
                      addonBefore={getCurrencySymbol(currencyUnit)}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Currency & Status */}
            <Card title='Settings' className='border-0 shadow-sm'>
              <Row gutter={24}>
                {/* Currency */}
                <Col span={12}>
                  <Form.Item
                    name='currencyUnit'
                    label={
                      <div className='flex items-center gap-2 mb-2'>
                        <div
                          className={`p-1 rounded ${isDark ? 'bg-yellow-400' : 'bg-yellow-100'}`}
                        >
                          <DollarOutlined className='!text-yellow-600 text-sm' />
                        </div>
                        <span className='font-medium'>Currency</span>
                      </div>
                    }
                    rules={[
                      { required: true, message: 'Please select currency' },
                    ]}
                  >
                    <Select
                      placeholder='Select currency'
                      size='large'
                      className='w-full'
                      style={{ borderRadius: '8px' }}
                    >
                      {Object.values(CurrencyCodeEnums).map((currency) => (
                        <Option key={currency} value={currency}>
                          <div className='flex items-center gap-2'>
                            <span
                              className={`font-mono text-xs ${isDark ? 'bg-gray-600' : 'bg-gray-100'} px-2 py-1 rounded`}
                            >
                              {getCurrencySymbol(currency)}
                            </span>
                            <span>{currency}</span>
                          </div>
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>

                {/* Order Status */}
                <Col span={12}>
                  <Form.Item
                    name='orderStatus'
                    label={
                      <div className='flex items-center gap-2 mb-2'>
                        <div
                          className={`p-1 rounded ${isDark ? 'bg-purple-400' : 'bg-purple-100'}`}
                        >
                          <EditOutlined className='!text-purple-600 text-sm' />
                        </div>
                        <span className='font-medium'>Order Status</span>
                      </div>
                    }
                    rules={[
                      { required: true, message: 'Please select order status' },
                    ]}
                  >
                    <Select
                      placeholder='Select order status'
                      size='large'
                      className='w-full'
                      style={{ borderRadius: '8px' }}
                    >
                      {Object.values(OrderStatusEnums).map((status) => (
                        <Option key={status} value={status}>
                          <div className='flex items-center gap-2'>
                            <div
                              className={`w-2 h-2 rounded-full ${
                                status === 'COMPLETED'
                                  ? 'bg-green-400'
                                  : status === 'PROCESSING'
                                    ? 'bg-blue-400'
                                    : status === 'PENDING'
                                      ? 'bg-orange-400'
                                      : status === 'CONFIRMED'
                                        ? 'bg-purple-400'
                                        : status === 'CANCELLED'
                                          ? 'bg-gray-400'
                                          : 'bg-red-400'
                              }`}
                            ></div>
                            {status}
                          </div>
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Notes & Metadata */}
            <Card title='Additional Information' className='border-0 shadow-sm'>
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name='notes'
                    label={
                      <div className='flex items-center gap-2 mb-2'>
                        <div
                          className={`p-1 ${isDark ? 'bg-gray-600' : 'bg-gray-100'} rounded`}
                        >
                          <FileTextOutlined className='text-sm' />
                        </div>
                        <span className='font-medium'>Notes</span>
                      </div>
                    }
                  >
                    <TextArea
                      placeholder='Enter order notes...'
                      rows={4}
                      className='w-full'
                      style={{ borderRadius: '8px' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name='metadata'
                    label={
                      <div className='flex items-center gap-2 mb-2'>
                        <div
                          className={`p-1 rounded ${isDark ? 'bg-indigo-600' : 'bg-indigo-100'}`}
                        >
                          <FileTextOutlined className='text-sm' />
                        </div>
                        <span className='font-medium'>Metadata (JSON)</span>
                      </div>
                    }
                  >
                    <TextArea
                      placeholder='{"key": "value"}'
                      rows={4}
                      className='w-full font-mono text-sm'
                      style={{ borderRadius: '8px' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Form>
        </div>

        {/* Footer Actions */}
        <div className='px-8 py-6 border-t flex justify-end gap-4'>
          <Button
            size='large'
            onClick={handleCancel}
            className='px-8 h-12 rounded-lg'
          >
            Cancel
          </Button>
          <Button
            type='primary'
            size='large'
            onClick={handleSubmit}
            loading={editOrderMutation.isPending}
            icon={<SaveOutlined />}
            className='px-8 h-12 bg-blue-600 hover:bg-blue-700 rounded-lg'
          >
            Update Order
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default EditOrderModal;
