import {
  BankOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  DollarOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  TagOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Button, Card, Col, Modal, Row, Statistic, Typography } from 'antd';
import React from 'react';
import { OrderStatusTag, OrderTypeTag, TextCompression } from '@/components';
import CurrencyTag from '@/components/Tags/CurrencyTag.tsx';
import { useUserStore } from '@/stores';
import type { Order } from '@/types';
import { getCurrencySymbol } from '@/utils/currencyUtils';
import { formatDateTime } from '@/utils/tableUtils.ts';

const { Title, Text } = Typography;

interface OrderDetailsModalProps {
  visible: boolean;
  order: Order | null;
  onClose: () => void;
  onEdit?: (order: Order) => void;
  onUpdateStatus?: (orderId: string, status: string) => void;
}

const OrderDetailsModal: React.FC<OrderDetailsModalProps> = (props) => {
  const { visible, order, onClose, onEdit } = props || {};
  const { isDark } = useUserStore();
  if (!order) return null;

  const totalPrice = order.quantity * order.pricePerUnit;

  const getStatusIcon = () => {
    switch (order.orderStatus) {
      case 'COMPLETED':
        return <CheckCircleOutlined className='!text-green-600 text-2xl' />;
      case 'PENDING':
        return <ClockCircleOutlined className='!text-orange-600 text-2xl' />;
      case 'PROCESSING':
        return <ClockCircleOutlined className='!text-blue-600 text-2xl' />;
      case 'CONFIRMED':
        return <CheckCircleOutlined className='!text-purple-600 text-2xl' />;
      case 'REJECTED':
        return <CloseCircleOutlined className='!text-red-600 text-2xl' />;
      case 'CANCELLED':
        return (
          <ExclamationCircleOutlined className='!text-gray-600 text-2xl' />
        );
      default:
        return <ClockCircleOutlined className='!text-gray-600 text-2xl' />;
    }
  };

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      className='order-details-modal'
      styles={{
        body: { padding: 0 },
        header: { display: 'none' },
      }}
    >
      <div>
        {/* Header */}
        <div className='px-8 py-6 border-b border-gray-200'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-4'>
              <div className='p-4 bg-slate-400 rounded-2xl'>
                {getStatusIcon()}
              </div>
              <div>
                <Title level={2} className='!mb-1'>
                  Order Details
                </Title>
                <div className='flex items-center gap-3 mb-2'>
                  <div className='flex items-center gap-1'>
                    <Text className='text-gray-500 text-lg'>#</Text>
                    <TextCompression
                      text={order.uxmOrderId}
                      maxLength={12}
                      size='large'
                      type='secondary'
                    />
                  </div>
                  <OrderTypeTag type={order.orderType} />
                  <OrderStatusTag status={order.orderStatus} />
                </div>
                <Text className='text-gray-600'>
                  {order.notes || 'No additional notes'}
                </Text>
              </div>
            </div>
            <div className='flex items-center gap-3'>
              {onEdit && (
                <Button
                  type='primary'
                  icon={<EditOutlined />}
                  onClick={() => onEdit(order)}
                  size='large'
                  className='bg-blue-600 hover:bg-blue-700'
                >
                  Edit Order
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className='px-8 py-6 user'>
          <Row gutter={24}>
            <Col span={8}>
              <Card className='text-center border-0 shadow-sm'>
                <Statistic
                  title='Total Amount'
                  value={totalPrice}
                  precision={2}
                  prefix={getCurrencySymbol(order.currencyUnit)}
                  valueStyle={{ color: '#16a34a' }}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card className='text-center border-0 shadow-sm'>
                <Statistic
                  title='Quantity'
                  value={order.quantity}
                  prefix={<TagOutlined className='text-purple-600' />}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card className='text-center border-0 shadow-sm'>
                <Statistic
                  title='Price Per Unit'
                  value={order.pricePerUnit}
                  precision={2}
                  prefix={getCurrencySymbol(order.currencyUnit)}
                  valueStyle={{ color: '#2563eb' }}
                />
              </Card>
            </Col>
          </Row>
        </div>

        {/* Main Content */}
        <div className='px-8 py-6'>
          <Row gutter={24}>
            {/* Left Column - Order Details */}
            <Col span={12}>
              <Card
                title='Order Information'
                className='h-full border-0 shadow-sm'
              >
                <div className='space-y-4'>
                  <div className='flex items-center justify-between p-3 rounded-lg'>
                    <div className='flex items-center gap-3'>
                      <div
                        className={`p-2 rounded-lg ${isDark ? 'bg-blue-400' : 'bg-blue-100 '}`}
                      >
                        <UserOutlined className='!text-blue-600' />
                      </div>
                      <div>
                        <Text className='text-gray-500 text-sm block'>
                          User ID
                        </Text>
                        <Text className='font-semibold'>{order.userId}</Text>
                      </div>
                    </div>
                  </div>

                  <div className='flex items-center justify-between p-3 user rounded-lg'>
                    <div className='flex items-center gap-3'>
                      <div
                        className={`p-2 rounded-lg ${isDark ? 'bg-green-400' : 'bg-green-100 '}`}
                      >
                        <BankOutlined className='!text-green-600' />
                      </div>
                      <div>
                        <Text className='text-gray-500 text-sm block'>
                          Counter ID
                        </Text>
                        <Text className='font-semibold'>{order.counterId}</Text>
                      </div>
                    </div>
                  </div>

                  <div className='flex items-center justify-between p-3 user rounded-lg'>
                    <div className='flex items-center gap-3'>
                      <div
                        className={`p-2 rounded-lg ${isDark ? 'bg-purple-400' : 'bg-purple-100 '}`}
                      >
                        <UserOutlined className='!text-purple-600' />
                      </div>
                      <div>
                        <Text className='text-gray-500 text-sm block'>
                          Staff ID
                        </Text>
                        <Text className='font-semibold'>{order.staffId}</Text>
                      </div>
                    </div>
                  </div>

                  <div className='flex items-center justify-between p-3 user rounded-lg'>
                    <div className='flex items-center gap-3'>
                      <div
                        className={`p-2 rounded-lg ${isDark ? 'bg-orange-400' : 'bg-orange-100 '}`}
                      >
                        <DollarOutlined className='text-orange-600' />
                      </div>
                      <div>
                        <Text className='text-gray-500 text-sm block'>
                          Currency
                        </Text>
                        <div className='flex items-center gap-2'>
                          <CurrencyTag currency={order.currencyUnit} />
                          <Text className='font-semibold'>
                            {order.currencyUnit}
                          </Text>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </Col>

            {/* Right Column - Timeline & Notes */}
            <Col span={12}>
              <Card
                title='Timeline & Notes'
                className='h-full border-0 shadow-sm'
              >
                <div className='space-y-4'>
                  <div className='p-3 rounded-lg'>
                    <div className='flex items-center gap-2 mb-2'>
                      <CalendarOutlined className='text-blue-600' />
                      <Text className='font-medium'>Created</Text>
                    </div>
                    <Text className='text-gray-600'>
                      {formatDateTime(order.createdAt)}
                    </Text>
                  </div>

                  <div className='p-3  rounded-lg'>
                    <div className='flex items-center gap-2 mb-2'>
                      <CalendarOutlined className='text-green-600' />
                      <Text className='font-medium'>Last Updated</Text>
                    </div>
                    <Text className='text-gray-600'>
                      {formatDateTime(order.updatedAt)}
                    </Text>
                  </div>

                  <div className='p-3 user rounded-lg'>
                    <div className='flex items-center gap-2 mb-2'>
                      <FileTextOutlined className='text-gray-600' />
                      <Text className='font-medium'>Notes</Text>
                    </div>
                    <Text className='text-gray-600'>
                      {order.notes || 'No notes available'}
                    </Text>
                  </div>

                  {order.metadata && (
                    <div className='p-3 bg-indigo-50 rounded-lg'>
                      <div className='flex items-center gap-2 mb-2'>
                        <InfoCircleOutlined className='text-indigo-600' />
                        <Text className='font-medium'>Metadata</Text>
                      </div>
                      <pre className='text-xs text-gray-600 p-2 rounded border overflow-auto max-h-32'>
                        {JSON.stringify(order.metadata, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </Card>
            </Col>
          </Row>
        </div>
      </div>
    </Modal>
  );
};

export default OrderDetailsModal;
