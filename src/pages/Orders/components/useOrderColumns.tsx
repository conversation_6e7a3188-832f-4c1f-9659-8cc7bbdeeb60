import {
  EditOutlined,
  EyeOutlined,
  ShoppingCartOutlined,
  AuditOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  Select,
  Space,
  Typography,
  type TableColumnType,
} from 'antd';
import { useTranslation } from 'react-i18next';
import { OrderTypeTag, TextCompression } from '@/components';
import CurrencyTag from '@/components/Tags/CurrencyTag';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive';
import { useUserStore } from '@/stores';
import type { Order } from '@/types';
import { OrderStatusEnums, OrderTypeEnums } from '@/utils';
import { formatOrderAmount } from '@/utils/currencyUtils';
import { formatDate } from '@/utils/tableUtils';

type OrderOptions = Order;

type UseProps = {
  onViewO: (order: OrderOptions) => void;
  onEdit: (order: OrderOptions) => void;
  onReview: (order: OrderOptions) => void;
  onUpdateStt: (orderId: number, status: OrderStatusEnums) => void;
  isLoadChangeStt: boolean;
};

const { Text } = Typography;
const { Option } = Select;

const useOrderColumns = (useProps: UseProps) => {
  const { onViewO, onEdit, onReview, isLoadChangeStt, onUpdateStt } = useProps;
  const { t } = useTranslation('useCounterColumns');
  const { isDark } = useUserStore();
  const { isMobile } = useResponsive();

  // Responsive values
  const avatarSize = useResponsiveValue({
    xs: 32,
    sm: 36,
    md: 40,
    lg: 40,
    xl: 40,
    '2xl': 40,
  });

  const columns: TableColumnType<OrderOptions>[] = [
    {
      title: t('type'),
      dataIndex: 'orderType',
      key: 'orderType',
      align: 'center',
      render: (type: OrderTypeEnums) => <OrderTypeTag type={type} />,
    },
    {
      title: t('orderDetails'),
      dataIndex: 'orderId',
      key: 'orderId',
      render: (orderId: string, record: Order) => (
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
          <Avatar
            className='bg-gradient-to-br from-green-400 to-green-600 text-white font-medium flex-shrink-0'
            icon={<ShoppingCartOutlined />}
            size={avatarSize}
          />
          <div className='min-w-0 flex-1'>
            <div className='flex items-center gap-1 mb-1'>
              <Text
                className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}
              >
                #
              </Text>
              <TextCompression
                text={orderId}
                maxLength={isMobile ? 8 : 12}
                size={isMobile ? 'small' : 'default'}
                showCopy={!isMobile}
                className='flex-1'
              />
            </div>
            <div className='flex items-center gap-1 mb-1'>
              <Text
                className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}
              >
                #
              </Text>
              <TextCompression
                text={record.uxmOrderId}
                maxLength={isMobile ? 8 : 12}
                size={isMobile ? 'small' : 'default'}
                showCopy={!isMobile}
                className='flex-1'
              />
            </div>
            <Text
              className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 truncate`}
            >
              User: {record.userId.slice(0, 8)}...
            </Text>
            <Text
              className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 truncate`}
            >
              Counter: {record.counterId}
            </Text>
          </div>
        </div>
      ),
      width: isMobile ? 180 : undefined,
    },
    {
      title: t('amount'),
      dataIndex: 'pricePerUnit',
      key: 'amount',
      render: (_pricePerUnit: number, record: Order) => {
        const totalPrice = record.quantity * record.pricePerUnit;
        return (
          <div className='text-right'>
            <Text
              className={`font-bold ${isMobile ? 'text-base' : 'text-lg'} text-gray-900 block`}
            >
              {formatOrderAmount(totalPrice, record.currencyUnit)}
            </Text>
            {!isMobile && (
              <div className='text-sm text-gray-500'>
                <div>
                  {record.quantity} ×{' '}
                  {formatOrderAmount(record.pricePerUnit, record.currencyUnit)}
                </div>
                <CurrencyTag currency={record.currencyUnit} />
              </div>
            )}
          </div>
        );
      },
      sorter: (a: Order, b: Order) =>
        a.quantity * a.pricePerUnit - b.quantity * b.pricePerUnit,
      width: isMobile ? 120 : undefined,
    },
    {
      title: t('status'),
      dataIndex: 'orderStatus',
      key: 'orderStatus',
      filters: [
        { text: 'Pending', value: 'PENDING' },
        { text: 'Confirmed', value: 'CONFIRMED' },
        { text: 'Processing', value: 'PROCESSING' },
        { text: 'Completed', value: 'COMPLETED' },
        { text: 'Cancelled', value: 'CANCELLED' },
        { text: 'Rejected', value: 'REJECTED' },
      ],
      onFilter: (value: any, record: Order) => {
        return record.orderStatus === value;
      },
      render: (status: OrderStatusEnums, record: OrderOptions) => (
        <Select
          value={status}
          style={{ width: isMobile ? 120 : 140 }}
          size={isMobile ? 'small' : 'middle'}
          onChange={(newStatus: OrderStatusEnums) =>
            onUpdateStt(record.orderId, newStatus)
          }
          loading={isLoadChangeStt}
          className='rounded-lg'
        >
          <Option value={OrderStatusEnums.Pending}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-orange-400 rounded-full'></div>
              Pending
            </div>
          </Option>
          <Option value={OrderStatusEnums.Confirmed}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-blue-400 rounded-full'></div>
              Confirmed
            </div>
          </Option>
          <Option value={OrderStatusEnums.Processing}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-purple-400 rounded-full'></div>
              Processing
            </div>
          </Option>
          <Option value={OrderStatusEnums.Completed}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-green-400 rounded-full'></div>
              Completed
            </div>
          </Option>
          <Option value={OrderStatusEnums.Cancelled}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-gray-400 rounded-full'></div>
              Cancelled
            </div>
          </Option>
          <Option value={OrderStatusEnums.Rejected}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-red-400 rounded-full'></div>
              Rejected
            </div>
          </Option>
        </Select>
      ),
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number) => (
        <Text className={`font-medium ${isMobile ? 'text-sm' : ''}`}>
          {quantity}
        </Text>
      ),
      width: isMobile ? 80 : undefined,
      responsive: isMobile ? ['md'] : undefined,
    },
    {
      title: t('date'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => (
        <div>
          <Text className={`text-gray-900 block ${isMobile ? 'text-xs' : ''}`}>
            {formatDate(date).split(',')[0]}
          </Text>
          {!isMobile && (
            <Text className='text-sm text-gray-500'>
              {formatDate(date).split(',')[1]}
            </Text>
          )}
        </div>
      ),
      sorter: (a: Order, b: Order) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
      width: isMobile ? 100 : undefined,
      responsive: isMobile ? ['lg'] : undefined,
    },
    {
      title: 'Staff ID',
      dataIndex: 'staffId',
      key: 'staffId',
      render: (staffId: string) => (
        <div
          className={`${isDark ? 'bg-slate-600' : 'bg-slate-100'} ${isMobile ? 'px-2 py-1' : 'px-3 py-1'} rounded-lg inline-block`}
        >
          <TextCompression
            text={staffId}
            maxLength={isMobile ? 8 : 12}
            size={isMobile ? 'small' : 'default'}
            showCopy={!isMobile}
          />
        </div>
      ),
      width: isMobile ? 80 : undefined,
      responsive: isMobile ? ['xl'] : undefined,
    },
    {
      title: t('actions'),
      key: 'actions',
      width: isMobile ? 80 : 180,
      render: (record: OrderOptions) => {
        const canReview = record.orderStatus === 'PENDING';

        return (
          <Space wrap>
            <Button
              type='text'
              icon={<EyeOutlined />}
              className='hover:bg-blue-50 hover:text-blue-600 rounded-lg'
              onClick={() => onViewO(record)}
              size={isMobile ? 'small' : 'middle'}
            >
              {!isMobile && 'View'}
            </Button>

            {canReview && (
              <Button
                type='text'
                icon={<AuditOutlined />}
                className='hover:bg-orange-50 hover:text-orange-600 rounded-lg'
                onClick={() => onReview(record)}
                size={isMobile ? 'small' : 'middle'}
              >
                {!isMobile && 'Review'}
              </Button>
            )}

            <Button
              type='text'
              icon={<EditOutlined />}
              className='hover:bg-green-50 hover:text-green-600 rounded-lg'
              onClick={() => onEdit(record)}
              size={isMobile ? 'small' : 'middle'}
            >
              {!isMobile && 'Edit'}
            </Button>
          </Space>
        );
      },
    },
  ];
  return columns;
};

export default useOrderColumns;
export type { OrderOptions };
