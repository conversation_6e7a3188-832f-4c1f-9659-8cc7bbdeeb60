import { apiClient } from '@/api/apiClient';
import type {
  Order,
  ApiResponse,
  PaginatedResponse,
  BackendPaginatedResponse,
  ApiFiltersParams,
} from '@/types';
import { buildApiQueryString } from '@/utils/apiUtils';

/**
 * Real API call to fetch orders
 */
const fetchOrdersFromAPI = async (
  params?: ApiFiltersParams,
): Promise<ApiResponse<PaginatedResponse<Order>>> => {
  try {
    const queryString = buildApiQueryString(params || {});
    const url = queryString ? `/admin/orders?${queryString}` : '/admin/orders';

    console.log('Fetching orders from API (PascalCase parameters):', url);

    const response = await apiClient.get<BackendPaginatedResponse<Order>>(url);

    console.log(response)
    // Transform backend response to frontend format
    const transformedData: PaginatedResponse<Order> = {
      data: response.data.items,
      total: response.data.totalItems,
      page: response.data.pageNumber,
      pageSize: response.data.pageSize,
    };

    return {
      data: transformedData,
      success: true,
      message: 'Orders fetched successfully from API',
    };
  } catch (error) {
    console.error('Error fetching orders from API:', error);
    throw error;
  }
};

/**
 * Main function to fetch orders - switches between mock and real API
 */
export const fetchOrders = async (
  params?: ApiFiltersParams,
): Promise<ApiResponse<PaginatedResponse<Order>>> => {
  return fetchOrdersFromAPI(params);
};

/**
 * Fetch single order by ID from real API
 */
const fetchOrderByIdFromAPI = async (
  id: string,
): Promise<ApiResponse<Order>> => {
  try {
    const response = await apiClient.get<Order>(`/admin/orders/${id}`);

    return {
      data: response.data,
      success: true,
      message: 'Order fetched successfully from API',
    };
  } catch (error) {
    console.error('Error fetching order from API:', error);
    throw error;
  }
};

/**
 * Main function to fetch order by ID - switches between mock and real API
 */
export const fetchOrderById = async (
  id: string,
): Promise<ApiResponse<Order>> => {
  return fetchOrderByIdFromAPI(id);
};

/**
 * Update order status via real API
 */
const updateOrderStatusFromAPI = async (
  orderId: string,
  status: string,
): Promise<ApiResponse<Order>> => {
  try {
    await apiClient.patch(`/admin/orders/${orderId}/status`, {
      orderId: orderId,
      orderStatus: status.toUpperCase(),
    });

    // Since API returns 204 No Content, we need to fetch the updated order
    const updatedOrderResponse = await apiClient.get<Order>(
      `/admin/orders/${orderId}`,
    );

    return {
      data: updatedOrderResponse.data,
      success: true,
      message: 'Order status updated successfully',
    };
  } catch (error) {
    console.error('Error updating order status via API:', error);
    throw error;
  }
};

/**
 * Main function to update order status - switches between mock and real API
 */
export const updateOrderStatus = async (
  orderId: string,
  status: string,
): Promise<ApiResponse<Order>> => {
  return updateOrderStatusFromAPI(orderId, status);
};

// Edit Order API Types
export interface EditOrderRequest {
  orderId: string;
  quantity: number;
  priceOfUnit: number;
  currencyUnit: string;
  orderStatus: string;
  notes: string;
  metadata?: any;
}

/**
 * Edit order via real API
 */
const editOrderFromAPI = async (
  orderId: string,
  orderData: EditOrderRequest,
): Promise<ApiResponse<Order>> => {
  try {
    const response = await apiClient.put<Order>(
      `/admin/orders/${orderId}`,
      orderData,
    );

    return {
      data: response.data,
      success: true,
      message: 'Order updated successfully',
    };
  } catch (error) {
    console.error('Error updating order via API:', error);
    throw error;
  }
};

/**
 * Main function to edit order - switches between mock and real API
 */
export const editOrder = async (
  orderId: string,
  orderData: EditOrderRequest,
): Promise<ApiResponse<Order>> => {
  return editOrderFromAPI(orderId, orderData);
};

// Order Review API Types
export interface VerifyOrderRequest {
  notes?: string;
  password: string;
}

export interface RejectOrderRequest {
  reason: string;
  password: string;
}

/**
 * Verify order via real API
 */
const verifyOrderFromAPI = async (
  orderId: string,
  verifyData: VerifyOrderRequest,
): Promise<ApiResponse<Order>> => {
  try {
    const response = await apiClient.post<Order>(
      `/admin/orders/${orderId}/verify`,
      verifyData,
    );

    return {
      data: response.data,
      success: true,
      message: 'Order verified successfully',
    };
  } catch (error) {
    console.error('Error verifying order via API:', error);
    throw error;
  }
};

/**
 * Reject order via real API
 */
const rejectOrderFromAPI = async (
  orderId: string,
  rejectData: RejectOrderRequest,
): Promise<ApiResponse<Order>> => {
  try {
    const response = await apiClient.post<Order>(
      `/admin/orders/${orderId}/reject`,
      rejectData,
    );

    return {
      data: response.data,
      success: true,
      message: 'Order rejected successfully',
    };
  } catch (error) {
    console.error('Error rejecting order via API:', error);
    throw error;
  }
};

/**
 * Main function to verify order
 */
export const verifyOrder = async (
  orderId: string,
  verifyData: VerifyOrderRequest,
): Promise<ApiResponse<Order>> => {
  return verifyOrderFromAPI(orderId, verifyData);
};

/**
 * Main function to reject order
 */
export const rejectOrder = async (
  orderId: string,
  rejectData: RejectOrderRequest,
): Promise<ApiResponse<Order>> => {
  return rejectOrderFromAPI(orderId, rejectData);
};
