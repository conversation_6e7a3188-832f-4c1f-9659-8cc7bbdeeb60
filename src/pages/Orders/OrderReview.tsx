import React, { useState } from 'react';
import {
  ArrowLeftOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  TeamOutlined,
  DesktopOutlined,
  DollarOutlined,
  CalendarOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Row,
  Typography,
  Space,
  Divider,
  Tag,
  Descriptions,
  Alert,
  Modal,
  Input,
  message,
  Spin,
} from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { useOrder, useVerifyOrder, useRejectOrder } from '@/hooks/useOrders';
import { useResponsive } from '@/hooks/useResponsive';
import { useUserStore } from '@/stores';
import { OrderStatusTag, OrderTypeTag, } from '@/components';
import { formatOrderAmount } from '@/utils/currencyUtils';
import { ROUTES } from '@/constants/routes';
import type { Order } from '@/types';
import CurrencyTag from "@/components/Tags/CurrencyTag.tsx";

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const OrderReview: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation('orders');
  const { isMobile, isTablet } = useResponsive();
  const { isDark } = useUserStore();

  // Local state
  const [verifyModalOpen, setVerifyModalOpen] = useState(false);
  const [rejectModalOpen, setRejectModalOpen] = useState(false);
  const [verifyNotes, setVerifyNotes] = useState('');
  const [rejectReason, setRejectReason] = useState('');

  // Fetch order data
  const { data: orderResponse, isLoading, error } = useOrder(orderId);
  const order = orderResponse?.data;

  // Mutations
  const verifyOrderMutation = useVerifyOrder();
  const rejectOrderMutation = useRejectOrder();

  const handleBack = () => {
    navigate(ROUTES.ORDERS);
  };

  const handleVerifyOrder = async () => {
    if (!order) return;

    try {
      await verifyOrderMutation.mutateAsync({
        orderId: order.orderId,
        verifyData: { notes: verifyNotes },
      });
      setVerifyModalOpen(false);
      setVerifyNotes('');
      // Navigate back after successful verification
      handleBack();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleRejectOrder = async () => {
    if (!order || !rejectReason.trim()) {
      message.error('Please provide a reason for rejection');
      return;
    }

    try {
      await rejectOrderMutation.mutateAsync({
        orderId: order.orderId,
        rejectData: { reason: rejectReason },
      });
      setRejectModalOpen(false);
      setRejectReason('');
      // Navigate back after successful rejection
      handleBack();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return 'orange';
      case 'CONFIRMED':
        return 'blue';
      case 'PROCESSING':
        return 'cyan';
      case 'COMPLETED':
        return 'green';
      case 'CANCELLED':
      case 'REJECTED':
        return 'red';
      default:
        return 'default';
    }
  };

  const canReviewOrder = (order: Order) => {
    // Only allow review for pending orders
    return order.orderStatus === 'PENDING';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spin size="large" />
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <ExclamationCircleOutlined className="text-6xl text-red-500" />
        <Title level={3}>Order Not Found</Title>
        <Text type="secondary">The order you're looking for doesn't exist or has been removed.</Text>
        <Button type="primary" onClick={handleBack}>
          Back to Orders
        </Button>
      </div>
    );
  }

  const isReviewable = canReviewOrder(order);

  return (
    <div className={`space-y-6 ${isMobile ? 'p-4' : 'p-6'}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            className="flex items-center"
          >
            {!isMobile && 'Back to Orders'}
          </Button>
          <div>
            <Title level={2} className="!mb-1">
              Order Review
            </Title>
            <Text type="secondary">
              Review and verify order details before approval
            </Text>
          </div>
        </div>

        {/* Action Buttons */}
        {isReviewable && (
          <Space>
            <Button
              type="primary"
              danger
              icon={<CloseCircleOutlined />}
              onClick={() => setRejectModalOpen(true)}
              size={isMobile ? 'middle' : 'large'}
            >
              {!isMobile && 'Reject'}
            </Button>
            <Button
              type="primary"
              icon={<CheckCircleOutlined />}
              onClick={() => setVerifyModalOpen(true)}
              size={isMobile ? 'middle' : 'large'}
              className="bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700"
            >
              {!isMobile && 'Verify'}
            </Button>
          </Space>
        )}
      </div>

      {/* Status Alert */}
      {!isReviewable && (
        <Alert
          message="Order Review Not Available"
          description="This order cannot be reviewed as it's not in pending status."
          type="info"
          showIcon
          className="mb-6"
        />
      )}

      <Row gutter={[24, 24]}>
        {/* Order Information */}
        <Col xs={24} lg={16}>
          <Card
            title={
              <Space>
                <ShoppingCartOutlined />
                <span>Order Information</span>
              </Space>
            }
            className="h-full"
          >
            <Descriptions
              column={isMobile ? 1 : 2}
              bordered
              size="middle"
              className="mb-6"
            >
              <Descriptions.Item label="Order ID" span={isMobile ? 1 : 2}>
                <Text strong className="text-blue-600">
                  {order.orderCode}
                </Text>
              </Descriptions.Item>
              
              <Descriptions.Item label="UXM Order ID">
                <Text code>{order.uxmOrderId}</Text>
              </Descriptions.Item>
              
              <Descriptions.Item label="Order Type">
                <OrderTypeTag type={order.orderType} />
              </Descriptions.Item>
              
              <Descriptions.Item label="Status">
                <OrderStatusTag status={order.orderStatus} />
              </Descriptions.Item>
              
              <Descriptions.Item label="Currency">
                <CurrencyTag currency={order.currencyUnit} />
              </Descriptions.Item>
              
              <Descriptions.Item label="Quantity">
                <Text strong>{order.quantity.toLocaleString()}</Text>
              </Descriptions.Item>
              
              <Descriptions.Item label="Price per Unit">
                <Text strong>
                  {formatOrderAmount(order.pricePerUnit, order.currencyUnit)}
                </Text>
              </Descriptions.Item>
              
              <Descriptions.Item label="Total Amount">
                <Text strong className="text-lg text-green-600">
                  {formatOrderAmount(order.quantity * order.pricePerUnit, order.currencyUnit)}
                </Text>
              </Descriptions.Item>
              
              <Descriptions.Item label="Created At">
                <Space>
                  <CalendarOutlined />
                  <Text>{dayjs(order.createdAt).format('MMM DD, YYYY HH:mm')}</Text>
                </Space>
              </Descriptions.Item>
              
              <Descriptions.Item label="Updated At">
                <Space>
                  <CalendarOutlined />
                  <Text>{dayjs(order.updatedAt).format('MMM DD, YYYY HH:mm')}</Text>
                </Space>
              </Descriptions.Item>
            </Descriptions>

            {/* Notes Section */}
            {order.notes && (
              <div>
                <Divider orientation="left">
                  <Space>
                    <FileTextOutlined />
                    <span>Order Notes</span>
                  </Space>
                </Divider>
                <Card size="small" className="bg-gray-50">
                  <Paragraph>{order.notes}</Paragraph>
                </Card>
              </div>
            )}
          </Card>
        </Col>

        {/* Related Information */}
        <Col xs={24} lg={8}>
          <Space direction="vertical" size="large" className="w-full">
            {/* User Information */}
            <Card
              title={
                <Space>
                  <UserOutlined />
                  <span>User Information</span>
                </Space>
              }
              size="small"
            >
              <Descriptions column={1} size="small">
                <Descriptions.Item label="User ID">
                  <Text code>{order.userId}</Text>
                </Descriptions.Item>
              </Descriptions>
            </Card>

            {/* Staff Information */}
            <Card
              title={
                <Space>
                  <TeamOutlined />
                  <span>Staff Information</span>
                </Space>
              }
              size="small"
            >
              <Descriptions column={1} size="small">
                <Descriptions.Item label="Staff ID">
                  <Text code>{order.staffId}</Text>
                </Descriptions.Item>
              </Descriptions>
            </Card>

            {/* Counter Information */}
            <Card
              title={
                <Space>
                  <DesktopOutlined />
                  <span>Counter Information</span>
                </Space>
              }
              size="small"
            >
              <Descriptions column={1} size="small">
                <Descriptions.Item label="Counter ID">
                  <Text code>{order.counterId}</Text>
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Space>
        </Col>
      </Row>

      {/* Verify Modal */}
      <Modal
        title={
          <Space>
            <CheckCircleOutlined className="text-green-600" />
            <span>Verify Order</span>
          </Space>
        }
        open={verifyModalOpen}
        onCancel={() => setVerifyModalOpen(false)}
        footer={[
          <Button key="cancel" onClick={() => setVerifyModalOpen(false)}>
            Cancel
          </Button>,
          <Button
            key="verify"
            type="primary"
            loading={verifyOrderMutation.isPending}
            onClick={handleVerifyOrder}
            className="bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700"
          >
            Verify Order
          </Button>,
        ]}
      >
        <div className="space-y-4">
          <Alert
            message="Confirm Order Verification"
            description="Are you sure you want to verify this order? This action will approve the order for processing."
            type="success"
            showIcon
          />
          
          <div>
            <Text strong>Additional Notes (Optional):</Text>
            <TextArea
              rows={3}
              value={verifyNotes}
              onChange={(e) => setVerifyNotes(e.target.value)}
              placeholder="Add any verification notes..."
              className="mt-2"
            />
          </div>
        </div>
      </Modal>

      {/* Reject Modal */}
      <Modal
        title={
          <Space>
            <CloseCircleOutlined className="text-red-600" />
            <span>Reject Order</span>
          </Space>
        }
        open={rejectModalOpen}
        onCancel={() => setRejectModalOpen(false)}
        footer={[
          <Button key="cancel" onClick={() => setRejectModalOpen(false)}>
            Cancel
          </Button>,
          <Button
            key="reject"
            type="primary"
            danger
            loading={rejectOrderMutation.isPending}
            onClick={handleRejectOrder}
          >
            Reject Order
          </Button>,
        ]}
      >
        <div className="space-y-4">
          <Alert
            message="Confirm Order Rejection"
            description="Are you sure you want to reject this order? This action cannot be undone."
            type="error"
            showIcon
          />
          
          <div>
            <Text strong>Reason for Rejection *:</Text>
            <TextArea
              rows={4}
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              placeholder="Please provide a detailed reason for rejecting this order..."
              className="mt-2"
              required
            />
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default OrderReview;
