import {
  ArrowLeftOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  TeamOutlined,
  DesktopOutlined,
  CalendarOutlined,
  FileTextOutlined,
  ExclamationCircleOutlined,
  SafetyOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Row,
  Typography,
  Space,
  Alert,
  Modal,
  Input,
  Spin,
  Form,
} from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { OrderStatusTag, OrderTypeTag } from '@/components';
import CurrencyTag from '@/components/Tags/CurrencyTag.tsx';
import { ROUTES } from '@/constants/routes';
import { useOrder, useVerifyOrder, useRejectOrder } from '@/hooks/useOrders';
import { useOrderNotifications } from '@/hooks';
import { useResponsive } from '@/hooks/useResponsive';
import { useUserStore } from '@/stores';
import type { Order } from '@/types';
import { formatOrderAmount } from '@/utils/currencyUtils';

const { Title, Text, Paragraph } = Typography;
const { TextArea, Password } = Input;

const OrderReview: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation('orderReview');
  const { isMobile } = useResponsive();
  const { isDark } = useUserStore();

  // Local state
  const [verifyModalOpen, setVerifyModalOpen] = useState(false);
  const [rejectModalOpen, setRejectModalOpen] = useState(false);
  const [verifyForm] = Form.useForm();
  const [rejectForm] = Form.useForm();

  // Fetch order data
  const { data: orderResponse, isLoading, error, refetch } = useOrder(orderId!);
  const order = orderResponse?.data;

  // Mutations
  const verifyOrderMutation = useVerifyOrder();
  const rejectOrderMutation = useRejectOrder();

  // Setup order notifications to automatically refetch data
  useOrderNotifications({
    onOrderUpdate: refetch,
    enabled: true,
  });

  const handleBack = () => {
    navigate(ROUTES.ORDERS);
  };

  const handleVerifyOrder = async () => {
    if (!order) return;

    try {
      const values = await verifyForm.validateFields();

      await verifyOrderMutation.mutateAsync({
        orderId: order.orderId,
        verifyData: {
          notes: values.notes,
          password: values.password,
        },
      });

      setVerifyModalOpen(false);
      verifyForm.resetFields();
      // Navigate back after successful verification
      handleBack();
    } catch (error) {
      console.log(error);
      // Error is handled by the mutation or form validation
    }
  };

  const handleRejectOrder = async () => {
    if (!order) return;

    try {
      const values = await rejectForm.validateFields();

      await rejectOrderMutation.mutateAsync({
        orderId: order.orderId,
        rejectData: {
          reason: values.reason,
          password: values.password,
        },
      });

      setRejectModalOpen(false);
      rejectForm.resetFields();
      // Navigate back after successful rejection
      handleBack();
    } catch (error) {
      console.log(error)
      // Error is handled by the mutation or form validation
    }
  };

  const canReviewOrder = (order: Order) => {
    // Only allow review for pending orders
    return order.orderStatus === 'PENDING';
  };

  if (isLoading) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <Spin size='large' />
      </div>
    );
  }

  if (error || !order || !orderId) {
    return (
      <div className='flex flex-col items-center justify-center min-h-[400px] space-y-4'>
        <ExclamationCircleOutlined className='text-6xl text-red-500' />
        <Title level={3}>{t('orderNotFound')}</Title>
        <Text type='secondary'>
          {t('orderNotFoundDescription')}
        </Text>
        <Button type='primary' onClick={handleBack}>
          {t('backToOrders')}
        </Button>
      </div>
    );
  }

  const isReviewable = canReviewOrder(order);

  return (
    <div className={`min-h-screen ${isDark ? 'bg-gray-900' : 'bg-gray-50'}`}>
      <div className={`${isMobile ? 'p-4' : 'p-8'} max-w-7xl mx-auto`}>
        {/* Header */}
        <div className='mb-8'>
          {/* Back Button */}
          <div className='mb-6'>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              className='flex items-center shadow-sm hover:shadow-md transition-shadow'
              size='large'
            >
              {!isMobile && t('backToOrders')}
            </Button>
          </div>

          {/* Title and Actions */}
          <div className='flex items-center justify-between mb-6'>
            <div>
              <Title level={1} className={`!mb-2 ${isDark ? 'text-white' : 'text-gray-900'}`}>
                {t('title')}
              </Title>
              <Text type='secondary' className='text-lg'>
                {t('description')}
              </Text>
            </div>

            {/* Action Buttons */}
            {isReviewable && (
              <Space size='large'>
                <Button
                  type='primary'
                  danger
                  icon={<CloseCircleOutlined />}
                  onClick={() => setRejectModalOpen(true)}
                  size='large'
                  className='h-12 px-8 shadow-lg hover:shadow-xl transition-all duration-300'
                >
                  {!isMobile && t('rejectOrder')}
                </Button>
                <Button
                  type='primary'
                  icon={<CheckCircleOutlined />}
                  onClick={() => setVerifyModalOpen(true)}
                  size='large'
                  className='h-12 px-8 bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700 shadow-lg hover:shadow-xl transition-all duration-300'
                >
                  {!isMobile && t('verifyOrder')}
                </Button>
              </Space>
            )}
          </div>

          {/* Order Status Badge */}
          <div className='flex items-center space-x-4'>
            <Card
              className={`w-fit shadow-sm ${isDark ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'} border`}
            >
              <div className='flex items-center justify-between space-x-6 px-2'>
                <div className='flex items-center space-x-3'>
                  <ShoppingCartOutlined
                    className={`text-2xl ${isDark ? 'text-gray-300' : 'text-gray-600'}`}
                  />
                  <div>
                    <Text
                      strong
                      className={`text-lg ${isDark ? 'text-white' : 'text-gray-900'}`}
                    >
                      {order?.uxmOrderId}
                    </Text>
                    <br />
                    <Text type='secondary' className='text-sm'>
                      {t('orderCode')}
                    </Text>
                  </div>
                </div>
                <div className='text-right'>
                  <OrderStatusTag status={order?.orderStatus || ''} />
                </div>
              </div>
            </Card>
          </div>
        </div>



        <Row gutter={[32, 32]}>
          {/* Main Order Information */}
          <Col xs={24} lg={16}>
            <Card
              className={`shadow-lg rounded-2xl border h-full ${isDark ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'}`}
              title={
                <div
                  className={`flex ${isDark ? 'bg-gray-800' : 'bg-white'} py-8 items-center space-x-3`}
                >
                  <div
                    className={`${isDark ? 'bg-gray-700' : 'bg-gray-100'} p-3 rounded-xl`}
                  >
                    <ShoppingCartOutlined
                      className={`text-2xl ${isDark ? 'text-gray-300' : 'text-gray-600'}`}
                    />
                  </div>
                  <div>
                    <Title
                      level={3}
                      className={`!mb-1 ${isDark ? 'text-white' : 'text-gray-900'}`}
                    >
                      {t('orderInformation')}
                    </Title>
                    <Text type='secondary'>
                      {t('orderInformationDescription')}
                    </Text>
                  </div>
                </div>
              }
              styles={{
                header: {
                  borderBottom: `1px solid ${isDark ? '#374151' : '#f0f0f0'}`,
                  background: isDark ? '#1f2937' : '#fafafa',
                },
              }}
            >
              {/* Order Details Grid */}
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6 mb-8'>
                <div className='col-span-full'>

                  {/* Status Alert */}
                  {!isReviewable && (
                    <Alert
                      message={t('orderReviewNotAvailable')}
                      description={t('orderReviewNotAvailableDescription')}
                      type='warning'
                      showIcon
                      className='mb-4 shadow-sm rounded-lg'
                    />
                  )}
                </div>
                {/* Order ID */}
                <div
                  className={`${isDark ? 'bg-gray-800 border-gray-600' : 'bg-gray-50 border-gray-200'} p-6 rounded-xl border`}
                >
                  <Text
                    type='secondary'
                    className='text-sm font-medium mb-2 block'
                  >
                    {t('orderId')}
                  </Text>
                  <Text
                    strong
                    className={`text-2xl font-mono ${isDark ? 'text-white' : 'text-gray-900'}`}
                  >
                    {order.orderId}
                  </Text>
                </div>

                <div
                  className={`${isDark ? 'bg-gray-800 border-gray-600' : 'bg-gray-50 border-gray-200'} p-6 rounded-xl border`}
                >
                  <Text
                    type='secondary'
                    className='text-sm font-medium mb-2 block'
                  >
                    {t('totalAmount')}
                  </Text>
                  <Text
                    strong
                    className={`text-3xl font-bold ${isDark ? 'text-green-400' : 'text-green-600'}`}
                  >
                    {formatOrderAmount(
                      order.quantity * order.pricePerUnit,
                      order.currencyUnit,
                    )}
                  </Text>
                </div>

                {/* UXM Order ID */}
                <div
                  className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}
                >
                  <Text
                    type='secondary'
                    className='text-sm font-medium mb-2 block'
                  >
                    {t('uxmOrderId')}
                  </Text>
                  <Text code className='text-base'>
                    {order.uxmOrderId}
                  </Text>
                </div>

                {/* Order Type */}
                <div
                  className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}
                >
                  <Text
                    type='secondary'
                    className='text-sm font-medium mb-2 block'
                  >
                    {t('orderType')}
                  </Text>
                  <OrderTypeTag type={order.orderType} />
                </div>

                {/* Status */}
                <div
                  className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}
                >
                  <Text
                    type='secondary'
                    className='text-sm font-medium mb-2 block'
                  >
                    {t('status')}
                  </Text>
                  <OrderStatusTag status={order.orderStatus} />
                </div>

                {/* Currency */}
                <div
                  className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}
                >
                  <Text
                    type='secondary'
                    className='text-sm font-medium mb-2 block'
                  >
                    {t('currency')}
                  </Text>
                  <CurrencyTag currency={order.currencyUnit} />
                </div>

                {/* Quantity */}
                <div
                  className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}
                >
                  <Text
                    type='secondary'
                    className='text-sm font-medium mb-2 block'
                  >
                    {t('quantity')}
                  </Text>
                  <Text strong className='text-lg'>
                    {order.quantity.toLocaleString()}
                  </Text>
                </div>

                {/* Price per Unit */}
                <div
                  className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}
                >
                  <Text
                    type='secondary'
                    className='text-sm font-medium mb-2 block'
                  >
                    {t('pricePerUnit')}
                  </Text>
                  <Text strong className='text-lg'>
                    {formatOrderAmount(order.pricePerUnit, order.currencyUnit)}
                  </Text>
                </div>

                {/* Timestamps */}
                <div
                  className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}
                >
                  <Text
                    type='secondary'
                    className='text-sm font-medium mb-2 block'
                  >
                    {t('createdAt')}
                  </Text>
                  <Space>
                    <CalendarOutlined
                      className={isDark ? 'text-gray-400' : 'text-gray-500'}
                    />
                    <Text>
                      {dayjs(order.createdAt).format('MMM DD, YYYY HH:mm')}
                    </Text>
                  </Space>
                </div>

                <div
                  className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}
                >
                  <Text
                    type='secondary'
                    className='text-sm font-medium mb-2 block'
                  >
                    {t('updatedAt')}
                  </Text>
                  <Space>
                    <CalendarOutlined
                      className={isDark ? 'text-gray-400' : 'text-gray-500'}
                    />
                    <Text>
                      {dayjs(order.updatedAt).format('MMM DD, YYYY HH:mm')}
                    </Text>
                  </Space>
                </div>
              </div>

              {/* Notes Section */}
              {order.notes && (
                <div className='mt-8'>
                  <div className='flex items-center space-x-3 mb-4'>
                    <div
                      className={`${isDark ? 'bg-gray-700' : 'bg-gray-100'} p-2 rounded-lg`}
                    >
                      <FileTextOutlined
                        className={`${isDark ? 'text-gray-300' : 'text-gray-600'}`}
                      />
                    </div>
                    <Title
                      level={4}
                      className={`!mb-0 ${isDark ? 'text-white' : 'text-gray-900'}`}
                    >
                      {t('orderNotes')}
                    </Title>
                  </div>
                  <div
                    className={`${isDark ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'} border p-6 rounded-xl`}
                  >
                    <Paragraph
                      className={`!mb-0 leading-relaxed ${isDark ? 'text-gray-300' : 'text-gray-700'}`}
                    >
                      {order.notes}
                    </Paragraph>
                  </div>
                </div>
              )}
            </Card>
          </Col>

          {/* Related Information Sidebar */}
          <Col xs={24} lg={8}>
            <Space direction='vertical' size='large' className='w-full'>
              {/* User Information */}
              <Card
                className={`shadow-lg rounded-2xl border ${isDark ? 'border-gray-600 bg-gray-800' : 'border-gray-200 bg-white'}`}
                title={
                  <div
                    className={`flex ${isDark ? 'bg-gray-800' : 'bg-white'} py-4 items-center space-x-3`}
                  >
                    <div
                      className={`${isDark ? 'bg-gray-700' : 'bg-gray-100'} p-2 rounded-lg`}
                    >
                      <UserOutlined
                        className={`text-lg ${isDark ? 'text-gray-300' : 'text-gray-600'}`}
                      />
                    </div>
                    <Title
                      level={4}
                      className={`!mb-0 ${isDark ? 'text-white' : 'text-gray-900'}`}
                    >
                      {t('userInformation')}
                    </Title>
                  </div>
                }
                styles={{
                  header: {
                    borderBottom: `1px solid ${isDark ? '#374151' : '#f0f0f0'}`,
                    background: isDark ? '#1f2937' : '#f9fafb',
                  },
                }}
              >
                <div
                  className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-xl`}
                >
                  <Text
                    type='secondary'
                    className='text-sm font-medium mb-2 block'
                  >
                    {t('userId')}
                  </Text>
                  <Text code className='text-base'>
                    {order.userId}
                  </Text>
                </div>
              </Card>

              {/* Staff Information */}
              <Card
                className={`shadow-lg rounded-2xl border ${isDark ? 'border-gray-600 bg-gray-800' : 'border-gray-200 bg-white'}`}
                title={
                  <div
                    className={`flex ${isDark ? 'bg-gray-800' : 'bg-white'} py-4 items-center space-x-3`}
                  >
                    <div
                      className={`${isDark ? 'bg-gray-700' : 'bg-gray-100'} p-2 rounded-lg`}
                    >
                      <TeamOutlined
                        className={`text-lg ${isDark ? 'text-gray-300' : 'text-gray-600'}`}
                      />
                    </div>
                    <Title
                      level={4}
                      className={`!mb-0 ${isDark ? 'text-white' : 'text-gray-900'}`}
                    >
                      {t('staffInformation')}
                    </Title>
                  </div>
                }
                styles={{
                  header: {
                    borderBottom: `1px solid ${isDark ? '#374151' : '#f0f0f0'}`,
                    background: isDark ? '#1f2937' : '#f9fafb',
                  },
                }}
              >
                <div
                  className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-xl`}
                >
                  <Text
                    type='secondary'
                    className='text-sm font-medium mb-2 block'
                  >
                    {t('staffId')}
                  </Text>
                  <Text code className='text-base'>
                    {order.staffId}
                  </Text>
                </div>
              </Card>

              {/* Counter Information */}
              <Card
                className={`shadow-lg rounded-2xl border ${isDark ? 'border-gray-600 bg-gray-800' : 'border-gray-200 bg-white'}`}
                title={
                  <div
                    className={`flex ${isDark ? 'bg-gray-800' : 'bg-white'} py-4 items-center space-x-3`}
                  >
                    <div
                      className={`${isDark ? 'bg-gray-700' : 'bg-gray-100'} p-2 rounded-lg`}
                    >
                      <DesktopOutlined
                        className={`text-lg ${isDark ? 'text-gray-300' : 'text-gray-600'}`}
                      />
                    </div>
                    <Title
                      level={4}
                      className={`!mb-0 ${isDark ? 'text-white' : 'text-gray-900'}`}
                    >
                      {t('counterInformation')}
                    </Title>
                  </div>
                }
                styles={{
                  header: {
                    borderBottom: `1px solid ${isDark ? '#374151' : '#f0f0f0'}`,
                    background: isDark ? '#1f2937' : '#f9fafb',
                  },
                }}
              >
                <div
                  className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-xl`}
                >
                  <Text
                    type='secondary'
                    className='text-sm font-medium mb-2 block'
                  >
                    {t('counterId')}
                  </Text>
                  <Text code className='text-base'>
                    {order.counterId}
                  </Text>
                </div>
              </Card>
            </Space>
          </Col>
        </Row>
      </div>

      {/* Verify Modal */}
      <Modal
        title={
          <div className='flex items-center space-x-3'>
            <div className='bg-green-100 p-2 rounded-lg'>
              <CheckCircleOutlined className='text-green-600 text-xl' />
            </div>
            <div>
              <Title level={4} className='!mb-0'>
                {t('verifyModalTitle')}
              </Title>
              <Text type='secondary' className='text-sm'>
                {t('verifyModalDescription')}
              </Text>
            </div>
          </div>
        }
        open={verifyModalOpen}
        onCancel={() => {
          setVerifyModalOpen(false);
          verifyForm.resetFields();
        }}
        footer={null}
        width={600}
        className='modern-modal'
      >
        <div className='pt-6'>
          <Alert
            message={t('confirmOrderVerification')}
            description={t('confirmOrderVerificationDescription')}
            type='success'
            showIcon
            className='mb-6 rounded-lg'
          />

          <Form
            form={verifyForm}
            layout='vertical'
            onFinish={handleVerifyOrder}
            className='space-y-4'
          >
            <Form.Item
              label={
                <span className='font-medium'>
                  <SafetyOutlined className='mr-2 text-blue-500' />
                  {t('adminPassword')}
                </span>
              }
              name='password'
              rules={[
                { required: true, message: t('adminPasswordRequired') },
                { min: 6, message: t('adminPasswordMinLength') },
              ]}
            >
              <Password
                placeholder={t('adminPasswordPlaceholder')}
                size='large'
                iconRender={(visible) =>
                  visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                }
                className='rounded-lg'
              />
            </Form.Item>

            <Form.Item
              label={
                <span className='font-medium'>
                  <FileTextOutlined className='mr-2 text-blue-500' />
                  {t('additionalNotes')}
                </span>
              }
              name='notes'
            >
              <TextArea
                rows={4}
                placeholder={t('additionalNotesPlaceholder')}
                className='rounded-lg'
              />
            </Form.Item>

            <div className='flex justify-end space-x-3 pt-4 border-t'>
              <Button
                size='large'
                onClick={() => {
                  setVerifyModalOpen(false);
                  verifyForm.resetFields();
                }}
                className='px-6'
              >
                {t('cancel')}
              </Button>
              <Button
                type='primary'
                size='large'
                loading={verifyOrderMutation.isPending}
                htmlType='submit'
                className='px-8 bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700 shadow-lg'
              >
                <CheckCircleOutlined className='mr-2' />
                {t('verify')}
              </Button>
            </div>
          </Form>
        </div>
      </Modal>

      {/* Reject Modal */}
      <Modal
        title={
          <div className='flex items-center space-x-3'>
            <div className='bg-red-100 p-2 rounded-lg'>
              <CloseCircleOutlined className='text-red-600 text-xl' />
            </div>
            <div>
              <Title level={4} className='!mb-0'>
                {t('rejectModalTitle')}
              </Title>
              <Text type='secondary' className='text-sm'>
                {t('rejectModalDescription')}
              </Text>
            </div>
          </div>
        }
        open={rejectModalOpen}
        onCancel={() => {
          setRejectModalOpen(false);
          rejectForm.resetFields();
        }}
        footer={null}
        width={600}
        className='modern-modal'
      >
        <div className='pt-6'>
          <Alert
            message={t('confirmOrderRejection')}
            description={t('confirmOrderRejectionDescription')}
            type='error'
            showIcon
            className='mb-6 rounded-lg'
          />

          <Form
            form={rejectForm}
            layout='vertical'
            onFinish={handleRejectOrder}
            className='space-y-4'
          >
            <Form.Item
              label={
                <span className='font-medium'>
                  <SafetyOutlined className='mr-2 text-blue-500' />
                  {t('adminPassword')}
                </span>
              }
              name='password'
              rules={[
                { required: true, message: t('adminPasswordRequired') },
                { min: 6, message: t('adminPasswordMinLength') },
              ]}
            >
              <Password
                placeholder={t('adminPasswordPlaceholder')}
                size='large'
                iconRender={(visible) =>
                  visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                }
                className='rounded-lg'
              />
            </Form.Item>

            <Form.Item
              label={
                <span className='font-medium'>
                  <ExclamationCircleOutlined className='mr-2 text-red-500' />
                  {t('reasonForRejection')}
                </span>
              }
              name='reason'
              rules={[
                {
                  required: true,
                  message: t('reasonRequired'),
                },
                { min: 10, message: t('reasonMinLength') },
              ]}
            >
              <TextArea
                rows={5}
                placeholder={t('reasonForRejectionPlaceholder')}
                className='rounded-lg'
                showCount
                maxLength={500}
              />
            </Form.Item>

            <div className='flex justify-end space-x-3 pt-4 border-t'>
              <Button
                size='large'
                onClick={() => {
                  setRejectModalOpen(false);
                  rejectForm.resetFields();
                }}
                className='px-6'
              >
                {t('cancel')}
              </Button>
              <Button
                type='primary'
                danger
                size='large'
                loading={rejectOrderMutation.isPending}
                htmlType='submit'
                className='px-8 shadow-lg'
              >
                <CloseCircleOutlined className='mr-2' />
                {t('reject')}
              </Button>
            </div>
          </Form>
        </div>
      </Modal>
    </div>
  );
};

export default OrderReview;
