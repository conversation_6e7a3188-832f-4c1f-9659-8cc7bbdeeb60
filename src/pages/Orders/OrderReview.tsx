import React, { useState } from 'react';
import {
  ArrowLeftOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  TeamOutlined,
  DesktopOutlined,
  DollarOutlined,
  CalendarOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
  LockOutlined,
  SafetyOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Row,
  Typography,
  Space,
  Divider,
  Tag,
  Descriptions,
  Alert,
  Modal,
  Input,
  message,
  Spin,
  Form,
  Badge,
} from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { useOrder, useVerifyOrder, useRejectOrder } from '@/hooks/useOrders';
import { useResponsive } from '@/hooks/useResponsive';
import { useUserStore } from '@/stores';
import { OrderStatusTag, OrderTypeTag, } from '@/components';
import { formatOrderAmount } from '@/utils/currencyUtils';
import { ROUTES } from '@/constants/routes';
import type { Order } from '@/types';
import CurrencyTag from "@/components/Tags/CurrencyTag.tsx";

const { Title, Text, Paragraph } = Typography;
const { TextArea, Password } = Input;

const OrderReview: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation('orders');
  const { isMobile, isTablet } = useResponsive();
  const { isDark } = useUserStore();

  // Local state
  const [verifyModalOpen, setVerifyModalOpen] = useState(false);
  const [rejectModalOpen, setRejectModalOpen] = useState(false);
  const [verifyForm] = Form.useForm();
  const [rejectForm] = Form.useForm();

  // Fetch order data
  const { data: orderResponse, isLoading, error } = useOrder(orderId);
  const order = orderResponse?.data;

  // Mutations
  const verifyOrderMutation = useVerifyOrder();
  const rejectOrderMutation = useRejectOrder();

  const handleBack = () => {
    navigate(ROUTES.ORDERS);
  };

  const handleVerifyOrder = async () => {
    if (!order) return;

    try {
      const values = await verifyForm.validateFields();

      await verifyOrderMutation.mutateAsync({
        orderId: order.orderId,
        verifyData: {
          notes: values.notes,
          password: values.password
        },
      });

      setVerifyModalOpen(false);
      verifyForm.resetFields();
      // Navigate back after successful verification
      handleBack();
    } catch (error) {
      // Error is handled by the mutation or form validation
    }
  };

  const handleRejectOrder = async () => {
    if (!order) return;

    try {
      const values = await rejectForm.validateFields();

      await rejectOrderMutation.mutateAsync({
        orderId: order.orderId,
        rejectData: {
          reason: values.reason,
          password: values.password
        },
      });

      setRejectModalOpen(false);
      rejectForm.resetFields();
      // Navigate back after successful rejection
      handleBack();
    } catch (error) {
      // Error is handled by the mutation or form validation
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return 'orange';
      case 'CONFIRMED':
        return 'blue';
      case 'PROCESSING':
        return 'cyan';
      case 'COMPLETED':
        return 'green';
      case 'CANCELLED':
      case 'REJECTED':
        return 'red';
      default:
        return 'default';
    }
  };

  const canReviewOrder = (order: Order) => {
    // Only allow review for pending orders
    return order.orderStatus === 'PENDING';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spin size="large" />
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <ExclamationCircleOutlined className="text-6xl text-red-500" />
        <Title level={3}>Order Not Found</Title>
        <Text type="secondary">The order you're looking for doesn't exist or has been removed.</Text>
        <Button type="primary" onClick={handleBack}>
          Back to Orders
        </Button>
      </div>
    );
  }

  const isReviewable = canReviewOrder(order);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className={`${isMobile ? 'p-4' : 'p-8'} max-w-7xl mx-auto`}>
        {/* Header */}
        <div className="mb-8">
          {/* Back Button */}
          <div className="mb-6">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              className="flex items-center shadow-sm hover:shadow-md transition-shadow"
              size="large"
            >
              {!isMobile && 'Back to Orders'}
            </Button>
          </div>

          {/* Title and Actions */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <Title level={1} className="!mb-2 text-gray-900">
                Order Review
              </Title>
              <Text type="secondary" className="text-lg">
                Review and verify order details before approval
              </Text>
            </div>

            {/* Action Buttons */}
            {isReviewable && (
              <Space size="large">
                <Button
                  type="primary"
                  danger
                  icon={<CloseCircleOutlined />}
                  onClick={() => setRejectModalOpen(true)}
                  size="large"
                  className="h-12 px-8 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  {!isMobile && 'Reject Order'}
                </Button>
                <Button
                  type="primary"
                  icon={<CheckCircleOutlined />}
                  onClick={() => setVerifyModalOpen(true)}
                  size="large"
                  className="h-12 px-8 bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  {!isMobile && 'Verify Order'}
                </Button>
              </Space>
            )}
          </div>

          {/* Order Status Badge */}
          <div className="flex items-center space-x-4">
            <Badge.Ribbon
              text={order?.orderStatus}
              color={getStatusColor(order?.orderStatus || '')}
              className="text-sm font-medium"
            >
              <Card className="w-fit shadow-sm bg-white">
                <div className="flex items-center space-x-3 px-2">
                  <ShoppingCartOutlined className="text-2xl text-blue-600" />
                  <div>
                    <Text strong className="text-lg">{order?.orderCode}</Text>
                    <br />
                    <Text type="secondary" className="text-sm">Order Code</Text>
                  </div>
                </div>
              </Card>
            </Badge.Ribbon>
          </div>
        </div>

        {/* Status Alert */}
        {!isReviewable && (
          <Alert
            message="Order Review Not Available"
            description="This order cannot be reviewed as it's not in pending status."
            type="info"
            showIcon
            className="mb-8 shadow-sm rounded-lg"
          />
        )}

        <Row gutter={[32, 32]}>
          {/* Main Order Information */}
          <Col xs={24} lg={16}>
            <Card
              className="shadow-lg rounded-2xl border border-gray-200 h-full"
              title={
                <div className="flex items-center space-x-3">
                  <div className={`${isDark ? 'bg-gray-700' : 'bg-gray-100'} p-3 rounded-xl`}>
                    <ShoppingCartOutlined className={`text-2xl ${isDark ? 'text-gray-300' : 'text-gray-600'}`} />
                  </div>
                  <div>
                    <Title level={3} className={`!mb-1 ${isDark ? 'text-white' : 'text-gray-900'}`}>
                      Order Information
                    </Title>
                    <Text type="secondary">
                      Complete order details and transaction information
                    </Text>
                  </div>
                </div>
              }
              headStyle={{
                borderBottom: `1px solid ${isDark ? '#374151' : '#f0f0f0'}`,
                background: isDark ? '#1f2937' : '#fafafa'
              }}
            >
              {/* Order Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                {/* Order ID */}
                <div className={`${isDark ? 'bg-gray-800 border-gray-600' : 'bg-gray-50 border-gray-200'} p-6 rounded-xl border`}>
                  <Text type="secondary" className="text-sm font-medium mb-2 block">Order ID</Text>
                  <Text strong className={`text-2xl font-mono ${isDark ? 'text-white' : 'text-gray-900'}`}>
                    {order.orderId}
                  </Text>
                </div>

                {/* Order Code */}
                <div className={`${isDark ? 'bg-gray-800 border-gray-600' : 'bg-gray-50 border-gray-200'} p-6 rounded-xl border`}>
                  <Text type="secondary" className="text-sm font-medium mb-2 block">Order Code</Text>
                  <Text strong className={`text-2xl font-mono ${isDark ? 'text-white' : 'text-gray-900'}`}>
                    {order.orderCode}
                  </Text>
                </div>

                  {/* UXM Order ID */}
                  <div className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}>
                    <Text type="secondary" className="text-sm font-medium mb-2 block">UXM Order ID</Text>
                    <Text code className="text-base">{order.uxmOrderId}</Text>
                  </div>

                  {/* Order Type */}
                  <div className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}>
                    <Text type="secondary" className="text-sm font-medium mb-2 block">Order Type</Text>
                    <OrderTypeTag type={order.orderType} />
                  </div>

                  {/* Status */}
                  <div className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}>
                    <Text type="secondary" className="text-sm font-medium mb-2 block">Status</Text>
                    <OrderStatusTag status={order.orderStatus} />
                  </div>

                  {/* Currency */}
                  <div className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}>
                    <Text type="secondary" className="text-sm font-medium mb-2 block">Currency</Text>
                    <CurrencyTag currency={order.currencyUnit} />
                  </div>

                  {/* Quantity */}
                  <div className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}>
                    <Text type="secondary" className="text-sm font-medium mb-2 block">Quantity</Text>
                    <Text strong className="text-lg">{order.quantity.toLocaleString()}</Text>
                  </div>

                  {/* Price per Unit */}
                  <div className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}>
                    <Text type="secondary" className="text-sm font-medium mb-2 block">Price per Unit</Text>
                    <Text strong className="text-lg">
                      {formatOrderAmount(order.pricePerUnit, order.currencyUnit)}
                    </Text>
                  </div>

                  {/* Total Amount */}
                  <div className="col-span-full">
                    <div className={`${isDark ? 'bg-gray-800 border-gray-600' : 'bg-gray-50 border-gray-200'} p-6 rounded-xl border`}>
                      <Text type="secondary" className="text-sm font-medium mb-2 block">Total Amount</Text>
                      <Text strong className={`text-3xl font-bold ${isDark ? 'text-green-400' : 'text-green-600'}`}>
                        {formatOrderAmount(order.quantity * order.pricePerUnit, order.currencyUnit)}
                      </Text>
                    </div>
                  </div>

                  {/* Timestamps */}
                  <div className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}>
                    <Text type="secondary" className="text-sm font-medium mb-2 block">Created At</Text>
                    <Space>
                      <CalendarOutlined className={isDark ? 'text-gray-400' : 'text-gray-500'} />
                      <Text>{dayjs(order.createdAt).format('MMM DD, YYYY HH:mm')}</Text>
                    </Space>
                  </div>

                  <div className={`${isDark ? 'bg-gray-800' : 'bg-gray-50'} p-4 rounded-xl`}>
                    <Text type="secondary" className="text-sm font-medium mb-2 block">Updated At</Text>
                    <Space>
                      <CalendarOutlined className={isDark ? 'text-gray-400' : 'text-gray-500'} />
                      <Text>{dayjs(order.updatedAt).format('MMM DD, YYYY HH:mm')}</Text>
                    </Space>
                  </div>
                </div>

                {/* Notes Section */}
                {order.notes && (
                  <div className="mt-8">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="bg-blue-100 p-2 rounded-lg">
                        <FileTextOutlined className="text-blue-600" />
                      </div>
                      <Title level={4} className="!mb-0">Order Notes</Title>
                    </div>
                    <div className="bg-amber-50 border border-amber-200 p-6 rounded-xl">
                      <Paragraph className="!mb-0 text-gray-700 leading-relaxed">
                        {order.notes}
                      </Paragraph>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </Col>

          {/* Related Information Sidebar */}
          <Col xs={24} lg={8}>
            <Space direction="vertical" size="large" className="w-full">
              {/* User Information */}
              <Card
                className={`shadow-lg rounded-2xl border ${isDark ? 'border-gray-600 bg-gray-800' : 'border-gray-200 bg-white'}`}
                title={
                  <div className="flex items-center space-x-3">
                    <div className={`${isDark ? 'bg-gray-700' : 'bg-gray-100'} p-2 rounded-lg`}>
                      <UserOutlined className={`text-lg ${isDark ? 'text-gray-300' : 'text-gray-600'}`} />
                    </div>
                    <Title level={4} className={`!mb-0 ${isDark ? 'text-white' : 'text-gray-900'}`}>
                      User Information
                    </Title>
                  </div>
                }
                headStyle={{
                  borderBottom: `1px solid ${isDark ? '#374151' : '#f0f0f0'}`,
                  background: isDark ? '#1f2937' : '#f9fafb'
                }}
              >
                <div className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-xl`}>
                  <Text type="secondary" className="text-sm font-medium mb-2 block">User ID</Text>
                  <Text code className="text-base">{order.userId}</Text>
                </div>
              </Card>

              {/* Staff Information */}
              <Card
                className={`shadow-lg rounded-2xl border ${isDark ? 'border-gray-600 bg-gray-800' : 'border-gray-200 bg-white'}`}
                title={
                  <div className="flex items-center space-x-3">
                    <div className={`${isDark ? 'bg-gray-700' : 'bg-gray-100'} p-2 rounded-lg`}>
                      <TeamOutlined className={`text-lg ${isDark ? 'text-gray-300' : 'text-gray-600'}`} />
                    </div>
                    <Title level={4} className={`!mb-0 ${isDark ? 'text-white' : 'text-gray-900'}`}>
                      Staff Information
                    </Title>
                  </div>
                }
                headStyle={{
                  borderBottom: `1px solid ${isDark ? '#374151' : '#f0f0f0'}`,
                  background: isDark ? '#1f2937' : '#f9fafb'
                }}
              >
                <div className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-xl`}>
                  <Text type="secondary" className="text-sm font-medium mb-2 block">Staff ID</Text>
                  <Text code className="text-base">{order.staffId}</Text>
                </div>
              </Card>

              {/* Counter Information */}
              <Card
                className={`shadow-lg rounded-2xl border ${isDark ? 'border-gray-600 bg-gray-800' : 'border-gray-200 bg-white'}`}
                title={
                  <div className="flex items-center space-x-3">
                    <div className={`${isDark ? 'bg-gray-700' : 'bg-gray-100'} p-2 rounded-lg`}>
                      <DesktopOutlined className={`text-lg ${isDark ? 'text-gray-300' : 'text-gray-600'}`} />
                    </div>
                    <Title level={4} className={`!mb-0 ${isDark ? 'text-white' : 'text-gray-900'}`}>
                      Counter Information
                    </Title>
                  </div>
                }
                headStyle={{
                  borderBottom: `1px solid ${isDark ? '#374151' : '#f0f0f0'}`,
                  background: isDark ? '#1f2937' : '#f9fafb'
                }}
              >
                <div className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-4 rounded-xl`}>
                  <Text type="secondary" className="text-sm font-medium mb-2 block">Counter ID</Text>
                  <Text code className="text-base">{order.counterId}</Text>
                </div>
              </Card>
            </Space>
          </Col>
        </Row>
      </div>

      {/* Verify Modal */}
      <Modal
        title={
          <div className="flex items-center space-x-3">
            <div className="bg-green-100 p-2 rounded-lg">
              <CheckCircleOutlined className="text-green-600 text-xl" />
            </div>
            <div>
              <Title level={4} className="!mb-0">Verify Order</Title>
              <Text type="secondary" className="text-sm">Approve this order for processing</Text>
            </div>
          </div>
        }
        open={verifyModalOpen}
        onCancel={() => {
          setVerifyModalOpen(false);
          verifyForm.resetFields();
        }}
        footer={null}
        width={600}
        className="modern-modal"
      >
        <div className="pt-6">
          <Alert
            message="Confirm Order Verification"
            description="Are you sure you want to verify this order? This action will approve the order for processing."
            type="success"
            showIcon
            className="mb-6 rounded-lg"
          />

          <Form
            form={verifyForm}
            layout="vertical"
            onFinish={handleVerifyOrder}
            className="space-y-4"
          >
            <Form.Item
              label={
                <span className="font-medium">
                  <SafetyOutlined className="mr-2 text-blue-500" />
                  Admin Password
                </span>
              }
              name="password"
              rules={[
                { required: true, message: 'Please enter your admin password' },
                { min: 6, message: 'Password must be at least 6 characters' }
              ]}
            >
              <Password
                placeholder="Enter your admin password"
                size="large"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                className="rounded-lg"
              />
            </Form.Item>

            <Form.Item
              label={
                <span className="font-medium">
                  <FileTextOutlined className="mr-2 text-blue-500" />
                  Additional Notes (Optional)
                </span>
              }
              name="notes"
            >
              <TextArea
                rows={4}
                placeholder="Add any verification notes or comments..."
                className="rounded-lg"
              />
            </Form.Item>

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button
                size="large"
                onClick={() => {
                  setVerifyModalOpen(false);
                  verifyForm.resetFields();
                }}
                className="px-6"
              >
                Cancel
              </Button>
              <Button
                type="primary"
                size="large"
                loading={verifyOrderMutation.isPending}
                htmlType="submit"
                className="px-8 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 border-0 shadow-lg"
              >
                <CheckCircleOutlined className="mr-2" />
                Verify Order
              </Button>
            </div>
          </Form>
        </div>
      </Modal>

      {/* Reject Modal */}
      <Modal
        title={
          <div className="flex items-center space-x-3">
            <div className="bg-red-100 p-2 rounded-lg">
              <CloseCircleOutlined className="text-red-600 text-xl" />
            </div>
            <div>
              <Title level={4} className="!mb-0">Reject Order</Title>
              <Text type="secondary" className="text-sm">Decline this order with reason</Text>
            </div>
          </div>
        }
        open={rejectModalOpen}
        onCancel={() => {
          setRejectModalOpen(false);
          rejectForm.resetFields();
        }}
        footer={null}
        width={600}
        className="modern-modal"
      >
        <div className="pt-6">
          <Alert
            message="Confirm Order Rejection"
            description="Are you sure you want to reject this order? This action cannot be undone."
            type="error"
            showIcon
            className="mb-6 rounded-lg"
          />

          <Form
            form={rejectForm}
            layout="vertical"
            onFinish={handleRejectOrder}
            className="space-y-4"
          >
            <Form.Item
              label={
                <span className="font-medium">
                  <SafetyOutlined className="mr-2 text-blue-500" />
                  Admin Password
                </span>
              }
              name="password"
              rules={[
                { required: true, message: 'Please enter your admin password' },
                { min: 6, message: 'Password must be at least 6 characters' }
              ]}
            >
              <Password
                placeholder="Enter your admin password"
                size="large"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                className="rounded-lg"
              />
            </Form.Item>

            <Form.Item
              label={
                <span className="font-medium">
                  <ExclamationCircleOutlined className="mr-2 text-red-500" />
                  Reason for Rejection *
                </span>
              }
              name="reason"
              rules={[
                { required: true, message: 'Please provide a reason for rejection' },
                { min: 10, message: 'Reason must be at least 10 characters' }
              ]}
            >
              <TextArea
                rows={5}
                placeholder="Please provide a detailed reason for rejecting this order..."
                className="rounded-lg"
                showCount
                maxLength={500}
              />
            </Form.Item>

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button
                size="large"
                onClick={() => {
                  setRejectModalOpen(false);
                  rejectForm.resetFields();
                }}
                className="px-6"
              >
                Cancel
              </Button>
              <Button
                type="primary"
                danger
                size="large"
                loading={rejectOrderMutation.isPending}
                htmlType="submit"
                className="px-8 shadow-lg"
              >
                <CloseCircleOutlined className="mr-2" />
                Reject Order
              </Button>
            </div>
          </Form>
        </div>
      </Modal>
    </div>
  );
};

export default OrderReview;
