import {
  CheckCircleOutlined,
  ClearOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  FilterOutlined,
  ReloadOutlined,
  SearchOutlined,
  ShoppingCartOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  DatePicker,
  Row,
  Select,
  Statistic,
  Typography,
} from 'antd';
import Search from 'antd/es/input/Search';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import useOrderColumns from './components/useOrderColumns';
import { Txt } from '@/components';
import GalaxyTable from '@/components/WeAreTableGalaxy/GalaxyTable';
import {
  useOrdersWithFilters,
  useUpdateOrderStatus,
} from '@/hooks/useOrders.ts';
import { useResponsive } from '@/hooks/useResponsive.ts';
import EditOrderModal from '@/pages/Orders/components/EditOrderModal';
import OrderDetailsModal from '@/pages/Orders/components/OrderDetailsModal';
import { useUserStore } from '@/stores';
import type { Order } from '@/types';
import {
  CurrencyCodeEnums,
  OrderStatusEnums,
  OrderTypeEnums,
} from '@/utils/enums';

const { Option } = Select;
const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

const Orders: React.FC = () => {
  const { t } = useTranslation('orders');
  const { isMobile } = useResponsive();
  const { isDark } = useUserStore();
  const navigate = useNavigate();

  // Local state for UI only
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // UI input states (separate from API filter logic)
  const [statusValue, setStatusValue] = useState<string | undefined>();
  const [orderTypeValue, setOrderTypeValue] = useState<string | undefined>();
  const [currencyValue, setCurrencyValue] = useState<string | undefined>();
  const [userSearchValue, setUserSearchValue] = useState<string>('');
  const [counterSearchValue, setCounterSearchValue] = useState<string>('');
  const [staffSearchValue, setStaffSearchValue] = useState<string>('');
  const [dateRangeValue, setDateRangeValue] = useState<any>(null);

  const {
    data: ordersResponse,
    isLoading,
    refetch,
    isRefetching,
    // Filter methods
    filterByStatus,
    filterByOrderType,
    filterByUser,
    filterByCounter,
    filterByStaff,
    filterByCurrency,
    filterByDateRange,
    // Clear methods
    clearStatusFilter,
    clearOrderTypeFilter,
    clearUserFilter,
    clearCounterFilter,
    clearStaffFilter,
    clearCurrencyFilter,
    clearDateFilter,
    clearFilters,
    hasActiveFiltering,
    // Pagination
    updatePage,
    updatePageSize,
    page,
    pageSize,
  } = useOrdersWithFilters();

  const updateOrderStatusMutation = useUpdateOrderStatus();
  const isLoadChangeStt = updateOrderStatusMutation.isPending;

  // Filter handlers - manage both UI state and API filters
  const handleStatusFilter = (value: string) => {
    setStatusValue(value);
    if (value) {
      filterByStatus(value);
    } else {
      clearStatusFilter();
    }
  };

  const handleOrderTypeFilter = (value: string) => {
    setOrderTypeValue(value);
    if (value) {
      filterByOrderType(value);
    } else {
      clearOrderTypeFilter();
    }
  };

  const handleCurrencyFilter = (value: string) => {
    setCurrencyValue(value);
    if (value) {
      filterByCurrency(value);
    } else {
      clearCurrencyFilter();
    }
  };

  const handleUserSearch = (value: string) => {
    setUserSearchValue(value);
    if (value.trim()) {
      filterByUser(value.trim());
    } else {
      clearUserFilter();
    }
  };

  const handleCounterFilter = (value: string) => {
    setCounterSearchValue(value);
    if (value.trim()) {
      filterByCounter(value.trim());
    } else {
      clearCounterFilter();
    }
  };

  const handleStaffSearch = (value: string) => {
    setStaffSearchValue(value);
    if (value.trim()) {
      filterByStaff(value.trim());
    } else {
      clearStaffFilter();
    }
  };

  const handleDateRangeFilter = (dates: any) => {
    setDateRangeValue(dates);
    if (dates && dates.length === 2) {
      const [startDate, endDate] = dates;
      filterByDateRange(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
      );
    } else {
      clearDateFilter();
    }
  };

  const handleClearAllFilters = () => {
    // Clear API filters
    clearFilters();

    // Clear UI input states
    setStatusValue(undefined);
    setOrderTypeValue(undefined);
    setCurrencyValue(undefined);
    setUserSearchValue('');
    setCounterSearchValue('');
    setStaffSearchValue('');
    setDateRangeValue(null);

    setShowAdvancedFilters(false);
  };

  // Modal handlers
  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowDetailsModal(true);
  };

  const handleEditOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowEditModal(true);
  };

  const handleReviewOrder = (order: Order) => {
    navigate(`/orders/review/${order.orderId}`);
  };

  const handleCloseDetailsModal = () => {
    setShowDetailsModal(false);
    setSelectedOrder(null);
  };

  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setSelectedOrder(null);
  };

  // Pagination handler
  const handlePaginationChange = (newPage: number, newPageSize: number) => {
    updatePage(newPage);
    if (newPageSize !== pageSize) {
      updatePageSize(newPageSize);
    }
  };

  const handleStatusUpdate = async (orderId: number, newStatus: string) => {
    try {
      await updateOrderStatusMutation.mutateAsync({
        orderId: orderId,
        status: newStatus,
      });
      await refetch();
    } catch (error) {
      console.log(error);
      // Error is handled by the mutation
    }
  };

  const columns = useOrderColumns({
    onViewO: (order) => {
      handleViewOrder(order);
    },
    onEdit: (order) => {
      handleEditOrder(order);
    },
    onReview: (order) => {
      handleReviewOrder(order);
    },
    onUpdateStt: (orderId, status) => {
      handleStatusUpdate(orderId, status);
    },
    isLoadChangeStt,
  });

  const orders = ordersResponse?.data?.data || [];
  const total = ordersResponse?.data?.total || 0;
  const completedOrders = orders.filter(
    (o) => o.orderStatus === OrderStatusEnums.Completed,
  ).length;
  const pendingOrders = orders.filter(
    (o) => o.orderStatus === OrderStatusEnums.Pending,
  ).length;
  const totalRevenue = orders
    .filter((o) => o.orderStatus === OrderStatusEnums.Completed)
    .reduce((sum, o) => sum + o.quantity * o.pricePerUnit, 0);

  return (
    <div
      className={`space-y-4 ${isMobile ? 'sm:space-y-4' : 'sm:space-y-6 lg:space-y-8'}`}
    >
      {/* Header Section */}
      <div
        className={`rounded-xl p-4 md:rounded-2xl md:p-6 lg:p-8 bg-gradient-to-r ${
          isDark
            ? 'from-green-700 via-green-800 to-green-900'
            : 'from-green-100 via-green-200 to-green-300'
        }`}
      >
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-3 sm:gap-4'>
            <div className='w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0'>
              <ShoppingCartOutlined className='!text-white text-lg md:text-xl' />
            </div>
            <div>
              <Title level={2}>{t('title')}</Title>
              <Txt>{t('description')}</Txt>
            </div>
          </div>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => refetch()}
            loading={isLoading}
            size={isMobile ? 'middle' : 'large'}
            className={`rounded-lg ${isMobile ? 'h-10 px-4 w-full' : 'h-12 px-6'} font-medium shadow-lg hover:shadow-xl transition-all`}
          >
            {t('refresh')}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <Row
        gutter={[
          { xs: 12, sm: 16, lg: 24 },
          { xs: 12, sm: 16, lg: 24 },
        ]}
      >
        <Col xs={24} sm={6}>
          <Card
            className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} `}
          >
            <Statistic
              title={
                <span className={isDark ? 'text-white' : 'text-black'}>
                  {t('totalOrders')}
                </span>
              }
              value={total}
              prefix={<ShoppingCartOutlined className='text-primary' />}
              valueStyle={{
                color: '#1890ff',
                fontSize: isMobile ? '20px' : '28px',
                fontWeight: 'bold',
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card
            className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} `}
          >
            <Statistic
              title={
                <span className={isDark ? 'text-white' : 'text-black'}>
                  {t('completed')}
                </span>
              }
              value={completedOrders}
              prefix={<CheckCircleOutlined className='text-green-500' />}
              valueStyle={{
                color: '#52c41a',
                fontSize: isMobile ? '20px' : '28px',
                fontWeight: 'bold',
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card
            className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} `}
          >
            <Statistic
              title={
                <span className={isDark ? 'text-white' : 'text-black'}>
                  {t('pending')}
                </span>
              }
              value={pendingOrders}
              prefix={<ClockCircleOutlined className='text-orange-500' />}
              valueStyle={{
                color: '#faad14',
                fontSize: isMobile ? '20px' : '28px',
                fontWeight: 'bold',
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card
            className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} `}
          >
            <Statistic
              title={
                <span className={isDark ? 'text-white' : 'text-black'}>
                  {t('revenue')}
                </span>
              }
              value={totalRevenue}
              prefix={<DollarOutlined className='text-purple-500' />}
              valueStyle={{
                color: '#e15ae1',
                fontSize: isMobile ? '20px' : '28px',
                fontWeight: 'bold',
              }}
              formatter={(value) => `$${Number(value).toLocaleString()}`}
            />
          </Card>
        </Col>
      </Row>

      {/* Search and Filters */}
      <Card className='bg-gradient-to-r from-blue-50 to-indigo-50 border-0 shadow-lg rounded-2xl overflow-hidden'>
        <div className='p-6'>
          {/* Header Section */}
          <div className='flex items-center justify-between mb-6'>
            <div className='flex items-center gap-4'>
              <div
                className={`p-3 h-fit ${isDark ? 'bg-blue-800' : 'bg-blue-100'} rounded-xl`}
              >
                <SearchOutlined className='!text-xl' />
              </div>
              <div>
                <Title level={4}>Search & Filter Orders</Title>
                <Text className={isDark ? '!text-gray-500' : '!text-gray-600'}>
                  Find orders using multiple criteria
                </Text>
              </div>
            </div>

            <div className='flex items-center gap-3'>
              {hasActiveFiltering && (
                <Button
                  icon={<ClearOutlined />}
                  onClick={handleClearAllFilters}
                  className='bg-red-50 border-red-200 text-red-600 hover:bg-red-100 hover:border-red-300'
                  size='large'
                >
                  Clear All
                </Button>
              )}
              <Button
                icon={<FilterOutlined />}
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                type={showAdvancedFilters ? 'primary' : 'default'}
                size='large'
                className={
                  showAdvancedFilters
                    ? 'bg-blue-600'
                    : 'bg-white border-gray-300 hover:border-blue-400'
                }
              >
                {showAdvancedFilters ? 'Hide Filters' : 'More Filters'}
              </Button>
            </div>
          </div>

          {/* Quick Filters */}
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6'>
            {/* Status Filter */}
            <div className='space-y-2'>
              <Text className='text-sm font-medium text-gray-700'>
                Order Status
              </Text>
              <Select
                placeholder='All Statuses'
                allowClear
                value={statusValue}
                onChange={handleStatusFilter}
                size='large'
                className='w-full'
                style={{ borderRadius: '12px' }}
              >
                <Option value={OrderStatusEnums.Pending}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-orange-400 rounded-full'></div>
                    Pending
                  </div>
                </Option>
                <Option value={OrderStatusEnums.Confirmed}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-blue-400 rounded-full'></div>
                    Confirmed
                  </div>
                </Option>
                <Option value={OrderStatusEnums.Processing}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-purple-400 rounded-full'></div>
                    Processing
                  </div>
                </Option>
                <Option value={OrderStatusEnums.Completed}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-green-400 rounded-full'></div>
                    Completed
                  </div>
                </Option>
                <Option value={OrderStatusEnums.Cancelled}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-gray-400 rounded-full'></div>
                    Cancelled
                  </div>
                </Option>
                <Option value={OrderStatusEnums.Rejected}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-red-400 rounded-full'></div>
                    Rejected
                  </div>
                </Option>
              </Select>
            </div>

            {/* Order Type Filter */}
            <div className='space-y-2'>
              <Text className='text-sm font-medium text-gray-700'>
                Order Type
              </Text>
              <Select
                placeholder='All Types'
                allowClear
                value={orderTypeValue}
                onChange={handleOrderTypeFilter}
                size='large'
                className='w-full'
                style={{ borderRadius: '12px' }}
              >
                <Option value={OrderTypeEnums.Buy}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-green-400 rounded-full'></div>
                    Buy
                  </div>
                </Option>
                <Option value={OrderTypeEnums.Sell}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-red-400 rounded-full'></div>
                    Sell
                  </div>
                </Option>
              </Select>
            </div>

            {/* Currency Filter */}
            <div className='space-y-2'>
              <Text className='text-sm font-medium text-gray-700'>
                Currency
              </Text>
              <Select
                placeholder='All Currencies'
                allowClear
                value={currencyValue}
                onChange={handleCurrencyFilter}
                size='large'
                className='w-full'
                style={{ borderRadius: '12px' }}
              >
                {Object.values(CurrencyCodeEnums).map((currency) => (
                  <Option key={currency} value={currency}>
                    <div className='flex items-center gap-2'>
                      <span className='font-mono text-xs bg-gray-100 px-2 py-1 rounded'>
                        {currency}
                      </span>
                    </div>
                  </Option>
                ))}
              </Select>
            </div>

            {/* User Search */}
            <div className='space-y-2'>
              <Text className='text-sm font-medium text-gray-700'>User ID</Text>
              <Search
                placeholder='Search user...'
                allowClear
                value={userSearchValue}
                onChange={(e) => setUserSearchValue(e.target.value)}
                onSearch={handleUserSearch}
                size='large'
                className='w-full'
                style={{ borderRadius: '12px' }}
              />
            </div>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className='border-t border-gray-200 pt-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                {/* Counter Search */}
                <div className='space-y-2'>
                  <Text className='text-sm font-medium text-gray-700'>
                    Counter ID
                  </Text>
                  <Search
                    placeholder='Search counter...'
                    allowClear
                    value={counterSearchValue}
                    onChange={(e) => setCounterSearchValue(e.target.value)}
                    onSearch={handleCounterFilter}
                    size='large'
                    className='w-full'
                    style={{ borderRadius: '12px' }}
                  />
                </div>

                {/* Staff Search */}
                <div className='space-y-2'>
                  <Text className='text-sm font-medium text-gray-700'>
                    Staff ID
                  </Text>
                  <Search
                    placeholder='Search staff...'
                    allowClear
                    value={staffSearchValue}
                    onChange={(e) => setStaffSearchValue(e.target.value)}
                    onSearch={handleStaffSearch}
                    size='large'
                    className='w-full'
                    style={{ borderRadius: '12px' }}
                  />
                </div>

                {/* Date Range - spans both columns */}
                <div className='space-y-2 md:col-span-2'>
                  <Text className='text-sm font-medium text-gray-700'>
                    Date Range
                  </Text>
                  <RangePicker
                    placeholder={['Start Date', 'End Date']}
                    value={dateRangeValue}
                    onChange={handleDateRangeFilter}
                    size='large'
                    className='w-full'
                    style={{ borderRadius: '12px' }}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
      {/* Orders Table */}
      <Card
        className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card overflow-hidden`}
      >
        <GalaxyTable
          data={orders}
          columns={columns}
          loading={isLoading || isRefetching}
          customSort={true}
          pagination={{
            current: page,
            pageSize: pageSize,
            total,
            onChange: handlePaginationChange,
            showSizeChanger: !isMobile,
            showQuickJumper: !isMobile,
            showTotal: !isMobile
              ? (total, range) =>
                  `${range[0]}-${range[1]} ${t('of')} ${total} ${t('items')}`
              : undefined,
          }}
          rowKey='orderCode'
          scroll={isMobile ? { x: 1200 } : undefined}
        />
      </Card>

      {/* Order Details Modal */}
      <OrderDetailsModal
        visible={showDetailsModal}
        order={selectedOrder}
        onClose={handleCloseDetailsModal}
        onEdit={(order) => {
          setSelectedOrder(order);
          setShowDetailsModal(false);
          setShowEditModal(true);
        }}
        onUpdateStatus={(orderId, status) => {
          handleStatusUpdate(orderId, status);
        }}
      />

      {/* Edit Order Modal */}
      <EditOrderModal
        visible={showEditModal}
        order={selectedOrder}
        onCancel={handleCloseEditModal}
      />
    </div>
  );
};

export default Orders;
