import { apiClient } from '@/api';
import { queryKeys } from '@/constants';
import { useMasterQuery, type UseMasterQueryProps } from '@/hooks';
import type { OrderStatusEnums, OrderTypeEnums } from '@/utils';

type DashboardSummaryProps = unknown;

interface RecentOrder {
  ownerName: string;
  price: number;
  orderType: OrderTypeEnums;
  status: OrderStatusEnums;
  createdAt: string;
}

interface NewMember {
  memberId: string;
  memberName: string;
  passportNumber: string;
  isActive: boolean;
  createdAt: string;
}

interface DashboardSummaryRes {
  totalMembers: number;
  recentOrderCount: number;
  pendingTransaction: number;
  totalRevenue: number;
  recentOrders: Array<RecentOrder>;
  newMembers: Array<NewMember>;
}

type Other = unknown;

const useDashboardSummary = (
  useProps: UseMasterQueryProps<
    Other,
    DashboardSummaryProps,
    DashboardSummaryRes
  >,
) => {
  const { params, ...config } = useProps;

  const query = useMasterQuery<DashboardSummaryRes, DashboardSummaryProps>({
    ...config,
    queryKey: queryKeys.dashboard.summary,
    qf: () => {
      const request = apiClient
        .get('/admin/dashboard')
        .then(({ data }) => data);
      return request;
    },
  });
  return { ...query };
};

export { useDashboardSummary };
export type { RecentOrder, NewMember };
