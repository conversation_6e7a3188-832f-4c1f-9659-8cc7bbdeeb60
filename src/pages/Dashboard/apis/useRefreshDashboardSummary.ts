import { apiClient } from '@/api';
import { useMasterMutation, type UseMasterQueryProps } from '@/hooks';

type RefreshDashboardSummaryProps = unknown;

type RefreshDashboardSummaryRes = unknown;

type Other = unknown;

const useRefreshDashboardSummary = (
  useProps: UseMasterQueryProps<
    Other,
    RefreshDashboardSummaryProps,
    RefreshDashboardSummaryRes
  >,
) => {
  const { params, ...config } = useProps;

  const query = useMasterMutation<
    RefreshDashboardSummaryRes,
    RefreshDashboardSummaryProps
  >({
    ...config,
    mutationFn: () => {
      const request = apiClient
        .post('/admin/dashboard/refresh')
        .then(({ data }) => data);
      return request;
    },
  });
  return { ...query };
};

export { useRefreshDashboardSummary };
