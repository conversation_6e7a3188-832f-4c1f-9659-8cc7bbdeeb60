import React from 'react';
import {
  ShoppingCartOutlined,
  AuditOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  UserOutlined,
  ArrowRightOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import {
  Avatar,
  Card,
  Col,
  Flex,
  List,
  Button,
  Badge,
  Typography,
  Space,
  Empty,
  Tooltip
} from 'antd';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useOrders } from '@/hooks/useOrders';
import { OrderStatusTag, OrderTypeTag } from '@/components';
import Txt from '@/components/Txt';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive';
import { useUserStore } from '@/stores';
import { formatOrderAmount } from '@/utils/currencyUtils';
import type { Order } from '@/types';

dayjs.extend(relativeTime);

const { Title, Text } = Typography;

interface RecentOrdersProps {
  loading?: boolean;
}

const RecentOrders = (props: RecentOrdersProps) => {
  const { loading = false } = props || {};

  const { t } = useTranslation('recentOrders');
  const { isMobile } = useResponsive();
  const { isDark } = useUserStore();
  const navigate = useNavigate();

  // Fetch orders that need review (PENDING status)
  const { data: ordersResponse, isLoading } = useOrders({});

  const pendingOrders = ordersResponse?.data?.data || [];
  const handleOrderClick = (order: Order) => {
    navigate(`/orders/review/${order.orderId}`);
  };

  const getOrderPriorityColor = (createdAt: string) => {
    const hoursAgo = dayjs().diff(dayjs(createdAt), 'hour');
    if (hoursAgo > 24) return 'red';
    if (hoursAgo > 12) return 'orange';
    if (hoursAgo > 6) return 'yellow';
    return 'green';
  };

  return (
    <Col xs={24} lg={12}>
      <Card
        loading={loading || isLoading}
        className={`rounded-2xl shadow-xl h-full overflow-hidden ${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border`}
        bodyStyle={{ padding: 0 }}
        title={
          !loading && !isLoading ? (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className={`${isDark ? 'bg-gray-700' : 'bg-gray-100'} p-3 rounded-xl`}>
                  <AuditOutlined className={`text-2xl ${isDark ? 'text-gray-300' : 'text-gray-600'}`} />
                </div>
                <div>
                  <Title level={3} className={`!mb-1 ${isDark ? 'text-white' : 'text-gray-900'}`}>
                    Orders Pending Review
                  </Title>
                  <Text type="secondary">
                    {pendingOrders.length} orders waiting for approval
                  </Text>
                </div>
              </div>
              <Badge
                count={pendingOrders.length}
                style={{
                  backgroundColor: isDark ? '#374151' : '#f3f4f6',
                  color: isDark ? '#fff' : '#374151'
                }}
              />
            </div>
          ) : undefined
        }
        headStyle={{
          borderBottom: `1px solid ${isDark ? '#374151' : '#f0f0f0'}`,
          background: isDark ? '#1f2937' : '#fafafa',
          padding: loading || isLoading ? 0 : '16px 24px'
        }}
      >

        {/* Orders List */}
        <div className="p-6">
          {pendingOrders.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div className="text-center">
                  <Text type="secondary">No orders pending review</Text>
                  <br />
                  <Text type="secondary" className="text-sm">
                    All orders have been processed
                  </Text>
                </div>
              }
              className="py-8"
            />
          ) : (
            <List
              dataSource={pendingOrders}
              renderItem={(order: Order) => (
                <List.Item
                  className={`border-0 px-0 py-4 rounded-lg transition-all duration-200 cursor-pointer ${
                    isDark ? 'hover:bg-gray-700' : 'hover:bg-gray-50'
                  }`}
                  onClick={() => handleOrderClick(order)}
                >
                  <div className="w-full">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Badge
                          dot
                          color={getOrderPriorityColor(order.createdAt)}
                          className="flex-shrink-0"
                        >
                          <Avatar
                            className={`${isDark ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'}`}
                            icon={<ShoppingCartOutlined />}
                            size="large"
                          />
                        </Badge>
                        <div>
                          <div className="flex items-center space-x-2 mb-1">
                            <Text strong className="text-lg">
                              {order.orderCode}
                            </Text>
                            <OrderTypeTag type={order.orderType} />
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <Space size="small">
                              <UserOutlined />
                              <Text type="secondary">User: {order.userId}</Text>
                            </Space>
                            <Space size="small">
                              <ClockCircleOutlined />
                              <Text type="secondary">
                                {dayjs(order.createdAt).fromNow()}
                              </Text>
                            </Space>
                          </div>
                        </div>
                      </div>

                      <div className="text-right">
                        <div className="flex items-center space-x-2 mb-1">
                          <DollarOutlined className="text-green-500" />
                          <Text strong className="text-lg text-green-600">
                            {formatOrderAmount(order.quantity * order.pricePerUnit, order.currencyUnit)}
                          </Text>
                        </div>
                        <Button
                          type="primary"
                          size="small"
                          icon={<AuditOutlined />}
                          className="bg-orange-500 hover:bg-orange-600 border-orange-500 hover:border-orange-600"
                        >
                          Review
                        </Button>
                      </div>
                    </div>

                    {/* Order Details */}
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <Text type="secondary" className="block">Quantity</Text>
                          <Text strong>{order.quantity.toLocaleString()}</Text>
                        </div>
                        <div>
                          <Text type="secondary" className="block">Price/Unit</Text>
                          <Text strong>
                            {formatOrderAmount(order.pricePerUnit, order.currencyUnit)}
                          </Text>
                        </div>
                        <div>
                          <Text type="secondary" className="block">Currency</Text>
                          <Text strong>{order.currencyUnit}</Text>
                        </div>
                        <div>
                          <Text type="secondary" className="block">Status</Text>
                          <OrderStatusTag status={order.orderStatus} />
                        </div>
                      </div>
                    </div>

                    {/* Urgency Indicator */}
                    {dayjs().diff(dayjs(order.createdAt), 'hour') > 12 && (
                      <div className="mt-2 flex items-center space-x-2 text-orange-600">
                        <ExclamationCircleOutlined />
                        <Text type="warning" className="text-sm">
                          Pending for {dayjs().diff(dayjs(order.createdAt), 'hour')} hours
                        </Text>
                      </div>
                    )}
                  </div>
                </List.Item>
              )}
            />
          )}

          {/* View All Button */}
          {pendingOrders.length > 0 && (
            <div className="text-center mt-6 pt-4 border-t border-gray-100">
              <Button
                type="link"
                icon={<ArrowRightOutlined />}
                onClick={() => navigate('/orders')}
                className="text-orange-600 hover:text-orange-700"
              >
                View All Orders
              </Button>
            </div>
          )}
        </div>
      </Card>
    </Col>
  );
};

export default RecentOrders;
