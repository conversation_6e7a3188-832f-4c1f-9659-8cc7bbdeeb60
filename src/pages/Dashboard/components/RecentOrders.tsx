import {
  ShoppingCartOutlined,
  AuditOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  UserOutlined,
  ArrowRightOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Card,
  Col,
  List,
  Button,
  Badge,
  Typography,
  Space,
  Empty,
} from 'antd';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { OrderStatusTag, OrderTypeTag } from '@/components';
import { useOrders } from '@/hooks/useOrders';
import { useOrderNotifications } from '@/hooks/useOrderNotifications';
import { useUserStore } from '@/stores';
import type { Order } from '@/types';
import { formatOrderAmount } from '@/utils/currencyUtils';

dayjs.extend(relativeTime);

const { Title, Text } = Typography;

interface RecentOrdersProps {
  loading?: boolean;
}

const RecentOrders = (props: RecentOrdersProps) => {
  const { loading = false } = props || {};

  const { t } = useTranslation('recentOrders');
  const { isDark } = useUserStore();
  const navigate = useNavigate();

  // Fetch orders that need review (PENDING status)
  const { data: ordersResponse, isLoading } = useOrders({
    filters: [{ key: 'orderStatus', operator: 'eq', value: 'PENDING' }],
    page: 1,
    pageSize: 5,
  });

  const pendingOrders = ordersResponse?.data?.data || [];
  const handleOrderClick = (order: Order) => {
    navigate(`/orders/review/${order.orderId}`);
  };

  return (
    <Col xs={24} lg={12}>
      <Card
        loading={loading || isLoading}
        className={`rounded-2xl shadow-xl h-full overflow-hidden ${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border`}
        styles={{
          body: {
            padding: loading || isLoading ? '24px' : 0,
            minHeight: loading || isLoading ? '400px' : 'auto',
          },
          header: {
            borderBottom: `1px solid ${isDark ? '#374151' : '#f0f0f0'}`,
            background: isDark ? '#1f2937' : '#fafafa',
            padding: loading || isLoading ? '24px' : '16px 24px',
            minHeight: loading || isLoading ? '80px' : 'auto',
          },
        }}
        title={
          !loading && !isLoading ? (
            <div className='flex items-center justify-between'>
              <div className='flex items-center space-x-4'>
                <div
                  className={`${isDark ? 'bg-gray-700' : 'bg-gray-100'} p-3 rounded-xl`}
                >
                  <AuditOutlined
                    className={`text-2xl ${isDark ? 'text-gray-300' : 'text-gray-600'}`}
                  />
                </div>
                <div>
                  <Title
                    level={3}
                    className={`!mb-1 ${isDark ? 'text-white' : 'text-gray-900'}`}
                  >
                    Orders Pending Review
                  </Title>
                  <Text type='secondary'>
                    {pendingOrders.length} orders waiting for approval
                  </Text>
                </div>
              </div>
              <Badge
                count={pendingOrders.length}
                style={{
                  backgroundColor: isDark ? '#374151' : '#f3f4f6',
                  color: isDark ? '#fff' : '#374151',
                }}
              />
            </div>
          ) : undefined
        }
      >
        {/* Orders List */}
        <div className='p-6'>
          {pendingOrders.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div className='text-center'>
                  <Text type='secondary'>No orders pending review</Text>
                  <br />
                  <Text type='secondary' className='text-sm'>
                    All orders have been processed
                  </Text>
                </div>
              }
              className='py-8'
            />
          ) : (
            <List
              dataSource={pendingOrders}
              renderItem={(order: Order) => (
                <List.Item
                  className={`border-0 px-0 py-4 rounded-lg transition-all duration-200 cursor-pointer ${
                    isDark ? 'hover:bg-gray-700' : 'hover:bg-gray-50'
                  }`}
                  onClick={() => handleOrderClick(order)}
                >
                  <div className='w-full'>
                    <div className='flex items-center justify-between mb-3'>
                      <div className='flex items-center space-x-3'>
                        <Avatar
                          className={`${isDark ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'}`}
                          icon={<ShoppingCartOutlined />}
                          size='large'
                        />
                        <div className='pl-4'>
                          <div className='flex items-center mb-1'>
                            <Text strong className='text-lg'>
                              {order.orderCode}
                            </Text>
                            <OrderTypeTag type={order.orderType} />
                          </div>
                          <div className='flex items-center space-x-4 text-sm text-gray-500'>
                            <Space size='small'>
                              <UserOutlined />
                              <Text type='secondary'>User: {order.userId}</Text>
                            </Space>
                            <Space size='small'>
                              <ClockCircleOutlined />
                              <Text type='secondary'>
                                {dayjs(order.createdAt).fromNow()}
                              </Text>
                            </Space>
                          </div>
                        </div>
                      </div>

                      <div className='text-right'>
                        <div className='flex items-center space-x-2 mb-1'>
                          <DollarOutlined className='text-green-500' />
                          <Text strong className='text-lg text-green-600'>
                            {formatOrderAmount(
                              order.quantity * order.pricePerUnit,
                              order.currencyUnit,
                            )}
                          </Text>
                        </div>
                        <Button
                          type='primary'
                          size='small'
                          icon={<AuditOutlined />}
                          className={`${isDark ? 'bg-gray-600 hover:bg-gray-500 border-gray-600 hover:border-gray-500' : 'bg-gray-700 hover:bg-gray-800 border-gray-700 hover:border-gray-800'}`}
                        >
                          Review
                        </Button>
                      </div>
                    </div>

                    {/* Order Details */}
                    <div
                      className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-3 rounded-lg`}
                    >
                      <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
                        <div>
                          <Text type='secondary' className='block'>
                            Quantity
                          </Text>
                          <Text strong>{order.quantity.toLocaleString()}</Text>
                        </div>
                        <div>
                          <Text type='secondary' className='block'>
                            Price/Unit
                          </Text>
                          <Text strong>
                            {formatOrderAmount(
                              order.pricePerUnit,
                              order.currencyUnit,
                            )}
                          </Text>
                        </div>
                        <div>
                          <Text type='secondary' className='block'>
                            Currency
                          </Text>
                          <Text strong>{order.currencyUnit}</Text>
                        </div>
                        <div>
                          <Text type='secondary' className='block'>
                            Status
                          </Text>
                          <OrderStatusTag status={order.orderStatus} />
                        </div>
                      </div>
                    </div>

                    {/* Urgency Indicator */}
                    {dayjs().diff(dayjs(order.createdAt), 'hour') > 12 && (
                      <div
                        className={`mt-2 flex items-center space-x-2 ${isDark ? 'text-orange-400' : 'text-orange-600'}`}
                      >
                        <ExclamationCircleOutlined />
                        <Text type='warning' className='text-sm'>
                          Pending for{' '}
                          {dayjs().diff(dayjs(order.createdAt), 'hour')} hours
                        </Text>
                      </div>
                    )}
                  </div>
                </List.Item>
              )}
            />
          )}

          {/* View All Button */}
          {pendingOrders.length > 0 && (
            <div
              className={`text-center mt-6 pt-4 border-t ${isDark ? 'border-gray-700' : 'border-gray-100'}`}
            >
              <Button
                type='link'
                icon={<ArrowRightOutlined />}
                onClick={() => navigate('/orders')}
                className={
                  isDark
                    ? 'text-gray-300 hover:text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }
              >
                View All Orders
              </Button>
            </div>
          )}
        </div>
      </Card>
    </Col>
  );
};

export default RecentOrders;
