import {
  UserOutlined,
  UserAddOutlined,
  IdcardOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ArrowRightOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Card,
  Col,
  List,
  Typography,
  Space,
  Badge,
  Button,
  Empty,
} from 'antd';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import type { NewMember } from '../apis';
import { AlphaStatusTag } from '@/components';
import { useUserStore } from '@/stores';
import { MemberStatusEnums } from '@/utils';

dayjs.extend(relativeTime);

const { Title, Text } = Typography;

interface NewMembersProps {
  newMembers: Array<NewMember>;
  loading: boolean;
}

const NewMembers = (props: NewMembersProps) => {
  const { newMembers, loading } = props || {};

  const { t } = useTranslation('newMembers');
  const { isDark } = useUserStore();
  const navigate = useNavigate();

  const handleMemberClick = (member: NewMember) => {
    // Navigate to user management or user detail page
    navigate('/user-management');
  };


  const getRegistrationPriority = (createdAt: string) => {
    const hoursAgo = dayjs().diff(dayjs(createdAt), 'hour');
    if (hoursAgo < 1) return 'new';
    if (hoursAgo < 24) return 'recent';
    return 'normal';
  };

  return (
    <Col xs={24} lg={12}>
      <Card
        loading={loading}
        className={`rounded-2xl shadow-xl h-full overflow-hidden ${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border`}
        styles={{
          body: {
            padding: loading ? '24px' : 0,
            minHeight: loading ? '400px' : 'auto',
          },
          header: {
            borderBottom: `1px solid ${isDark ? '#374151' : '#f0f0f0'}`,
            background: isDark ? '#1f2937' : '#fafafa',
            padding: loading ? '24px' : '16px 24px',
            minHeight: loading ? '80px' : 'auto',
          },
        }}
        title={
          !loading ? (
            <div className='flex items-center justify-between'>
              <div className='flex items-center space-x-4'>
                <div
                  className={`${isDark ? 'bg-gray-700' : 'bg-gray-100'} p-3 rounded-xl`}
                >
                  <UserAddOutlined
                    className={`text-2xl ${isDark ? 'text-gray-300' : 'text-gray-600'}`}
                  />
                </div>
                <div>
                  <Title
                    level={3}
                    className={`!mb-1 ${isDark ? 'text-white' : 'text-gray-900'}`}
                  >
                    Recently Registered
                  </Title>
                  <Text type='secondary'>
                    {newMembers.length} new members joined
                  </Text>
                </div>
              </div>
              <Badge
                count={newMembers.length}
                style={{
                  backgroundColor: isDark ? '#374151' : '#f3f4f6',
                  color: isDark ? '#fff' : '#374151',
                }}
              />
            </div>
          ) : undefined
        }
      >
        {/* Members List */}
        <div className='p-6'>
          {newMembers.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div className='text-center'>
                  <Text type='secondary'>No new members</Text>
                  <br />
                  <Text type='secondary' className='text-sm'>
                    No recent registrations
                  </Text>
                </div>
              }
              className='py-8'
            />
          ) : (
            <List
              dataSource={newMembers}
              renderItem={(member: NewMember) => (
                <List.Item
                  className={`border-0 px-0 py-4 rounded-lg transition-all duration-200 cursor-pointer ${
                    isDark ? 'hover:bg-gray-700' : 'hover:bg-gray-50'
                  }`}
                  onClick={() => handleMemberClick(member)}
                >
                  <div className='w-full'>
                    <div className='flex items-center justify-between mb-3'>
                      <div className='flex items-center space-x-3'>
                        <Avatar
                          className={`${isDark ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'} font-medium`}
                          size='large'
                        >
                          {member.memberName.charAt(0).toUpperCase()}
                        </Avatar>
                        <div className='pl-4'>
                          <div className='flex items-center space-x-2 mb-1'>
                            <Text strong className='text-lg'>
                              {member.memberName}
                            </Text>
                            <AlphaStatusTag
                              status={
                                member.isActive
                                  ? MemberStatusEnums.Active
                                  : MemberStatusEnums.Inactive
                              }
                            />
                          </div>
                          <div className='flex items-center space-x-4 text-sm text-gray-500'>
                            <Space size='small'>
                              <IdcardOutlined />
                              <Text type='secondary'>
                                {member.passportNumber}
                              </Text>
                            </Space>
                            <Space size='small'>
                              <CalendarOutlined />
                              <Text type='secondary'>
                                {dayjs(member.createdAt).fromNow()}
                              </Text>
                            </Space>
                          </div>
                        </div>
                      </div>

                      <div className='text-right'>
                        <Button
                          type='primary'
                          size='small'
                          icon={<UserOutlined />}
                          className={`${isDark ? 'bg-gray-600 hover:bg-gray-500 border-gray-600 hover:border-gray-500' : 'bg-gray-700 hover:bg-gray-800 border-gray-700 hover:border-gray-800'}`}
                        >
                          View
                        </Button>
                      </div>
                    </div>

                    {/* Member Details */}
                    <div
                      className={`${isDark ? 'bg-gray-700' : 'bg-gray-50'} p-3 rounded-lg`}
                    >
                      <div className='grid grid-cols-2 md:grid-cols-3 gap-4 text-sm'>
                        <div>
                          <Text type='secondary' className='block'>
                            Status
                          </Text>
                          <div className='flex items-center space-x-1'>
                            {member.isActive ? (
                              <CheckCircleOutlined className='text-green-500' />
                            ) : (
                              <CloseCircleOutlined className='text-red-500' />
                            )}
                            <Text strong>
                              {member.isActive ? 'Active' : 'Inactive'}
                            </Text>
                          </div>
                        </div>
                        <div>
                          <Text type='secondary' className='block'>
                            Registered
                          </Text>
                          <Text strong>
                            {dayjs(member.createdAt).format('MMM DD, YYYY')}
                          </Text>
                        </div>
                        <div>
                          <Text type='secondary' className='block'>
                            ID Document
                          </Text>
                          <Text strong className='font-mono text-xs'>
                            {member.passportNumber}
                          </Text>
                        </div>
                      </div>
                    </div>

                    {/* New Member Indicator */}
                    {getRegistrationPriority(member.createdAt) === 'new' && (
                      <div
                        className={`mt-2 flex items-center space-x-2 ${isDark ? 'text-green-400' : 'text-green-600'}`}
                      >
                        <UserAddOutlined />
                        <Text type='success' className='text-sm'>
                          Registered {dayjs(member.createdAt).fromNow()}
                        </Text>
                      </div>
                    )}
                  </div>
                </List.Item>
              )}
            />
          )}

          {/* View All Button */}
          {newMembers.length > 0 && (
            <div
              className={`text-center mt-6 pt-4 border-t ${isDark ? 'border-gray-700' : 'border-gray-100'}`}
            >
              <Button
                type='link'
                icon={<ArrowRightOutlined />}
                onClick={() => navigate('/user-management')}
                className={
                  isDark
                    ? 'text-gray-300 hover:text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }
              >
                View All Members
              </Button>
            </div>
          )}
        </div>
      </Card>
    </Col>
  );
};

export default NewMembers;
