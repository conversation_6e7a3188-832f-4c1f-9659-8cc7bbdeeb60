import React from 'react';
import {
  UserOutlined,
  UserAddOutlined,
  IdcardOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ArrowRightOutlined
} from '@ant-design/icons';
import {
  Avatar,
  Card,
  Col,
  List,
  Typography,
  Space,
  Badge,
  Button,
  Empty,
  Tooltip
} from 'antd';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import type { NewMember } from '../apis';
import { AlphaStatusTag } from '@/components';
import Txt from '@/components/Txt';
import { useResponsive } from '@/hooks/useResponsive';
import { useUserStore } from '@/stores';
import { MemberStatusEnums } from '@/utils';

dayjs.extend(relativeTime);

const { Title, Text } = Typography;

interface NewMembersProps {
  newMembers: Array<NewMember>;
  loading: boolean;
}

const NewMembers = (props: NewMembersProps) => {
  const { newMembers, loading } = props || {};

  const { t } = useTranslation('newMembers');
  const { isMobile } = useResponsive();
  const { isDark } = useUserStore();
  const navigate = useNavigate();

  const handleMemberClick = (member: NewMember) => {
    // Navigate to user management or user detail page
    navigate('/user-management');
  };

  const getMemberStatusColor = (isActive: boolean) => {
    return isActive ? 'green' : 'red';
  };

  const getRegistrationPriority = (createdAt: string) => {
    const hoursAgo = dayjs().diff(dayjs(createdAt), 'hour');
    if (hoursAgo < 1) return 'new';
    if (hoursAgo < 24) return 'recent';
    return 'normal';
  };

  return (
    <Col xs={24} lg={12}>
      <Card
        loading={loading}
        className="rounded-2xl border-0 shadow-xl h-full overflow-hidden"
        bodyStyle={{ padding: 0 }}
      >
        {/* Header */}
        <div className="bg-blue-500 p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-white bg-opacity-20 p-3 rounded-xl">
                <UserAddOutlined className="text-2xl" />
              </div>
              <div>
                <Title level={3} className="!text-white !mb-1">
                  Recently Registered
                </Title>
                <Text className="text-blue-100">
                  {newMembers.length} new members joined
                </Text>
              </div>
            </div>
            <Badge
              count={newMembers.length}
              className="bg-white text-blue-500"
              style={{ backgroundColor: 'white', color: '#3b82f6' }}
            />
          </div>
        </div>

        {/* Members List */}
        <div className="p-6">
          {newMembers.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div className="text-center">
                  <Text type="secondary">No new members</Text>
                  <br />
                  <Text type="secondary" className="text-sm">
                    No recent registrations
                  </Text>
                </div>
              }
              className="py-8"
            />
          ) : (
            <List
              dataSource={newMembers}
              renderItem={(member: NewMember) => (
                <List.Item
                  className="border-0 px-0 py-4 hover:bg-gray-50 rounded-lg transition-all duration-200 cursor-pointer"
                  onClick={() => handleMemberClick(member)}
                >
                  <div className="w-full">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <Badge
                          dot
                          color={getMemberStatusColor(member.isActive)}
                          className="flex-shrink-0"
                        >
                          <Avatar
                            className="bg-blue-100 text-blue-600 font-medium"
                            size="large"
                          >
                            {member.memberName.charAt(0).toUpperCase()}
                          </Avatar>
                        </Badge>
                        <div>
                          <div className="flex items-center space-x-2 mb-1">
                            <Text strong className="text-lg">
                              {member.memberName}
                            </Text>
                            <AlphaStatusTag
                              status={
                                member.isActive
                                  ? MemberStatusEnums.Active
                                  : MemberStatusEnums.Inactive
                              }
                            />
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <Space size="small">
                              <IdcardOutlined />
                              <Text type="secondary">{member.passportNumber}</Text>
                            </Space>
                            <Space size="small">
                              <CalendarOutlined />
                              <Text type="secondary">
                                {dayjs(member.createdAt).fromNow()}
                              </Text>
                            </Space>
                          </div>
                        </div>
                      </div>

                      <div className="text-right">
                        {getRegistrationPriority(member.createdAt) === 'new' && (
                          <Badge
                            count="NEW"
                            style={{ backgroundColor: '#52c41a' }}
                            className="mb-2"
                          />
                        )}
                        <Button
                          type="primary"
                          size="small"
                          icon={<UserOutlined />}
                          className="bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600"
                        >
                          View
                        </Button>
                      </div>
                    </div>

                    {/* Member Details */}
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <Text type="secondary" className="block">Status</Text>
                          <div className="flex items-center space-x-1">
                            {member.isActive ? (
                              <CheckCircleOutlined className="text-green-500" />
                            ) : (
                              <CloseCircleOutlined className="text-red-500" />
                            )}
                            <Text strong>
                              {member.isActive ? 'Active' : 'Inactive'}
                            </Text>
                          </div>
                        </div>
                        <div>
                          <Text type="secondary" className="block">Registered</Text>
                          <Text strong>
                            {dayjs(member.createdAt).format('MMM DD, YYYY')}
                          </Text>
                        </div>
                        <div>
                          <Text type="secondary" className="block">ID Document</Text>
                          <Text strong className="font-mono text-xs">
                            {member.passportNumber}
                          </Text>
                        </div>
                      </div>
                    </div>

                    {/* New Member Indicator */}
                    {getRegistrationPriority(member.createdAt) === 'new' && (
                      <div className="mt-2 flex items-center space-x-2 text-green-600">
                        <UserAddOutlined />
                        <Text type="success" className="text-sm">
                          Registered {dayjs(member.createdAt).fromNow()}
                        </Text>
                      </div>
                    )}
                  </div>
                </List.Item>
              )}
            />
          )}

          {/* View All Button */}
          {newMembers.length > 0 && (
            <div className="text-center mt-6 pt-4 border-t border-gray-100">
              <Button
                type="link"
                icon={<ArrowRightOutlined />}
                onClick={() => navigate('/user-management')}
                className="text-blue-600 hover:text-blue-700"
              >
                View All Members
              </Button>
            </div>
          )}
        </div>
      </Card>
    </Col>
  );
};

export default NewMembers;
