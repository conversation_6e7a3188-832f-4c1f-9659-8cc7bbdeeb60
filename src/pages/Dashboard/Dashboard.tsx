import {
  UserOutlined,
  ShoppingCartOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  ReloadOutlined,
  DashboardOutlined,
} from '@ant-design/icons';
import { Row, Col, Typography, Button, Tooltip } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useDashboardSummary, useRefreshDashboardSummary } from './apis';
import NewMembers from './components/NewMembers';
import RecentOrders from './components/RecentOrders';
import Txt from '@/components/Txt';
import { useResponsive } from '@/hooks/useResponsive.ts';
import StatsCard from '@/pages/Dashboard/components/StatsCard';
import { useUserStore } from '@/stores';

const { Title } = Typography;

const Dashboard: React.FC = () => {
  const { t } = useTranslation('dashboard');
  const { isMobile } = useResponsive();
  const { isDark } = useUserStore();
  const {
    data: dashboardSummary,
    isPending: loadingDashboardSummary,
    refetch: refetchDashboardSummary,
    isRefetching: refetchingDashboardSummary,
  } = useDashboardSummary({});
  const {
    mutate: refreshDashboardSummary,
    isPending: refreshingDashboardSummary,
  } = useRefreshDashboardSummary({
    onSuccess: () => {
      refetchDashboardSummary();
    },
  });

  const handleRefresh = () => {
    refreshDashboardSummary({});
  };

  return (
    <div
      className={`space-y-4 ${isMobile ? 'sm:space-y-4' : 'sm:space-y-6 lg:space-y-8'}`}
    >
      {/* Welcome Section */}
      <div
        className={`rounded-xl p-4 md:rounded-2xl md:p-6 lg:p-8 bg-gradient-to-r ${
          isDark
            ? 'from-blue-700 via-blue-800 to-blue-900'
            : 'from-blue-100 via-blue-200 to-blue-300'
        }`}
      >
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-3 sm:gap-4'>
            <div className='w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-blue-400 to-slate-600 rounded-xl flex items-center justify-center flex-shrink-0'>
              <DashboardOutlined className='!text-white text-lg md:text-xl' />
            </div>
            <div>
              <Title level={2}>{t('welcome')}</Title>
              <Txt>{t('description')}</Txt>
            </div>
          </div>

          <Tooltip title={t('refreshDescription')}>
            <Button
              size='large'
              icon={<ReloadOutlined />}
              loading={refreshingDashboardSummary || refetchingDashboardSummary}
              onClick={handleRefresh}
            >
              {t('refresh')}
            </Button>
          </Tooltip>
        </div>
      </div>

      {/* Stats Cards */}
      <Row
        gutter={[
          { xs: 12, sm: 16, lg: 24 },
          { xs: 12, sm: 16, lg: 24 },
        ]}
      >
        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title={t('totalMembers')}
            value={dashboardSummary?.totalMembers || 0}
            loading={loadingDashboardSummary || refetchingDashboardSummary}
            icon={<UserOutlined />}
            color='#1890ff'
            trend={{ value: 12, isPositive: true }}
          />
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title={t('recentOrders')}
            value={dashboardSummary?.recentOrderCount || 0}
            loading={loadingDashboardSummary || refetchingDashboardSummary}
            icon={<ShoppingCartOutlined />}
            color='#52c41a'
            trend={{ value: 8, isPositive: true }}
          />
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title={t('pendingTransactions')}
            value={dashboardSummary?.pendingTransaction || 0}
            loading={loadingDashboardSummary || refetchingDashboardSummary}
            icon={<ClockCircleOutlined />}
            color='#faad14'
            trend={{ value: 3, isPositive: false }}
          />
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <StatsCard
            title={t('totalRevenue')}
            value={dashboardSummary?.totalRevenue || 0}
            loading={loadingDashboardSummary || refetchingDashboardSummary}
            icon={<DollarOutlined />}
            color='#722ed1'
            isCurrency
            trend={{ value: 15, isPositive: true }}
          />
        </Col>
      </Row>

      {/* Recent Activity */}
      <Row
        gutter={[
          { xs: 12, sm: 16, lg: 24 },
          { xs: 12, sm: 16, lg: 24 },
        ]}
      >
        {/* Recent Orders */}
        <RecentOrders
          loading={loadingDashboardSummary || refetchingDashboardSummary}
        />

        {/* Recent Members */}
        <NewMembers
          newMembers={dashboardSummary?.newMembers || []}
          loading={loadingDashboardSummary || refetchingDashboardSummary}
        />
      </Row>
    </div>
  );
};

export default Dashboard;
