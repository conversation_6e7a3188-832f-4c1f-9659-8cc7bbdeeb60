import { mockMembers, mockDelay } from '@/api/mockData.ts';
import type {
  Member,
  CreateMemberRequest,
  ApiResponse,
  PaginatedResponse,
} from '@/types';

// Mock API functions - replace with real API calls later
export const fetchMembers = async (params?: {
  page?: number;
  pageSize?: number;
  search?: string;
}): Promise<ApiResponse<PaginatedResponse<Member>>> => {
  await mockDelay();

  const { page = 1, pageSize = 10, search = '' } = params || {};

  // Filter members based on search
  let filteredMembers = mockMembers;
  if (search) {
    filteredMembers = mockMembers.filter(
      (member) =>
        member.name.toLowerCase().includes(search.toLowerCase()) ||
        member.passport.toLowerCase().includes(search.toLowerCase()),
    );
  }

  // Paginate results
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedMembers = filteredMembers.slice(startIndex, endIndex);

  // Later replace with: return api.get<PaginatedResponse<Member>>('/members', params);
  return {
    data: {
      data: paginatedMembers,
      total: filteredMembers.length,
      page,
      pageSize,
    },
    success: true,
    message: 'Members fetched successfully',
  };
};

export const registerMember = async (
  memberData: CreateMemberRequest,
): Promise<ApiResponse<Member>> => {
  await mockDelay();

  // Create new member with mock data
  const newMember: Member = {
    id: Date.now().toString(),
    name: memberData.name,
    passport: memberData.passport,
    registrationDate: new Date().toISOString(),
    status: 'active',
  };

  // Add to mock data (in real app, this would be handled by the backend)
  mockMembers.unshift(newMember);

  // Later replace with: return api.post<Member>('/members', memberData);
  return {
    data: newMember,
    success: true,
    message: 'Member registered successfully',
  };
};

export const fetchMemberById = async (
  id: string,
): Promise<ApiResponse<Member>> => {
  await mockDelay();

  const member = mockMembers.find((m) => m.id === id);

  if (!member) {
    throw new Error('Member not found');
  }

  // Later replace with: return api.get<Member>(`/members/${id}`);
  return {
    data: member,
    success: true,
    message: 'Member fetched successfully',
  };
};
