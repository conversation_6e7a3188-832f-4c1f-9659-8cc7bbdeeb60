import { useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/api';
import { useMasterMutation, type UseMasterMutationProps } from '@/hooks';

type UserDeleteProps = {
  userId: string;
};
type UserDeleteRes = unknown;

type Other = unknown;
const useDeleteUser = (
  useProps: UseMasterMutationProps<UserDeleteRes, UserDeleteProps, Other>,
) => {
  const { ...config } = useProps;
  const queryClient = useQueryClient();
  const mutation = useMasterMutation<UserDeleteRes, UserDeleteProps>({
    ...config,
    mutationFn: (props) => {
      const request = apiClient
        .delete(`/admin/users/${props.userId}`)
        .then(({ data }) => data);
      return request;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-list'] });
    },
  });
  return { ...mutation };
};

export { useDeleteUser };
export type { UserDeleteProps };
