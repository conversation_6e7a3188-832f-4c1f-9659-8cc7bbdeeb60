import { useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/api';
import { useMasterMutation, type UseMasterMutationProps } from '@/hooks';
import type { StaffStatusEnums } from '@/utils';

type UserStatusProps = {
  userId: string;
  status: StaffStatusEnums;
};
type UserStatusRes = unknown;

type Other = unknown;

const useUserStatus = (
  useProps: UseMasterMutationProps<UserStatusRes, UserStatusProps, Other>,
) => {
  const { ...config } = useProps;
  const queryClient = useQueryClient();
  const mutation = useMasterMutation<UserStatusRes, UserStatusProps>({
    ...config,
    mutationFn: (props) => {
      const request = apiClient
        .patch(`/admin/users/${props.userId}/status`, props)
        .then(({ data }) => data);
      return request;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-list'] });
    },
  });
  return { ...mutation };
};

export { useUserStatus };
