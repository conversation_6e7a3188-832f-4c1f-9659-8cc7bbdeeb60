import qs from 'qs';
import { apiClient } from '@/api/apiClient';
import { DEFAULT_STALE_TIME } from '@/constants';
import { useMasterQuery, type UseMasterQueryProps } from '@/hooks/useApiMaster';
import type { ApiFiltersParams } from '@/types';
import { buildApiQueryParams, type MemberStatusEnums } from '@/utils';

type UserListProps = {
  filterParams?: ApiFiltersParams;
  sort?: Array<string>;
};
type UserOptions = {
  id: string;
  phoneNumber: string;
  userName: string;
  email: string;
  status: MemberStatusEnums;
  isNew: boolean | string;
  passportNumber: string;
  roles: Array<string>;
  createdByName: string;
  createdAt: string;
  updatedAt: string;
};
type UserListRes = {
  totalItems: number;
  totalPages: number;
  pageSize: number;
  pageNumber: number;
  items: Array<UserOptions>;
};

type Other = unknown;

const useUserList = (
  useProps: UseMasterQueryProps<Other, UserListProps, UserListRes>,
) => {
  const { params, ...config } = useProps;
  const testQuery = useMasterQuery<UserListRes, UserListProps>({
    ...config,
    queryKey: ['user-list', ...Object.values(params || {})],
    qf: () => {
      const queryString = buildApiQueryParams(params?.filterParams || {});
      const request = apiClient
        .get(`/admin/users?${queryString}`, {
          params: { ...params },
          paramsSerializer: (params) => {
            return qs.stringify(params, {
              arrayFormat: 'repeat',
            });
          },
        })
        .then(({ data }) => data);
      return request;
    },
    refetchOnWindowFocus: true,
    staleTime: DEFAULT_STALE_TIME,
  });
  return { ...testQuery };
};

export { useUserList };
export type { UserListProps, UserListRes, UserOptions };
