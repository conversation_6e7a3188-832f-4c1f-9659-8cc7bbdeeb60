import {
  CheckCircleOutlined,
  CloseOutlined,
  IdcardOutlined,
  MailOutlined,
  PhoneOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Avatar, Card, Descriptions, Modal, Tag, Typography } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import type { UserDTRes } from '../apis';
import { AlphaStatusTag } from '@/components';
import { PassportTypeTag } from '@/components/Tags/PassportTypeTag';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import { useUserStore } from '@/stores';
import { formatDate } from '@/utils/tableUtils.ts';

const { Title, Paragraph } = Typography;

interface UserDetailsModalProps {
  visible: boolean;
  user: UserDTRes | null;
  onClose: () => void;
  onEdit?: (user: UserDTRes) => void;
}

const UserDetailsModal: React.FC<UserDetailsModalProps> = ({
  visible,
  user,
  onClose,
}) => {
  const { t } = useTranslation('memberDetailsModal');
  const { isMobile } = useResponsive();
  const { isDark } = useUserStore();

  const modalWidth = useResponsiveValue({
    xs: '95%',
    sm: '90%',
    md: '800px',
    lg: '900px',
  });

  const avatarSize = useResponsiveValue({
    xs: 48,
    sm: 56,
    md: 64,
  });

  const descriptionColumns = useResponsiveValue({
    xs: 1,
    sm: 1,
    md: 2,
  });

  if (!user) return null;

  const passport = user.passport;

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width={modalWidth}
      className={isMobile ? 'top-4' : 'top-8'}
      closeIcon={
        <CloseOutlined className='text-gray-400 hover:text-gray-600' />
      }
      styles={{ body: { padding: isMobile ? '0px' : '24px' } }}
    >
      <div className='space-y-6'>
        {/* Header */}
        <div className='flex items-center gap-4'>
          <Avatar
            size={avatarSize}
            className='bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold'
          >
            {user.userName?.[0]?.toUpperCase()}
          </Avatar>
          <div>
            <Title level={3} className='!mb-1'>
              {user.userName}
            </Title>
            <Tag color={user.isNew ? 'brown' : 'blue'}>
              {user.isNew ? 'Is New' : 'Passord Change'}
            </Tag>
          </div>
        </div>

        {/* Personal Info */}
        <Card
          className={`${isDark ? '' : '!bg-[#8f9baf]'}`}
          title={
            <span className='flex items-center gap-2'>
              <UserOutlined /> {t('personalInfo') || 'Personal Info'}
            </span>
          }
        >
          <Descriptions
            column={descriptionColumns}
            size={isMobile ? 'small' : 'middle'}
          >
            {user.email && (
              <Descriptions.Item
                label={
                  <span>
                    <MailOutlined /> Email
                  </span>
                }
              >
                <a href={`mailto:${user.email}`} className='text-blue-600'>
                  {user.email}
                </a>
              </Descriptions.Item>
            )}
            {user.phoneNumber && (
              <Descriptions.Item
                label={
                  <span>
                    <PhoneOutlined /> {t('phone') || 'Phone'}
                  </span>
                }
              >
                <a href={`tel:${user.phoneNumber}`} className='text-blue-600'>
                  {user.phoneNumber}
                </a>
              </Descriptions.Item>
            )}
            <Descriptions.Item
              label={
                <span>
                  <IdcardOutlined /> Passport Type
                </span>
              }
            >
              <Paragraph className='mb-0'>
                <PassportTypeTag type={user.passport.passportType} />
              </Paragraph>
            </Descriptions.Item>
            <Descriptions.Item
              label={
                <span>
                  <CheckCircleOutlined /> Status
                </span>
              }
            >
              <AlphaStatusTag status={user.status} />
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* Passport Info */}
        {passport && (
          <Card
            className={`${isDark ? '' : '!bg-[#8f9baf]'}`}
            title={
              <span className='flex items-center gap-2'>
                <IdcardOutlined /> Passport Info
              </span>
            }
          >
            <Descriptions
              column={descriptionColumns}
              size={isMobile ? 'small' : 'middle'}
            >
              <Descriptions.Item label='Passport Number'>
                {passport.passportNumber}
              </Descriptions.Item>
              <Descriptions.Item label='Passport Type'>
                <PassportTypeTag type={user.passport.passportType} />
              </Descriptions.Item>
              <Descriptions.Item label='First Name'>
                {passport.firstName}
              </Descriptions.Item>
              <Descriptions.Item label='Last Name'>
                {passport.lastName}
              </Descriptions.Item>
              <Descriptions.Item label='Gender'>
                {passport.gender}
              </Descriptions.Item>
              <Descriptions.Item label='Nationality'>
                {passport.nationality}
              </Descriptions.Item>
              <Descriptions.Item label='Date of Birth'>
                {formatDate(passport.dateOfBirth)}
              </Descriptions.Item>
              <Descriptions.Item label='Place of Birth'>
                {passport.placeOfBirth}
              </Descriptions.Item>
              <Descriptions.Item label='Issued By'>
                {passport.issuedBy}
              </Descriptions.Item>
              <Descriptions.Item label='Issue Date'>
                {formatDate(passport.issueDate)}
              </Descriptions.Item>
              <Descriptions.Item label='Expiration Date'>
                {formatDate(passport.expirationDate)}
              </Descriptions.Item>
              <Descriptions.Item label='Remarks'>
                {passport.remarks}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default UserDetailsModal;
