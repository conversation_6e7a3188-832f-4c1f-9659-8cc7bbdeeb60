import { UserOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Col, Row, Statistic, Typography } from 'antd';

import React, { useCallback, useMemo, useState } from 'react';
import {
  useDeleteUser,
  useUserDetails,
  useUserList,
  useUserStatus,
  type UserDTRes,
  type UserListProps,
} from './apis';
import UserFilters from './components/UserFilter';
import useUserColumns from './components/useUserColumns';
import useUserFilter from './components/useUserFilter';
import UserDetailsModal from './Modals/UserDetailsModal';
import { GalaxyTable, Txt } from '@/components';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import { useUserStore } from '@/stores';
import { MemberStatusEnums } from '@/utils';

const { Title } = Typography;
const DEFAULT_SORT = ['createdAt:desc'];

const UserManagement: React.FC = () => {
  const { isDark } = useUserStore();
  const { isMobile } = useResponsive();
  const [sort, setSort] = useState<Array<string>>(DEFAULT_SORT);

  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserDTRes | null>(null);

  // Responsive values
  const statisticFontSize = useResponsiveValue({
    xs: '18px',
    sm: '20px',
    md: '24px',
    lg: '24px',
    xl: '24px',
    '2xl': '24px',
  });
  const {
    page,
    pageSize,
    updatePage,
    updatePageSize,
    apiParams: filterParams,
    ...filters
  } = useUserFilter();
  const params: UserListProps = useMemo(
    () => ({
      filterParams,
      sort,
    }),
    [filterParams, sort],
  );

  // request User
  const {
    data: userGalaxy,
    isLoading,
    error,
    refetch: isRegetGlx,
    isRefetching: isLoadUpUser,
  } = useUserList({ params });
  const {
    mutate: userDiting,
    // isError: detailErr,
    isPending: pendingD,
  } = useUserDetails({
    onSuccess: (userDT) => {
      setSelectedUser(userDT);
      setShowDetailsModal(true);
    },
  });

  const { mutate: setUserStt, isPending: statusTing } = useUserStatus({
    onSuccess: () => {
      isRegetGlx();
    },
  });

  const { mutate: delUser, isPending: delting } = useDeleteUser({});

  const users = useMemo(() => {
    if (!userGalaxy || error) return [];
    return userGalaxy.items;
  }, [userGalaxy, error]);

  // const { activeCount, inactiveCount, newUserCount } = useMemo(() => {
  //   let activeCount = 0;
  //   let inactiveCount = 0;
  //   let newUserCount = 0;

  //   if (users) {
  //     activeCount = users.filter(
  //       (user) => user.status === MemberStatusEnums.Active,
  //     ).length;

  //     inactiveCount = users.filter(
  //       (user) => user.status === MemberStatusEnums.Inactive,
  //     ).length;

  //     newUserCount = users.filter((user) => user.isNew === true).length;
  //   }

  //   return { activeCount, inactiveCount, newUserCount };
  // }, [users]);

  const handleUserDt = (userId: string) => {
    userDiting({ userId });
  };

  // FE use later

  const handleCloseDetailsModal = () => {
    setShowDetailsModal(false);
    setSelectedUser(null);
  };

  const handleToggleStatus = useCallback(
    (userId: string, status: MemberStatusEnums) => {
      setUserStt({
        userId,
        status: status ? 0 : 1,
      });
    },
    [setUserStt],
  );

  const columns = useUserColumns({
    onViewM: (id: string) => handleUserDt(id),
    pendingD,
    handleToggleStatus,
    statusTing,
    onDelUser: (id: string) => delUser({ userId: id }),
    delting,
  });

  if (error) {
    return (
      <Alert
        message='Error'
        description='Failed to load members'
        type='error'
        showIcon
        className='rounded-lg'
      />
    );
  }

  return (
    <div
      className={`space-y-4 ${isMobile ? 'sm:space-y-4' : 'sm:space-y-6 lg:space-y-8'}`}
    >
      {/* Header Section */}
      <div
        className={`rounded-xl p-4 md:rounded-2xl md:p-6 lg:p-8 bg-gradient-to-r ${
          isDark
            ? 'from-teal-700 via-teal-800 to-teal-900'
            : 'from-teal-100 via-teal-200 to-teal-300'
        }`}
      >
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-3 sm:gap-4'>
            <div className='w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-teal-400 to-teal-600 rounded-xl flex items-center justify-center flex-shrink-0'>
              <UserOutlined className='!text-white text-lg md:text-xl' />
            </div>
            <div>
              <Title level={2}>User Management</Title>
              <Txt>Manage casino user registrations and profiles</Txt>
            </div>
          </div>
        </div>
      </div>

      <div className={`${isMobile ? 'mt-3' : 'mt-4'}`}>
        <Row
          gutter={[
            { xs: 12, sm: 16, lg: 24 },
            { xs: 12, sm: 16, lg: 24 },
          ]}
        >
          <Col xs={24} sm={12} lg={6}>
            <Card
              className={`text-center border border-green-100 bg-green-50/50 shadow-card ${isMobile ? 'p-2' : ''}`}
            >
              <Statistic
                title={
                  <span className={isMobile ? 'text-xs' : ''}>All User</span>
                }
                value={userGalaxy?.totalItems}
                prefix={<UserOutlined className='text-green-600' />}
                precision={0}
                valueStyle={{
                  color: '#059669',
                  fontSize: statisticFontSize,
                  fontWeight: 'bold',
                }}
              />
            </Card>
          </Col>
          {/* <Col xs={24} sm={12} lg={6}>
            <Card
              className={`text-center border border-red-100 bg-red-50/50 shadow-card ${isMobile ? 'p-2' : ''}`}
            >
              <Statistic
                title={
                  <span className={isMobile ? 'text-xs' : ''}>
                    Inactive User
                  </span>
                }
                value={inactiveCount}
                prefix={<PoweroffOutlined className='text-red-600' />}
                precision={0}
                valueStyle={{
                  color: '#dc2626',
                  fontSize: statisticFontSize,
                  fontWeight: 'bold',
                }}
              />
            </Card>
          </Col> */}
          {/* <Col xs={24} sm={12} lg={6}>
            <Card
              className={`text-center border border-blue-100 bg-blue-50/50 shadow-card ${isMobile ? 'p-2' : ''}`}
            >
              <Statistic
                title={
                  <span className={isMobile ? 'text-xs' : ''}>Active User</span>
                }
                value={activeCount}
                prefix={<CheckCircleOutlined className='text-blue-600' />}
                precision={0}
                valueStyle={{
                  color: '#1d4ed8',
                  fontSize: statisticFontSize,
                  fontWeight: 'bold',
                }}
              />
            </Card>
          </Col> */}
          {/* <Col xs={24} sm={12} lg={6}>
            <Card
              className={`text-center border border-purple-100 bg-purple-50/50 shadow-card ${isMobile ? 'p-2' : ''}`}
            >
              <Statistic
                title={
                  <span className={isMobile ? 'text-xs' : ''}>New User</span>
                }
                value={newUserCount}
                prefix={<UsergroupAddOutlined className='text-purple-600' />}
                precision={0}
                valueStyle={{
                  color: '#7c3aed',
                  fontSize: statisticFontSize,
                  fontWeight: 'bold',
                }}
              />
            </Card>
          </Col> */}
        </Row>
      </div>

      {/* Members Table */}
      <Card
        className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card overflow-hidden`}
      >
        <UserFilters {...filters} />
        <GalaxyTable
          data={users}
          columns={columns}
          loading={isLoading || isLoadUpUser}
          pagination={{
            current: page,
            pageSize: pageSize,
            total: userGalaxy?.totalItems || 0,
            onChange: (newPage, newPageSize) => {
              updatePage(newPage);
              if (pageSize !== newPageSize) updatePageSize(newPageSize);
            },
          }}
          onSortChange={(newSort) => {
            if (newSort) setSort(newSort);
          }}
          rowKey='id'
          scroll={isMobile ? { x: 800 } : undefined}
          style={{ marginTop: '30px' }}
        />
      </Card>

      <UserDetailsModal
        visible={showDetailsModal}
        user={selectedUser}
        onClose={handleCloseDetailsModal}
        onEdit={(member) => {
          console.log('Edit member:', member);
        }}
      />
    </div>
  );
};

export default UserManagement;
