import { DeleteOutlined, EyeOutlined, MoreOutlined } from '@ant-design/icons';
import {
  Avatar,
  Button,
  Popconfirm,
  Switch,
  Tag,
  Tooltip,
  Typography,
  type TableColumnType,
} from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { UserOptions } from '../apis';
import DropdownAlpha from '@/components/DropdownYF/DropdownAlpha';
import Txt from '@/components/Txt';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive';
import { useUserStore } from '@/stores';
import type { MemberStatusEnums } from '@/utils';
import { formatDate } from '@/utils/tableUtils';

interface UseProps {
  onViewM: (id: string) => void;
  onDelUser: (id: string) => void;
  pendingD: boolean;
  statusTing: boolean;
  handleToggleStatus: (userId: string, status: MemberStatusEnums) => void;
  delting: boolean;
}
const { Text } = Typography;

const useUserColumns = (props: UseProps) => {
  const {
    onViewM,
    pendingD,
    handleToggleStatus,
    statusTing,
    onDelUser,
    delting,
  } = props || {};
  const { t } = useTranslation('members');
  const { t: optionsT } = useTranslation('options');

  const { isDark } = useUserStore();
  const avatarSize = useResponsiveValue({
    xs: 32,
    sm: 36,
    md: 40,
    lg: 40,
    xl: 40,
    '2xl': 40,
  });
  const { isMobile } = useResponsive();
  const columns: Array<TableColumnType<UserOptions>> = useMemo(
    () => [
      {
        title: 'Member Name',
        dataIndex: 'userName',
        key: 'userName',
        render: (userName: string) => (
          <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
            <Avatar
              className='bg-gradient-to-br from-primary to-primary-dark text-white font-medium flex-shrink-0'
              size={avatarSize}
            >
              {userName.charAt(0).toUpperCase()}
            </Avatar>
            <div className='min-w-0 flex-1'>
              <div
                className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}
              >
                <Text
                  className={`font-medium ${isDark ? 'text-gray-100' : 'text-gray-900'} ${isMobile ? 'text-sm' : ''} truncate`}
                >
                  {userName}
                </Text>
              </div>
            </div>
          </div>
        ),
        sorter: {
          multiple: 1,
        },
        width: isMobile ? 200 : undefined,
      },
      {
        title: 'Email',
        dataIndex: 'email',
        key: 'email',
        render: (email: string) => (
          <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
            <div className='flex-1'>
              {email && !isMobile && (
                <Text
                  className={`text-xs ${isDark ? 'text-gray-500' : 'text-gray-400'} block truncate`}
                >
                  {email}
                </Text>
              )}
            </div>
          </div>
        ),
        width: isMobile ? 200 : undefined,
      },
      {
        title: t('passport'),
        dataIndex: 'passportNumber',
        key: 'passportNumber',
        sorter: {
          multiple: 1,
        },
        render: (passport: string) => (
          <div
            className={`font-mono ${isMobile ? 'text-xs' : 'text-sm'} ${
              isDark ? 'bg-gray-700 text-gray-100' : 'bg-gray-50 text-gray-800'
            } ${isMobile ? 'px-2 py-1' : 'px-3 py-1'} rounded-lg inline-block`}
          >
            {isMobile ? passport.slice(-6) : passport}
          </div>
        ),
        width: isMobile ? 100 : undefined,
        responsive: isMobile ? ['lg'] : undefined,
      },
      {
        title: 'Phone Number',
        dataIndex: 'phoneNumber',
        key: 'phoneNumber',
        sorter: {
          multiple: 1,
        },
        render: (phoneNumber: string) => (
          <div
            className={`font-mono ${isMobile ? 'text-xs' : 'text-sm'} ${
              isDark ? 'bg-gray-700 text-gray-100' : 'bg-gray-50 text-gray-800'
            } ${isMobile ? 'px-2 py-1' : 'px-3 py-1'} rounded-lg inline-block`}
          >
            {isMobile ? phoneNumber.slice(-6) : phoneNumber}
          </div>
        ),
        width: isMobile ? 100 : undefined,
        responsive: isMobile ? ['lg'] : undefined,
      },
      {
        key: 'status',
        dataIndex: 'status',
        title: <Txt>Status</Txt>,
        sorter: {
          multiple: 1,
        },
        align: 'center',
        render: (_, record: UserOptions) => {
          return (
            <Tooltip title={t('toggleStatusTooltip')}>
              <Switch
                loading={statusTing}
                checked={!!record.status}
                checkedChildren={optionsT('activeStaffStatusEnums')}
                unCheckedChildren={optionsT('inactiveStaffStatusEnums')}
                onClick={() => {
                  handleToggleStatus(record.id, record.status);
                }}
              ></Switch>
            </Tooltip>
          );
        },
      },
      {
        title: t('registrationDate'),
        dataIndex: 'createdAt',
        key: 'createdAt',
        sorter: {
          multiple: 1,
        },
        render: (_: string, record: UserOptions) => {
          const [createdDate, createdTime] = formatDate(record.createdAt).split(
            ',',
          );
          const [updatedDate, updatedTime] = formatDate(record.updatedAt).split(
            ',',
          );

          return (
            <div>
              {/* Created At */}
              <Text
                className={`${isDark ? 'text-gray-100' : 'text-gray-900'} block ${isMobile ? 'text-xs' : ''}`}
              >
                {t('createdAt')}: {createdDate}
              </Text>
              {!isMobile && (
                <Text
                  className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}
                >
                  {createdTime}
                </Text>
              )}

              {/* Updated At */}
              <Text
                className={`${isDark ? 'text-gray-100' : 'text-gray-900'} block ${isMobile ? 'text-xs' : ''} mt-1`}
              >
                {t('updatedAt')}: {updatedDate}
              </Text>
              {!isMobile && (
                <Text
                  className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}
                >
                  {updatedTime}
                </Text>
              )}
            </div>
          );
        },
      },
      {
        title: 'Created By',
        dataIndex: 'createdByName',
        key: 'createdByName',
        render: (createdByName: string) => (
          <div
            className={`font-mono ${isMobile ? 'text-xs' : 'text-sm'} ${
              isDark ? 'bg-gray-700 text-gray-100' : 'bg-gray-50 text-gray-800'
            } ${isMobile ? 'px-2 py-1' : 'px-3 py-1'} rounded-lg inline-block`}
          >
            {isMobile ? createdByName.slice(-6) : createdByName}
          </div>
        ),
        width: isMobile ? 100 : undefined,
        responsive: isMobile ? ['lg'] : undefined,
      },
      {
        title: 'NewUser',
        dataIndex: 'isNew',
        key: 'isNew',
        align: 'center',
        render: (isNew: boolean) => (
          <Tag color={isNew ? 'gold' : 'volcano'}>{isNew ? 'Yes' : 'No'}</Tag>
        ),
        filters: [
          { text: 'Yes', value: 'True' },
          { text: 'No', value: 'False' },
        ],
        width: isMobile ? 80 : undefined,
      },
      {
        title: t('actions'),
        key: 'actions',
        align: 'center',
        width: isMobile ? 60 : 120,
        render: (_: UserOptions, record: UserOptions) => {
          const dropUserItems = [
            {
              key: 'details',
              item: (
                <div>
                  <Button
                    type='text'
                    icon={<EyeOutlined />}
                    className={`hover:${isDark ? 'bg-primary/20 text-primary' : 'bg-primary/10 text-primary'} rounded-lg`}
                    onClick={() => {
                      onViewM(record.id);
                    }}
                    loading={pendingD}
                    size={isMobile ? 'small' : 'middle'}
                  >
                    {!isMobile && 'View Detail'}
                  </Button>
                </div>
              ),
            },
            {
              key: 'deleteMb',
              item: (
                <Popconfirm
                  title='Are you sure you want to delete this counter?'
                  onConfirm={() => onDelUser(record.id)}
                  okText='Yes'
                  cancelText='No'
                  okButtonProps={{ loading: delting }}
                >
                  <Button
                    icon={<DeleteOutlined />}
                    danger
                    type='text'
                    className='w-full text-left'
                  >
                    Delete Member
                  </Button>
                </Popconfirm>
              ),
            },
          ];
          return (
            <DropdownAlpha
              buttonProps={{
                type: 'default',
                size: isMobile ? 'small' : 'middle',
              }}
              icon={<MoreOutlined />}
              items={dropUserItems}
              noUnderLink
              gap={8}
              itemHeight='fit-content'
              pannelMaxHeight={200}
              dontStickDown={true}
            />
          );
        },
      },
    ],
    [
      avatarSize,
      isDark,
      isMobile,
      onViewM,
      t,
      pendingD,
      handleToggleStatus,
      statusTing,
      optionsT,
      delting,
      onDelUser,
    ],
  );
  return columns;
};
export default useUserColumns;
