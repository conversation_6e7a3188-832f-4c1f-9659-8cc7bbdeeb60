import { useApiFilters } from '@/hooks';
import type { MemberStatusEnums } from '@/utils';

const useUserFilter = () => {
  const apiFilters = useApiFilters();

  const filterByUserName = (userName: string) => {
    apiFilters.addFilter({
      key: 'userName',
      operator: 'c',
      value: userName,
    });
  };
  const clearUserNameFilter = () => {
    apiFilters.removeFilter('userName');
  };

  const filterByStatus = (status: MemberStatusEnums) => {
    apiFilters.addFilter({
      key: 'status',
      operator: 'eq',
      value: status,
    });
  };
  const clearStatusFilter = () => {
    apiFilters.removeFilter('status');
  };

  const filterByDateRange = (startDate: string, endDate: string) => {
    apiFilters.addFilter({
      key: 'createdAt',
      operator: 'gte',
      value: startDate,
    });
    apiFilters.addFilter({
      key: 'createdAt',
      operator: 'lte',
      value: endDate,
    });
  };
  const clearDateRangeFilter = () => {
    apiFilters.removeFilter('createdAt');
  };

  const filterByUpdateDateRange = (startDate: string, endDate: string) => {
    apiFilters.addFilter({
      key: 'updatedAt',
      operator: 'gte',
      value: startDate,
    });
    apiFilters.addFilter({
      key: 'updatedAt',
      operator: 'lte',
      value: endDate,
    });
  };
  const clearUpdateDateRangeFilter = () => {
    apiFilters.removeFilter('updatedAt');
  };

  return {
    ...apiFilters,
    filterByStatus,
    clearStatusFilter,
    filterByDateRange,
    filterByUserName,
    clearUserNameFilter,
    clearDateRangeFilter,
    filterByUpdateDateRange,
    clearUpdateDateRangeFilter,
  };
};

export default useUserFilter;
