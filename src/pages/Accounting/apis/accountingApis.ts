import {
  mockFinancialStatements,
  mockTransactionSummaries,
  mockReconciliationReports,
  mockDelay,
} from '@/api/mockData.ts';
import type {
  ApiResponse,
  FinancialStatement,
  TransactionSummary,
  ReconciliationReport,
  AccountingFilters,
} from '@/types';

// Fetch accounting stats
export const fetchAccountingStats = async (): Promise<ApiResponse<any>> => {
  await mockDelay();

  // Calculate stats from mock data
  const latestStatement = mockFinancialStatements[0];
  const reconciliationStats = {
    balanced: mockReconciliationReports.filter((r) => r.status === 'balanced')
      .length,
    variance: mockReconciliationReports.filter((r) => r.status === 'variance')
      .length,
    pending: mockReconciliationReports.filter((r) => r.status === 'pending')
      .length,
  };

  const stats = {
    totalRevenue: latestStatement?.data.revenue.total || 0,
    totalExpenses: latestStatement?.data.expenses.total || 0,
    netIncome:
      (latestStatement?.data.revenue.total || 0) -
      (latestStatement?.data.expenses.total || 0),
    totalAssets: latestStatement?.data.assets.total || 0,
    totalLiabilities: latestStatement?.data.liabilities.total || 0,
    equity: latestStatement?.data.equity.total || 0,
    cashFlow: latestStatement?.data.assets.cash || 0,
    reconciliationStatus: reconciliationStats,
  };

  return {
    data: stats,
    success: true,
    message: 'Accounting stats fetched successfully',
  };
};

// Fetch financial statements
export const fetchFinancialStatements = async (
  filters?: AccountingFilters,
): Promise<ApiResponse<FinancialStatement[]>> => {
  await mockDelay();

  let filteredStatements = [...mockFinancialStatements];

  // Apply filters
  if (filters?.reportType) {
    filteredStatements = filteredStatements.filter(
      (s) => s.type === filters.reportType,
    );
  }

  if (filters?.period) {
    filteredStatements = filteredStatements.filter(
      (s) => s.period.type === filters.period,
    );
  }

  if (filters?.dateRange) {
    const [startDate, endDate] = filters.dateRange;
    filteredStatements = filteredStatements.filter((s) => {
      const statementStart = new Date(s.period.startDate);
      const statementEnd = new Date(s.period.endDate);
      const filterStart = new Date(startDate);
      const filterEnd = new Date(endDate);

      return statementStart >= filterStart && statementEnd <= filterEnd;
    });
  }

  // Sort by date (newest first)
  filteredStatements.sort(
    (a, b) =>
      new Date(b.period.endDate).getTime() -
      new Date(a.period.endDate).getTime(),
  );

  return {
    data: filteredStatements,
    success: true,
    message: 'Financial statements fetched successfully',
  };
};

// Fetch transaction summaries
export const fetchTransactionSummaries = async (
  filters?: AccountingFilters,
): Promise<ApiResponse<TransactionSummary[]>> => {
  await mockDelay();

  let filteredSummaries = [...mockTransactionSummaries];

  // Apply date range filter
  if (filters?.dateRange) {
    const [startDate, endDate] = filters.dateRange;
    filteredSummaries = filteredSummaries.filter((s) => {
      const summaryStart = new Date(s.period.startDate);
      const summaryEnd = new Date(s.period.endDate);
      const filterStart = new Date(startDate);
      const filterEnd = new Date(endDate);

      return summaryStart >= filterStart && summaryEnd <= filterEnd;
    });
  }

  // Sort by date (newest first)
  filteredSummaries.sort(
    (a, b) =>
      new Date(b.period.endDate).getTime() -
      new Date(a.period.endDate).getTime(),
  );

  return {
    data: filteredSummaries,
    success: true,
    message: 'Transaction summaries fetched successfully',
  };
};

// Fetch reconciliation reports
export const fetchReconciliationReports = async (
  filters?: AccountingFilters,
): Promise<ApiResponse<ReconciliationReport[]>> => {
  await mockDelay();

  let filteredReports = [...mockReconciliationReports];

  // Apply filters
  if (filters?.registerId) {
    filteredReports = filteredReports.filter(
      (r) => r.registerId === filters.registerId,
    );
  }
  if (filters?.reconciliationStatus) {
    filteredReports = filteredReports.filter(
      (r) => r.status === filters.reconciliationStatus,
    );
  }

  if (filters?.dateRange) {
    const [startDate, endDate] = filters.dateRange;
    filteredReports = filteredReports.filter((r) => {
      const reportStart = new Date(r.period.startDate);
      const reportEnd = new Date(r.period.endDate);
      const filterStart = new Date(startDate);
      const filterEnd = new Date(endDate);

      return reportStart >= filterStart && reportEnd <= filterEnd;
    });
  }

  // Sort by date (newest first)
  filteredReports.sort(
    (a, b) =>
      new Date(b.period.endDate).getTime() -
      new Date(a.period.endDate).getTime(),
  );

  return {
    data: filteredReports,
    success: true,
    message: 'Reconciliation reports fetched successfully',
  };
};

// Generate financial statement
export const generateFinancialStatement = async (
  type: FinancialStatement['type'],
  period: FinancialStatement['period'],
): Promise<ApiResponse<FinancialStatement>> => {
  await mockDelay();

  // Simulate generating new financial data based on current transactions
  const baseData = mockFinancialStatements[0].data;
  const variance = 0.8 + Math.random() * 0.4; // 80% to 120% variance

  const newStatement: FinancialStatement = {
    id: `FS_${Date.now()}`,
    type,
    period,
    data: {
      revenue: {
        gaming: Math.round(baseData.revenue.gaming * variance),
        food: Math.round(baseData.revenue.food * variance),
        beverages: Math.round(baseData.revenue.beverages * variance),
        other: Math.round(baseData.revenue.other * variance),
        total: 0, // Will be calculated below
      },
      expenses: {
        salaries: Math.round(baseData.expenses.salaries * variance),
        utilities: Math.round(baseData.expenses.utilities * variance),
        maintenance: Math.round(baseData.expenses.maintenance * variance),
        supplies: Math.round(baseData.expenses.supplies * variance),
        marketing: Math.round(baseData.expenses.marketing * variance),
        other: Math.round(baseData.expenses.other * variance),
        total: 0, // Will be calculated below
      },
      assets: {
        cash: Math.round(baseData.assets.cash * variance),
        inventory: Math.round(baseData.assets.inventory * variance),
        equipment: Math.round(baseData.assets.equipment * variance),
        other: Math.round(baseData.assets.other * variance),
        total: 0, // Will be calculated below
      },
      liabilities: {
        accountsPayable: Math.round(
          baseData.liabilities.accountsPayable * variance,
        ),
        loans: Math.round(baseData.liabilities.loans * variance),
        other: Math.round(baseData.liabilities.other * variance),
        total: 0, // Will be calculated below
      },
      equity: {
        capital: baseData.equity.capital, // Keep capital constant
        retainedEarnings: Math.round(
          baseData.equity.retainedEarnings * variance,
        ),
        total: 0, // Will be calculated below
      },
    },
    generatedAt: new Date().toISOString(),
    generatedBy: 'EMP001', // Current user
  };

  // Calculate totals
  newStatement.data.revenue.total = Object.values(newStatement.data.revenue)
    .filter(
      (v) => typeof v === 'number' && v !== newStatement.data.revenue.total,
    )
    .reduce((sum, val) => sum + val, 0);

  newStatement.data.expenses.total = Object.values(newStatement.data.expenses)
    .filter(
      (v) => typeof v === 'number' && v !== newStatement.data.expenses.total,
    )
    .reduce((sum, val) => sum + val, 0);

  newStatement.data.assets.total = Object.values(newStatement.data.assets)
    .filter(
      (v) => typeof v === 'number' && v !== newStatement.data.assets.total,
    )
    .reduce((sum, val) => sum + val, 0);

  newStatement.data.liabilities.total = Object.values(
    newStatement.data.liabilities,
  )
    .filter(
      (v) => typeof v === 'number' && v !== newStatement.data.liabilities.total,
    )
    .reduce((sum, val) => sum + val, 0);

  newStatement.data.equity.total = Object.values(newStatement.data.equity)
    .filter(
      (v) => typeof v === 'number' && v !== newStatement.data.equity.total,
    )
    .reduce((sum, val) => sum + val, 0);

  // Add to mock data for future retrieval
  mockFinancialStatements.unshift(newStatement);
  return {
    data: newStatement,
    success: true,
    message: 'Financial statement generated successfully',
  };
};

// Generate transaction summary
export const generateTransactionSummary = async (period: {
  startDate: string;
  endDate: string;
}): Promise<ApiResponse<TransactionSummary>> => {
  await mockDelay();

  // Simulate generating new transaction summary
  const baseData = mockTransactionSummaries[0];
  const variance = 0.7 + Math.random() * 0.6; // 70% to 130% variance

  const newSummary: TransactionSummary = {
    id: `TS_${Date.now()}`,
    period,
    summary: {
      totalTransactions: Math.round(
        baseData.summary.totalTransactions * variance,
      ),
      totalAmount: Math.round(baseData.summary.totalAmount * variance),
      averageTransaction: 0, // Will be calculated below
      byType: {
        sale: {
          count: Math.round(baseData.summary.byType.sale.count * variance),
          amount: Math.round(baseData.summary.byType.sale.amount * variance),
        },
        refund: {
          count: Math.round(baseData.summary.byType.refund.count * variance),
          amount: Math.round(baseData.summary.byType.refund.amount * variance),
        },
        void: {
          count: Math.round(baseData.summary.byType.void.count * variance),
          amount: Math.round(baseData.summary.byType.void.amount * variance),
        },
        cash_in: {
          count: Math.round(baseData.summary.byType.cash_in.count * variance),
          amount: Math.round(baseData.summary.byType.cash_in.amount * variance),
        },
        cash_out: {
          count: Math.round(baseData.summary.byType.cash_out.count * variance),
          amount: Math.round(
            baseData.summary.byType.cash_out.amount * variance,
          ),
        },
      },
      byPaymentMethod: {
        cash: {
          count: Math.round(
            baseData.summary.byPaymentMethod.cash.count * variance,
          ),
          amount: Math.round(
            baseData.summary.byPaymentMethod.cash.amount * variance,
          ),
        },
        card: {
          count: Math.round(
            baseData.summary.byPaymentMethod.card.count * variance,
          ),
          amount: Math.round(
            baseData.summary.byPaymentMethod.card.amount * variance,
          ),
        },
        digital_wallet: {
          count: Math.round(
            baseData.summary.byPaymentMethod.digital_wallet.count * variance,
          ),
          amount: Math.round(
            baseData.summary.byPaymentMethod.digital_wallet.amount * variance,
          ),
        },
        crypto: {
          count: Math.round(
            baseData.summary.byPaymentMethod.crypto.count * variance,
          ),
          amount: Math.round(
            baseData.summary.byPaymentMethod.crypto.amount * variance,
          ),
        },
      },
      byEmployee: baseData.summary.byEmployee, // Keep employee data same for simplicity
      byRegister: baseData.summary.byRegister, // Keep register data same for simplicity
    },
    transactions: baseData.transactions, // Keep transaction details same
    transactionsByPaymentMethod: baseData.transactionsByPaymentMethod,
    transactionsByEmployee: baseData.transactionsByEmployee,
    transactionsByRegister: baseData.transactionsByRegister,
    generatedAt: new Date().toISOString(),
  };

  // Calculate average transaction
  newSummary.summary.averageTransaction =
    newSummary.summary.totalAmount / newSummary.summary.totalTransactions;

  // Add to mock data for future retrieval
  mockTransactionSummaries.unshift(newSummary);

  return {
    data: newSummary,
    success: true,
    message: 'Transaction summary generated successfully',
  };
};

// Generate reconciliation report
export const generateReconciliationReport = async (
  registerId: string,
  period: { startDate: string; endDate: string },
): Promise<ApiResponse<ReconciliationReport>> => {
  await mockDelay();

  // Find register name
  const registerNames: Record<string, string> = {
    REG001: 'Main Counter',
    REG002: 'VIP Lounge',
    REG003: 'Gaming Floor',
  };

  // Simulate generating new reconciliation report
  const baseData = mockReconciliationReports[0];
  const variance = 0.8 + Math.random() * 0.4; // 80% to 120% variance
  const hasVariance = Math.random() > 0.7; // 30% chance of variance
  const varianceAmount = hasVariance
    ? Math.round((Math.random() - 0.5) * 100)
    : 0; // -50 to +50

  const expectedBalance = Math.round(baseData.expectedBalance * variance);
  const actualBalance = expectedBalance + varianceAmount;

  const newReport: ReconciliationReport = {
    id: `REC_${Date.now()}_${registerId}`,
    registerId,
    registerName: registerNames[registerId] || 'Unknown Register',
    shiftId: `SHIFT_${Date.now()}_001`,
    period,
    openingBalance: baseData.openingBalance,
    closingBalance: baseData.closingBalance,
    expectedBalance,
    actualBalance,
    variance: actualBalance - expectedBalance,
    transactions: {
      sales: Math.round(baseData.transactions.sales * variance),
      refunds: Math.round(baseData.transactions.refunds * variance),
      cashIn: Math.round(baseData.transactions.cashIn * variance),
      cashOut: Math.round(baseData.transactions.cashOut * variance),
      voids: Math.round(baseData.transactions.voids * variance),
    },
    discrepancies:
      varianceAmount !== 0
        ? [
            {
              type: varianceAmount > 0 ? 'overage' : 'shortage',
              amount: Math.abs(varianceAmount),
              reason:
                varianceAmount > 0
                  ? 'Customer overpayment not processed as change'
                  : 'Minor counting discrepancy',
            },
          ]
        : [],
    status: varianceAmount === 0 ? 'balanced' : 'variance',
    reconciledBy: 'EMP001',
    reconciledAt: new Date().toISOString(),
    notes:
      varianceAmount === 0
        ? 'Perfect balance - no discrepancies'
        : `${Math.abs(varianceAmount)} ${varianceAmount > 0 ? 'overage' : 'shortage'} - within acceptable range`,
  };

  // Add to mock data for future retrieval
  mockReconciliationReports.unshift(newReport);

  return {
    data: newReport,
    success: true,
    message: 'Reconciliation report generated successfully',
  };
};

// Update reconciliation report
export const updateReconciliationReport = async (
  reportId: string,
  updates: Partial<ReconciliationReport>,
): Promise<ApiResponse<ReconciliationReport>> => {
  await mockDelay();

  const reportIndex = mockReconciliationReports.findIndex(
    (r) => r.id === reportId,
  );
  if (reportIndex === -1) {
    throw new Error('Reconciliation report not found');
  }

  // Update the report
  mockReconciliationReports[reportIndex] = {
    ...mockReconciliationReports[reportIndex],
    ...updates,
    reconciledAt: new Date().toISOString(),
    reconciledBy: 'EMP001', // Current user
  };

  // Recalculate variance if actual balance changed
  if (updates.actualBalance !== undefined) {
    const report = mockReconciliationReports[reportIndex];
    report.variance = report.actualBalance - report.expectedBalance;

    // Update status based on variance
    if (report.variance === 0) {
      report.status = 'balanced';
    } else {
      report.status = 'variance';
    }
  }

  return {
    data: mockReconciliationReports[reportIndex],
    success: true,
    message: 'Reconciliation report updated successfully',
  };
};
