import {
  ReconciliationOutlined,
  DownloadOutlined,
  EyeOutlined,
  EditOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  DollarOutlined,
  CalculatorOutlined,
  StockOutlined,
  DownOutlined,
} from '@ant-design/icons';
import {
  Card,
  Table,
  Button,
  Select,
  DatePicker,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Alert,
  Modal,
  Form,
  Input,
  InputNumber,
} from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import GenerateReportModal from './modals/GenerateReportModal';
import { useReconciliationReports } from '@/hooks/useAccounting.ts';
import type { ReconciliationReport } from '@/types';
import { formatCurrency, formatDate } from '@/utils/tableUtils.ts';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;
const { TextArea } = Input;

const ReconciliationReports: React.FC = () => {
  const { t } = useTranslation();
  const [selectedReport, setSelectedReport] =
    useState<ReconciliationReport | null>(null);
  const [showReconcileModal, setShowReconcileModal] = useState(false);
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [reconcileForm] = Form.useForm();
  const [registerId, setRegisterId] = useState<string>('');
  const [status, setStatus] = useState<
    ReconciliationReport['status'] | undefined
  >();
  const [dateRange, setDateRange] = useState<[string, string] | undefined>();

  const { data: reportsResponse, isLoading } = useReconciliationReports({
    registerId,
    reconciliationStatus: status,
    dateRange,
  });

  const reports = reportsResponse?.data || [];

  const handleViewReport = (report: ReconciliationReport) => {
    setSelectedReport(report);
  };

  const handleReconcile = (report: ReconciliationReport) => {
    setSelectedReport(report);
    reconcileForm.setFieldsValue({
      actualBalance: report.actualBalance,
      notes: report.notes || '',
    });
    setShowReconcileModal(true);
  };

  const handleDownload = (report: ReconciliationReport) => {
    // In a real app, this would trigger a download
    console.log('Downloading report:', report.id);
  };

  const handleReconcileSubmit = async (values: any) => {
    if (!selectedReport) return;

    // In a real app, this would call an API to update the reconciliation
    console.log('Reconciling report:', selectedReport.id, values);
    setShowReconcileModal(false);
    reconcileForm.resetFields();
    setSelectedReport(null);
  };

  const getStatusColor = (status: ReconciliationReport['status']) => {
    switch (status) {
      case 'balanced':
        return 'green';
      case 'variance':
        return 'orange';
      case 'pending':
        return 'blue';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: ReconciliationReport['status']) => {
    switch (status) {
      case 'balanced':
        return <CheckCircleOutlined />;
      case 'variance':
        return <WarningOutlined />;
      case 'pending':
        return <ClockCircleOutlined />;
      default:
        return <ExclamationCircleOutlined />;
    }
  };

  const getVarianceColor = (variance: number) => {
    if (variance === 0) return 'text-green-600';
    if (Math.abs(variance) <= 50) return 'text-orange-600';
    return 'text-red-600';
  };

  const columns = [
    {
      title: t('accounting.register'),
      dataIndex: 'registerName',
      key: 'registerName',
      render: (name: string, record: ReconciliationReport) => (
        <div>
          <div className='font-medium text-gray-900'>{name}</div>
          <div className='text-sm text-gray-500'>ID: {record.registerId}</div>
        </div>
      ),
    },
    {
      title: t('accounting.period'),
      key: 'period',
      render: (_: any, record: ReconciliationReport) => (
        <div>
          <div className='font-medium text-gray-900'>
            {formatDate(record.period.startDate)}
          </div>
          <div className='text-sm text-gray-500'>
            to {formatDate(record.period.endDate)}
          </div>
        </div>
      ),
    },
    {
      title: t('accounting.expectedBalance'),
      dataIndex: 'expectedBalance',
      key: 'expectedBalance',
      render: (balance: number) => (
        <span className='font-medium text-blue-600'>
          {formatCurrency(balance)}
        </span>
      ),
    },
    {
      title: t('accounting.actualBalance'),
      dataIndex: 'actualBalance',
      key: 'actualBalance',
      render: (balance: number) => (
        <span className='font-medium text-green-600'>
          {formatCurrency(balance)}
        </span>
      ),
    },
    {
      title: t('accounting.variance'),
      dataIndex: 'variance',
      key: 'variance',
      render: (variance: number) => (
        <div className='flex items-center gap-2'>
          {variance === 0 ? (
            <CheckCircleOutlined className='text-green-600' />
          ) : variance > 0 ? (
            <StockOutlined className='text-orange-600' />
          ) : (
            <DownOutlined className='text-red-600' />
          )}
          <span className={`font-bold ${getVarianceColor(variance)}`}>
            {variance === 0 ? '—' : formatCurrency(Math.abs(variance))}
          </span>
          {variance !== 0 && (
            <span className='text-xs text-gray-500'>
              ({variance > 0 ? 'overage' : 'shortage'})
            </span>
          )}
        </div>
      ),
      sorter: (a: ReconciliationReport, b: ReconciliationReport) =>
        Math.abs(a.variance) - Math.abs(b.variance),
    },
    {
      title: t('accounting.status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: ReconciliationReport['status']) => (
        <Tag
          color={getStatusColor(status)}
          icon={getStatusIcon(status)}
          className='font-medium'
        >
          {t(`accounting.reconciliationStatuses.${status}`)}
        </Tag>
      ),
      filters: [
        { text: 'Balanced', value: 'balanced' },
        { text: 'Variance', value: 'variance' },
        { text: 'Pending', value: 'pending' },
      ],
      onFilter: (value: any, record: ReconciliationReport) =>
        record.status === value,
    },
    {
      title: t('common.actions'),
      key: 'actions',
      width: 150,
      render: (_: any, record: ReconciliationReport) => (
        <Space size='small'>
          <Button
            type='text'
            icon={<EyeOutlined />}
            onClick={() => handleViewReport(record)}
            className='text-blue-600 hover:text-blue-800'
            title={t('common.view')}
          />
          {record.status === 'pending' && (
            <Button
              type='text'
              icon={<EditOutlined />}
              onClick={() => handleReconcile(record)}
              className='text-orange-600 hover:text-orange-800'
              title={t('accounting.reconcile')}
            />
          )}
          <Button
            type='text'
            icon={<DownloadOutlined />}
            onClick={() => handleDownload(record)}
            className='text-green-600 hover:text-green-800'
            title={t('common.download')}
          />
        </Space>
      ),
    },
  ];

  const renderReportDetails = (report: ReconciliationReport) => {
    return (
      <div className='space-y-6'>
        {/* Report Header */}
        <div className='text-center border-b pb-4'>
          <Title level={3}>{t('accounting.reconciliationReport')}</Title>
          <Text className='text-gray-600'>
            {report.registerName} - {formatDate(report.period.startDate)} to{' '}
            {formatDate(report.period.endDate)}
          </Text>
        </div>

        {/* Status Alert */}
        {report.variance !== 0 && (
          <Alert
            message={
              report.variance > 0
                ? t('accounting.overage')
                : t('accounting.shortage')
            }
            description={`${t('accounting.varianceAmount')}: ${formatCurrency(
              Math.abs(report.variance),
            )}`}
            type={Math.abs(report.variance) <= 50 ? 'warning' : 'error'}
            showIcon
            className='rounded-lg'
          />
        )}

        {/* Balance Summary */}
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card className='text-center border border-blue-200 bg-blue-50/50'>
              <Statistic
                title={t('accounting.openingBalance')}
                value={report.openingBalance}
                formatter={(value) => formatCurrency(Number(value))}
                prefix={<DollarOutlined className='text-blue-600' />}
                valueStyle={{ color: '#1d4ed8' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className='text-center border border-green-200 bg-green-50/50'>
              <Statistic
                title={t('accounting.expectedBalance')}
                value={report.expectedBalance}
                formatter={(value) => formatCurrency(Number(value))}
                prefix={<CalculatorOutlined className='text-green-600' />}
                valueStyle={{ color: '#059669' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card className='text-center border border-purple-200 bg-purple-50/50'>
              <Statistic
                title={t('accounting.actualBalance')}
                value={report.actualBalance}
                formatter={(value) => formatCurrency(Number(value))}
                prefix={<DollarOutlined className='text-purple-600' />}
                valueStyle={{ color: '#7c3aed' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card
              className={`text-center border ${
                report.variance === 0
                  ? 'border-green-200 bg-green-50/50'
                  : 'border-red-200 bg-red-50/50'
              }`}
            >
              <Statistic
                title={t('accounting.variance')}
                value={Math.abs(report.variance)}
                formatter={(value) =>
                  report.variance === 0 ? '—' : formatCurrency(Number(value))
                }
                prefix={
                  report.variance === 0 ? (
                    <CheckCircleOutlined className='text-green-600' />
                  ) : (
                    <WarningOutlined
                      className={
                        report.variance > 0 ? 'text-orange-600' : 'text-red-600'
                      }
                    />
                  )
                }
                valueStyle={{
                  color: report.variance === 0 ? '#059669' : '#dc2626',
                }}
              />
            </Card>
          </Col>
        </Row>

        {/* Transaction Breakdown */}
        <Card
          title={t('accounting.transactionBreakdown')}
          className='border-0 shadow-sm'
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={8}>
              <div className='p-4 bg-green-50 rounded-lg border border-green-200'>
                <div className='flex items-center justify-between'>
                  <span className='text-green-700 font-medium'>
                    {t('accounting.sales')}
                  </span>
                  <span className='text-green-600 font-bold'>
                    {formatCurrency(report.transactions.sales)}
                  </span>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <div className='p-4 bg-red-50 rounded-lg border border-red-200'>
                <div className='flex items-center justify-between'>
                  <span className='text-red-700 font-medium'>
                    {t('accounting.refunds')}
                  </span>
                  <span className='text-red-600 font-bold'>
                    {formatCurrency(Math.abs(report.transactions.refunds))}
                  </span>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <div className='p-4 bg-blue-50 rounded-lg border border-blue-200'>
                <div className='flex items-center justify-between'>
                  <span className='text-blue-700 font-medium'>
                    {t('accounting.cashIn')}
                  </span>
                  <span className='text-blue-600 font-bold'>
                    {formatCurrency(report.transactions.cashIn)}
                  </span>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <div className='p-4 bg-orange-50 rounded-lg border border-orange-200'>
                <div className='flex items-center justify-between'>
                  <span className='text-orange-700 font-medium'>
                    {t('accounting.cashOut')}
                  </span>
                  <span className='text-orange-600 font-bold'>
                    {formatCurrency(Math.abs(report.transactions.cashOut))}
                  </span>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} lg={8}>
              <div className='p-4 bg-gray-50 rounded-lg border border-gray-200'>
                <div className='flex items-center justify-between'>
                  <span className='text-gray-700 font-medium'>
                    {t('accounting.voids')}
                  </span>
                  <span className='text-gray-600 font-bold'>
                    {formatCurrency(Math.abs(report.transactions.voids))}
                  </span>
                </div>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Discrepancies */}
        {report.discrepancies.length > 0 && (
          <Card
            title={t('accounting.discrepancies')}
            className='border-0 shadow-sm'
          >
            <div className='space-y-3'>
              {report.discrepancies.map((discrepancy, index) => (
                <Alert
                  key={index}
                  message={`${
                    discrepancy.type === 'overage'
                      ? t('accounting.overage')
                      : t('accounting.shortage')
                  }: ${formatCurrency(discrepancy.amount)}`}
                  description={discrepancy.reason}
                  type={discrepancy.type === 'overage' ? 'warning' : 'error'}
                  showIcon
                  className='rounded-lg'
                />
              ))}
            </div>
          </Card>
        )}

        {/* Reconciliation Info */}
        {report.reconciledBy && (
          <Card
            title={t('accounting.reconciliationInfo')}
            className='border-0 shadow-sm'
          >
            <div className='space-y-2'>
              <div className='flex justify-between'>
                <span className='text-gray-700'>
                  {t('accounting.reconciledBy')}
                </span>
                <span className='font-medium'>{report.reconciledBy}</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-gray-700'>
                  {t('accounting.reconciledAt')}
                </span>
                <span className='font-medium'>
                  {formatDate(report.reconciledAt!)}
                </span>
              </div>
              {report.notes && (
                <div>
                  <div className='text-gray-700 mb-2'>
                    {t('accounting.notes')}
                  </div>
                  <div className='bg-gray-50 p-3 rounded-lg text-gray-800'>
                    {report.notes}
                  </div>
                </div>
              )}
            </div>
          </Card>
        )}
      </div>
    );
  };

  return (
    <div className='space-y-6'>
      {/* Filters */}
      <Card className='rounded-lg border-0 shadow-sm'>
        <div className='flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between'>
          <div className='flex flex-col sm:flex-row gap-4'>
            <Select
              placeholder={t('accounting.selectRegister')}
              allowClear
              value={registerId || undefined}
              onChange={setRegisterId}
              className='w-48'
            >
              <Option value='REG001'>Main Counter</Option>
              <Option value='REG002'>VIP Lounge</Option>
              <Option value='REG003'>Gaming Floor</Option>
            </Select>
            <Select
              placeholder={t('accounting.selectStatus')}
              allowClear
              value={status || undefined}
              onChange={setStatus}
              className='w-32'
            >
              <Option value='balanced'>
                {t('accounting.reconciliationStatuses.balanced')}
              </Option>
              <Option value='variance'>
                {t('accounting.reconciliationStatuses.variance')}
              </Option>
              <Option value='pending'>
                {t('accounting.reconciliationStatuses.pending')}
              </Option>
            </Select>
            <RangePicker
              onChange={(dates) => {
                setDateRange(dates as unknown as [string, string]);
              }}
              className='w-64'
            />
          </div>
          <Button
            type='primary'
            icon={<ReconciliationOutlined />}
            className='bg-blue-600 hover:bg-blue-700'
            onClick={() => setShowGenerateModal(true)}
          >
            {t('accounting.generateReport')}
          </Button>
        </div>
      </Card>

      {/* Report Details View */}
      {selectedReport && (
        <Card
          title={
            <div className='flex items-center justify-between'>
              <span className='flex items-center gap-2'>
                <ReconciliationOutlined className='text-blue-600' />
                {t('accounting.reportDetails')}
              </span>
              <Button
                onClick={() => setSelectedReport(null)}
                className='text-gray-500'
              >
                {t('common.close')}
              </Button>
            </div>
          }
          className='rounded-lg border-0 shadow-sm'
        >
          {renderReportDetails(selectedReport)}
        </Card>
      )}

      {/* Reports Table */}
      <Card
        title={
          <span className='flex items-center gap-2'>
            <ReconciliationOutlined className='text-blue-600' />
            {t('accounting.reconciliationReports')}
          </span>
        }
        className='rounded-lg border-0 shadow-sm'
      >
        <Table
          dataSource={reports}
          columns={columns}
          rowKey='id'
          loading={isLoading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} reports`,
          }}
        />
      </Card>

      {/* Reconcile Modal */}
      <Modal
        title={t('accounting.reconcileReport')}
        open={showReconcileModal}
        onCancel={() => {
          setShowReconcileModal(false);
          reconcileForm.resetFields();
        }}
        footer={[
          <Button key='cancel' onClick={() => setShowReconcileModal(false)}>
            {t('common.cancel')}
          </Button>,
          <Button
            key='submit'
            type='primary'
            onClick={() => reconcileForm.submit()}
            className='bg-blue-600 hover:bg-blue-700'
          >
            {t('accounting.reconcile')}
          </Button>,
        ]}
      >
        <Form
          form={reconcileForm}
          layout='vertical'
          onFinish={handleReconcileSubmit}
        >
          <Form.Item
            name='actualBalance'
            label={t('accounting.actualBalance')}
            rules={[
              {
                required: true,
                message: t('accounting.actualBalanceRequired'),
              },
            ]}
          >
            <InputNumber
              className='w-full'
              formatter={(value) =>
                `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
              parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
            />
          </Form.Item>
          <Form.Item name='notes' label={t('accounting.notes')}>
            <TextArea
              rows={4}
              placeholder={t('accounting.reconciliationNotes')}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Generate Report Modal */}
      <GenerateReportModal
        open={showGenerateModal}
        onCancel={() => setShowGenerateModal(false)}
      />
    </div>
  );
};

export default ReconciliationReports;
