import {
  CalendarOutlined,
  DownloadOutlined,
  DownOutlined,
  EyeOutlined,
  FileTextOutlined,
  PrinterOutlined,
  StockOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  DatePicker,
  Divider,
  Progress,
  Row,
  Select,
  Space,
  Statistic,
  Tag,
  Typography,
} from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import GenerateStatementModal from './modals/GenerateStatementModal';
import { GalaxyTable } from '@/components';
import { useFinancialStatements } from '@/hooks/useAccounting.ts';
import { useResponsive } from '@/hooks/useResponsive.ts';
import type { FinancialStatement } from '@/types';
import { formatCurrency, formatDate } from '@/utils/tableUtils.ts';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

const FinancialStatements: React.FC = () => {
  const { t } = useTranslation('financialStatements');
  const { isMobile } = useResponsive();
  const [selectedStatement, setSelectedStatement] =
    useState<FinancialStatement | null>(null);
  const [statementType, setStatementType] = useState<
    FinancialStatement['type'] | undefined
  >();
  const [period, setPeriod] = useState<
    FinancialStatement['period']['type'] | undefined
  >();
  const [dateRange, setDateRange] = useState<[string, string] | undefined>();
  const [showGenerateModal, setShowGenerateModal] = useState(false);

  // Responsive values

  const { data: statementsResponse, isLoading } = useFinancialStatements({
    reportType: statementType,
    period,
    dateRange,
  });

  const statements = statementsResponse?.data || [];

  const handleViewStatement = (statement: FinancialStatement) => {
    setSelectedStatement(statement);
  };

  const handleDownload = (statement: FinancialStatement) => {
    // In a real app, this would trigger a download
    console.log('Downloading statement:', statement.id);
  };

  const handlePrint = (statement: FinancialStatement) => {
    // In a real app, this would trigger printing
    console.log('Printing statement:', statement.id);
  };

  const getStatementTypeColor = (type: FinancialStatement['type']) => {
    switch (type) {
      case 'income_statement':
        return 'green';
      case 'balance_sheet':
        return 'blue';
      case 'cash_flow':
        return 'purple';
      case 'trial_balance':
        return 'orange';
      default:
        return 'default';
    }
  };

  const getPeriodColor = (periodType: string) => {
    switch (periodType) {
      case 'daily':
        return 'cyan';
      case 'weekly':
        return 'blue';
      case 'monthly':
        return 'green';
      case 'quarterly':
        return 'orange';
      case 'yearly':
        return 'red';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      title: t('statementType'),
      dataIndex: 'type',
      key: 'type',
      render: (type: FinancialStatement['type']) => (
        <Tag
          color={getStatementTypeColor(type)}
          className={`font-medium ${isMobile ? 'text-xs' : ''}`}
        >
          {isMobile ? type.replace('_', ' ') : t(`${type}`)}
        </Tag>
      ),
      width: isMobile ? 100 : undefined,
    },
    {
      title: t('period'),
      dataIndex: 'period',
      key: 'period',
      render: (period: FinancialStatement['period']) => (
        <div>
          <Tag
            color={getPeriodColor(period.type)}
            className={isMobile ? 'text-xs' : ''}
          >
            {t(`${period.type}`)}
          </Tag>
          <div
            className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 mt-1`}
          >
            {isMobile
              ? `${formatDate(period.startDate).slice(0, 6)} - ${formatDate(period.endDate).slice(0, 6)}`
              : `${formatDate(period.startDate)} - ${formatDate(period.endDate)}`}
          </div>
        </div>
      ),
      width: isMobile ? 120 : undefined,
    },
    {
      title: t('revenue'),
      dataIndex: ['data', 'revenue', 'total'],
      key: 'revenue',
      render: (revenue: number) => (
        <span
          className={`font-medium text-green-600 ${isMobile ? 'text-sm' : ''}`}
        >
          {formatCurrency(revenue)}
        </span>
      ),
      sorter: (a: FinancialStatement, b: FinancialStatement) =>
        a.data.revenue.total - b.data.revenue.total,
      width: isMobile ? 100 : undefined,
      responsive: isMobile ? ['md'] : undefined,
    },
    {
      title: t('expenses'),
      dataIndex: ['data', 'expenses', 'total'],
      key: 'expenses',
      render: (expenses: number) => (
        <span
          className={`font-medium text-red-600 ${isMobile ? 'text-sm' : ''}`}
        >
          {formatCurrency(expenses)}
        </span>
      ),
      sorter: (a: FinancialStatement, b: FinancialStatement) =>
        a.data.expenses.total - b.data.expenses.total,
      width: isMobile ? 100 : undefined,
      responsive: isMobile ? ['md'] : undefined,
    },
    {
      title: t('netIncome'),
      key: 'netIncome',
      render: (_: unknown, record: FinancialStatement) => {
        const netIncome =
          record.data.revenue.total - record.data.expenses.total;
        return (
          <div className='flex items-center gap-2'>
            {netIncome >= 0 ? (
              <StockOutlined className='text-green-600' />
            ) : (
              <DownOutlined className='text-red-600' />
            )}
            <span
              className={`font-bold ${netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}
            >
              {formatCurrency(netIncome)}
            </span>
          </div>
        );
      },
      sorter: (a: FinancialStatement, b: FinancialStatement) => {
        const aNet = a.data.revenue.total - a.data.expenses.total;
        const bNet = b.data.revenue.total - b.data.expenses.total;
        return aNet - bNet;
      },
    },
    {
      title: t('generatedAt'),
      dataIndex: 'generatedAt',
      key: 'generatedAt',
      render: (date: string) => (
        <div className='text-sm text-gray-600'>{formatDate(date)}</div>
      ),
    },
    {
      title: t('actions'),
      key: 'actions',
      width: isMobile ? 60 : 150,
      render: (_: unknown, record: FinancialStatement) => (
        <Space size='small' direction={isMobile ? 'vertical' : 'horizontal'}>
          <Button
            type='text'
            icon={<EyeOutlined />}
            onClick={() => handleViewStatement(record)}
            className='text-blue-600 hover:text-blue-800'
            title={t('view')}
            size={isMobile ? 'small' : 'large'}
          >
            {!isMobile && t('view')}
          </Button>
          {!isMobile && (
            <>
              <Button
                type='text'
                icon={<DownloadOutlined />}
                onClick={() => handleDownload(record)}
                className='text-green-600 hover:text-green-800'
                title={t('download')}
                size={isMobile ? 'small' : 'large'}
              />
              <Button
                type='text'
                icon={<PrinterOutlined />}
                onClick={() => handlePrint(record)}
                className='text-purple-600 hover:text-purple-800'
                title={t('print')}
                size={isMobile ? 'small' : 'large'}
              />
            </>
          )}
        </Space>
      ),
    },
  ];

  const renderStatementDetails = (statement: FinancialStatement) => {
    const { data } = statement;
    const netIncome = data.revenue.total - data.expenses.total;
    const profitMargin = data.revenue.total
      ? (netIncome / data.revenue.total) * 100
      : 0;

    return (
      <div className='space-y-6'>
        {/* Statement Header */}
        <div className='text-center border-b pb-4'>
          <Title level={3}>{t(`${statement.type}`)}</Title>
          <Text className='text-gray-600'>
            {formatDate(statement.period.startDate)} -
            {formatDate(statement.period.endDate)}
          </Text>
        </div>

        {/* Revenue Section */}
        <Card title={t('revenue')} className='border border-green-200'>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Statistic
                title={t('gaming')}
                value={data.revenue.gaming}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#059669' }}
              />
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Statistic
                title={t('food')}
                value={data.revenue.food}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#059669' }}
              />
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Statistic
                title={t('beverages')}
                value={data.revenue.beverages}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#059669' }}
              />
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Statistic
                title={t('other')}
                value={data.revenue.other}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#059669' }}
              />
            </Col>
          </Row>
          <Divider />
          <Row>
            <Col span={24}>
              <Statistic
                title={t('totalRevenue')}
                value={data.revenue.total}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{
                  color: '#059669',
                  fontSize: '24px',
                  fontWeight: 'bold',
                }}
              />
            </Col>
          </Row>
        </Card>

        {/* Expenses Section */}
        <Card title={t('expenses')} className='border border-red-200'>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Statistic
                title={t('salaries')}
                value={data.expenses.salaries}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#dc2626' }}
              />
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Statistic
                title={t('utilities')}
                value={data.expenses.utilities}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#dc2626' }}
              />
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Statistic
                title={t('maintenance')}
                value={data.expenses.maintenance}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#dc2626' }}
              />
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Statistic
                title={t('supplies')}
                value={data.expenses.supplies}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{ color: '#dc2626' }}
              />
            </Col>
          </Row>
          <Divider />
          <Row>
            <Col span={24}>
              <Statistic
                title={t('totalExpenses')}
                value={data.expenses.total}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{
                  color: '#dc2626',
                  fontSize: '24px',
                  fontWeight: 'bold',
                }}
              />
            </Col>
          </Row>
        </Card>

        {/* Net Income Section */}
        <Card
          title={t('netIncome')}
          className={`border ${netIncome >= 0 ? 'border-green-200' : 'border-red-200'}`}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Statistic
                title={t('netIncome')}
                value={netIncome}
                formatter={(value) => formatCurrency(Number(value))}
                valueStyle={{
                  color: netIncome >= 0 ? '#059669' : '#dc2626',
                  fontSize: '28px',
                  fontWeight: 'bold',
                }}
                prefix={netIncome >= 0 ? <StockOutlined /> : <DownOutlined />}
              />
            </Col>
            <Col xs={24} sm={12}>
              <Statistic
                title={t('profitMargin')}
                value={profitMargin}
                precision={2}
                suffix='%'
                valueStyle={{
                  color: profitMargin >= 0 ? '#059669' : '#dc2626',
                  fontSize: '28px',
                  fontWeight: 'bold',
                }}
              />
            </Col>
          </Row>
          <div className='mt-4'>
            <Progress
              percent={Math.min(Math.abs(profitMargin), 100)}
              strokeColor={profitMargin >= 0 ? '#059669' : '#dc2626'}
              showInfo={false}
            />
          </div>
        </Card>

        {/* Balance Sheet Data (if applicable) */}
        {statement.type === 'balance_sheet' && (
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={8}>
              <Card title={t('assets')} className='border border-blue-200'>
                <div className='space-y-2'>
                  <div className='flex justify-between'>
                    <span>{t('cash')}</span>
                    <span className='font-medium'>
                      {formatCurrency(data.assets.cash)}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span>{t('inventory')}</span>
                    <span className='font-medium'>
                      {formatCurrency(data.assets.inventory)}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span>{t('equipment')}</span>
                    <span className='font-medium'>
                      {formatCurrency(data.assets.equipment)}
                    </span>
                  </div>
                  <Divider className='my-2' />
                  <div className='flex justify-between font-bold text-lg'>
                    <span>{t('totalAssets')}</span>
                    <span className='text-blue-600'>
                      {formatCurrency(data.assets.total)}
                    </span>
                  </div>
                </div>
              </Card>
            </Col>
            <Col xs={24} lg={8}>
              <Card title={t('liabilities')} className='border border-red-200'>
                <div className='space-y-2'>
                  <div className='flex justify-between'>
                    <span>{t('accountsPayable')}</span>
                    <span className='font-medium'>
                      {formatCurrency(data.liabilities.accountsPayable)}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span>{t('loans')}</span>
                    <span className='font-medium'>
                      {formatCurrency(data.liabilities.loans)}
                    </span>
                  </div>
                  <Divider className='my-2' />
                  <div className='flex justify-between font-bold text-lg'>
                    <span>{t('totalLiabilities')}</span>
                    <span className='text-red-600'>
                      {formatCurrency(data.liabilities.total)}
                    </span>
                  </div>
                </div>
              </Card>
            </Col>
            <Col xs={24} lg={8}>
              <Card title={t('equity')} className='border border-green-200'>
                <div className='space-y-2'>
                  <div className='flex justify-between'>
                    <span>{t('capital')}</span>
                    <span className='font-medium'>
                      {formatCurrency(data.equity.capital)}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span>{t('retainedEarnings')}</span>
                    <span className='font-medium'>
                      {formatCurrency(data.equity.retainedEarnings)}
                    </span>
                  </div>
                  <Divider className='my-2' />
                  <div className='flex justify-between font-bold text-lg'>
                    <span>{t('totalEquity')}</span>
                    <span className='text-green-600'>
                      {formatCurrency(data.equity.total)}
                    </span>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>
        )}
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${isMobile ? 'sm:space-y-4' : 'sm:space-y-6'}`}>
      {/* Filters */}
      <Card
        className={`${isMobile ? 'rounded-lg' : 'rounded-lg'} border-0 shadow-sm`}
      >
        <div
          className={`flex ${isMobile ? 'flex-col' : 'flex-col lg:flex-row'} ${isMobile ? 'gap-3' : 'gap-4'} items-start lg:items-center justify-between`}
        >
          <div
            className={`flex ${isMobile ? 'flex-col' : 'flex-col sm:flex-row'} ${isMobile ? 'gap-2' : 'gap-4'} ${isMobile ? 'w-full' : ''}`}
          >
            <Select
              placeholder={t('selectStatementType')}
              allowClear
              value={statementType || undefined}
              onChange={setStatementType}
              style={{ width: isMobile ? '100%' : '192px' }}
              size={isMobile ? 'middle' : 'large'}
            >
              <Option value='income_statement'>
                {isMobile ? t('income') : t('incomeStatement')}
              </Option>
              <Option value='balance_sheet'>
                {isMobile ? t('balance') : t('balanceSheet')}
              </Option>
              <Option value='cash_flow'>
                {isMobile ? t('cashFlowOG') : t('cashFlow')}
              </Option>
              <Option value='trial_balance'>
                {isMobile ? t('trial') : t('trialBalance')}
              </Option>
            </Select>
            <Select
              placeholder={t('selectPeriod')}
              allowClear
              value={period || undefined}
              onChange={setPeriod}
              style={{ width: isMobile ? '100%' : '128px' }}
              size={isMobile ? 'middle' : 'large'}
            >
              <Option value='daily'>{t('daily')}</Option>
              <Option value='weekly'>{t('weekly')}</Option>
              <Option value='monthly'>{t('monthly')}</Option>
              <Option value='quarterly'>{t('quarterly')}</Option>
              <Option value='yearly'>{t('yearly')}</Option>
            </Select>
            <RangePicker
              onChange={(dates) => {
                setDateRange(dates as unknown as [string, string]);
              }}
              style={{ width: isMobile ? '100%' : '256px' }}
              size={isMobile ? 'middle' : 'large'}
            />
          </div>
          <Button
            type='primary'
            icon={<FileTextOutlined />}
            className={`bg-blue-600 hover:bg-blue-700 ${isMobile ? 'w-full' : ''}`}
            onClick={() => setShowGenerateModal(true)}
            size={isMobile ? 'middle' : 'large'}
          >
            {isMobile ? t('Generate') : t('generateStatement')}
          </Button>
        </div>
      </Card>

      {/* Statement Details View */}
      {selectedStatement && (
        <Card
          title={
            <div className='flex items-center justify-between'>
              <span className='flex items-center gap-2'>
                <FileTextOutlined className='text-blue-600' />
                {t('statementDetails')}
              </span>
              <Button
                onClick={() => setSelectedStatement(null)}
                className='text-gray-500'
              >
                {t('close')}
              </Button>
            </div>
          }
          className='rounded-lg border-0 shadow-sm'
        >
          {renderStatementDetails(selectedStatement)}
        </Card>
      )}

      {/* Statements Table */}
      <Card
        title={
          <span className={`flex items-center ${isMobile ? 'gap-2' : 'gap-2'}`}>
            <CalendarOutlined className='text-blue-600' />
            <span className={isMobile ? 'text-sm' : ''}>
              {t('financialStatements')}
            </span>
          </span>
        }
        className={`${isMobile ? 'rounded-lg' : 'rounded-lg'} border-0 shadow-sm`}
      >
        <GalaxyTable
          data={statements}
          columns={columns}
          rowKey='id'
          loading={isLoading}
          pagination={{
            current: 1,
            pageSize: isMobile ? 5 : 10,
            showSizeChanger: !isMobile,
            showQuickJumper: !isMobile,
            total: statements.length,
            showTotal: !isMobile
              ? (total, range) =>
                  `${range[0]}-${range[1]} ${t('of')} ${total} ${t('statements')}`
              : undefined,
            onChange: () => {},
          }}
          scroll={isMobile ? { x: 800 } : undefined}
          size={isMobile ? 'small' : 'large'}
        />
      </Card>

      {/* Generate Statement Modal */}
      <GenerateStatementModal
        open={showGenerateModal}
        onCancel={() => setShowGenerateModal(false)}
      />
    </div>
  );
};

export default FinancialStatements;
