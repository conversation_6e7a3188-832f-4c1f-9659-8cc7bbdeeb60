import {
  ReconciliationOutlined,
  CalendarOutlined,
  SettingOutlined,
  DollarOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Modal,
  Form,
  Select,
  DatePicker,
  Button,
  Row,
  Col,
  Typography,
  Card,
  Input,
  InputNumber,
  Checkbox,
} from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useGenerateReconciliationReport } from '@/hooks/useAccounting.ts';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;
const { TextArea } = Input;

interface GenerateReportModalProps {
  open: boolean;
  onCancel: () => void;
}

const GenerateReportModal: React.FC<GenerateReportModalProps> = ({
  open,
  onCancel,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const generateReport = useGenerateReconciliationReport();

  const handleSubmit = async (values: any) => {
    const { registerId, dateRange, shiftDetails, reconciliationOptions } =
      values;
    console.log(values);
    const period = {
      startDate: dateRange[0].toISOString(),
      endDate: dateRange[1].toISOString(),
    };

    try {
      await generateReport.mutateAsync({
        registerId,
        period,
        options: {
          shiftDetails,
          reconciliationOptions,
        },
      });
      form.resetFields();
      onCancel();
    } catch (error) {
      console.log(error);
      // Error is handled by the hook
    }
  };
  const handleItemClick = (fieldName: string[]) => {
    const currentValue = form.getFieldValue(fieldName);
    form.setFieldsValue({
      [fieldName[0]]: {
        ...form.getFieldValue(fieldName[0]),
        [fieldName[1]]: !currentValue,
      },
    });
  };
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const registerOptions = [
    {
      value: 'REG001',
      label: 'Main Counter',
      description: 'Primary cash register',
    },
    {
      value: 'REG002',
      label: 'VIP Lounge',
      description: 'High-value transactions',
    },
    {
      value: 'REG003',
      label: 'Gaming Floor',
      description: 'Gaming area transactions',
    },
  ];

  return (
    <Modal
      title={
        <div className='flex items-center gap-3'>
          <ReconciliationOutlined className='text-blue-600 text-xl' />
          <div>
            <Title level={4} className='mb-0'>
              {t('accounting.generateReconciliationReport')}
            </Title>
            <Text className='text-gray-500'>
              {t('accounting.configureReportParameters')}
            </Text>
          </div>
        </div>
      }
      open={open}
      onCancel={handleCancel}
      width={700}
      footer={[
        <Button key='cancel' onClick={handleCancel}>
          {t('common.cancel')}
        </Button>,
        <Button
          key='submit'
          type='primary'
          onClick={() => form.submit()}
          loading={generateReport.isPending}
          className='bg-blue-600 hover:bg-blue-700'
        >
          {t('accounting.generateReport')}
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout='vertical'
        onFinish={handleSubmit}
        initialValues={{
          reconciliationOptions: {
            includeDiscrepancies: true,
            autoReconcile: false,
          },
        }}
      >
        <Card className='mb-6 border border-blue-200 bg-blue-50/30'>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name='registerId'
                label={
                  <span className='flex items-center gap-2'>
                    <DollarOutlined className='text-blue-600' />
                    {t('accounting.cashRegister')}
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: t('accounting.registerRequired'),
                  },
                ]}
              >
                <Select
                  placeholder={t('accounting.selectRegister')}
                  size='large'
                >
                  {registerOptions.map((register) => (
                    <Option key={register.value} value={register.value}>
                      <div>
                        <div className='font-medium'>{register.label}</div>
                        <div className='text-sm text-gray-500'>
                          {register.description}
                        </div>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name='dateRange'
                label={
                  <span className='flex items-center gap-2'>
                    <CalendarOutlined className='text-blue-600' />
                    {t('accounting.reconciliationPeriod')}
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: t('accounting.dateRangeRequired'),
                  },
                ]}
              >
                <RangePicker
                  size='large'
                  className='w-full'
                  placeholder={[t('common.startDate'), t('common.endDate')]}
                  showTime
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        <Card className='mb-6 border border-green-200 bg-green-50/30'>
          <div className='flex items-center gap-2 mb-4'>
            <UserOutlined className='text-green-600' />
            <Title level={5} className='mb-0'>
              {t('accounting.shiftDetails')}
            </Title>
          </div>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name={['shiftDetails', 'openingBalance']}
                label={t('accounting.openingBalance')}
              >
                <InputNumber
                  size='large'
                  className='w-full'
                  placeholder='0.00'
                  formatter={(value) =>
                    `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name={['shiftDetails', 'expectedClosingBalance']}
                label={t('accounting.expectedClosingBalance')}
              >
                <InputNumber
                  size='large'
                  className='w-full'
                  placeholder='0.00'
                  formatter={(value) =>
                    `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name={['shiftDetails', 'shiftNotes']}
            label={t('accounting.shiftNotes')}
          >
            <TextArea
              rows={3}
              placeholder={t('accounting.shiftNotesPlaceholder')}
            />
          </Form.Item>
        </Card>

        <Card className='border border-purple-200 bg-purple-50/30'>
          <div className='flex items-center gap-2 mb-4'>
            <SettingOutlined className='text-purple-600' />
            <Title level={5} className='mb-0'>
              {t('accounting.reconciliationOptions')}
            </Title>
          </div>

          <div
            className='flex items-center gap-3 p-3 bg-white rounded-lg border my-6 cursor-pointer'
            onClick={() =>
              handleItemClick(['reconciliationOptions', 'includeDiscrepancies'])
            }
          >
            <Form.Item
              name={['reconciliationOptions', 'includeDiscrepancies']}
              valuePropName='checked'
              noStyle
            >
              <Checkbox className='w-4 h-4 text-blue-600 rounded' />
            </Form.Item>
            <div>
              <div className='font-medium'>
                {t('accounting.includeDiscrepancies')}
              </div>
              <div className='text-sm text-gray-500'>
                {t('accounting.includeDiscrepanciesDescription')}
              </div>
            </div>
          </div>

          {/* autoReconcile */}
          <div
            className='flex items-center gap-3 p-3 bg-white rounded-lg my-6 border cursor-pointer'
            onClick={() =>
              handleItemClick(['reconciliationOptions', 'autoReconcile'])
            }
          >
            <Form.Item
              name={['reconciliationOptions', 'autoReconcile']}
              valuePropName='checked'
              noStyle
            >
              <Checkbox className='w-4 h-4 text-blue-600 rounded' />
            </Form.Item>
            <div>
              <div className='font-medium'>{t('accounting.autoReconcile')}</div>
              <div className='text-sm text-gray-500'>
                {t('accounting.autoReconcileDescription')}
              </div>
            </div>
          </div>

          <Form.Item
            name={['reconciliationOptions', 'varianceTolerance']}
            label={t('accounting.varianceTolerance')}
          >
            <Select
              placeholder={t('accounting.selectVarianceTolerance')}
              size='large'
            >
              <Option value={0}>{t('accounting.noTolerance')}</Option>
              <Option value={5}>$5</Option>
              <Option value={10}>$10</Option>
              <Option value={25}>$25</Option>
              <Option value={50}>$50</Option>
              <Option value={100}>$100</Option>
            </Select>
          </Form.Item>
        </Card>
      </Form>
    </Modal>
  );
};

export default GenerateReportModal;
