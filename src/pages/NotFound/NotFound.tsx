import { HomeOutlined } from '@ant-design/icons';
import { Result, Button } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/constants/routes.ts';

const NotFound: React.FC = () => {
  const { t } = useTranslation('notFound');
  const navigate = useNavigate();

  const handleBackHome = () => {
    navigate(ROUTES.DASHBOARD);
  };

  return (
    <div className='flex items-center justify-center min-h-[60vh]'>
      <Result
        status='404'
        title='404'
        subTitle={t('subtitle')}
        extra={
          <Button
            type='primary'
            icon={<HomeOutlined />}
            onClick={handleBackHome}
            size='large'
            className='bg-blue-600 hover:bg-blue-700'
          >
            {t('backToDashboard')}
          </Button>
        }
      />
    </div>
  );
};

export default NotFound;
