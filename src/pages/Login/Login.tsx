import { MoonOutlined, SunOutlined } from '@ant-design/icons';
import { Button, Card, Form, Input, Switch, Typography } from 'antd';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useLogin } from './apis/useLogin';
import { mockAdminAccount } from '@/api/mockData';
import { Logo } from '@/components';
import DropdownLocale from '@/components/DropdownYF/DropdownLocale';
import { ROUTES } from '@/constants/routes';
import { useAuthStore, useUserStore } from '@/stores';
import type { LoginProps } from '@/types';

const { Title } = Typography;

export default function Login() {
  const { isDark, setIsDark } = useUserStore();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { t } = useTranslation('login');
  const { isAuthenticated } = useAuthStore();
  const { mutate: login, isPending: loggingIn } = useLogin({});

  const isDev = import.meta.env.DEV;

  const onFinish = (values: LoginProps) => {
    login({ ...values });
  };

  useEffect(() => {
    if (isAuthenticated) {
      navigate(ROUTES.DASHBOARD, { replace: true });
    }
  }, [isAuthenticated, navigate]);

  return (
    <div
      className={`min-h-screen flex flex-col items-center pt-16 sm:pt-32 px-4 gap-y-8 ${isDark ? 'bg-[#2a405e]' : 'bg-gray-400'}`}
    >
      <Card className='w-96 max-w-full !border-none'>
        <div className='mx-auto w-fit'>
          <Logo size='lg' />
        </div>

        <Title level={4} className='!my-4 text-center'>
          {t('title')}
        </Title>

        <Form
          form={form}
          name='loginForm'
          layout='vertical'
          onFinish={onFinish}
          autoComplete='off'
          initialValues={{
            username: isDev ? mockAdminAccount.username : '',
            password: isDev ? mockAdminAccount.password : '',
          }}
        >
          <Form.Item
            label={t('username')}
            name='username'
            rules={[{ required: true, message: t('login.usernameError') }]}
          >
            <Input
              placeholder={t('usernamePlaceholder')}
              autoFocus
              size='large'
            />
          </Form.Item>

          <Form.Item
            label={t('password')}
            name='password'
            rules={[{ required: true, message: t('passwordError') }]}
          >
            <Input.Password
              placeholder={t('passwordPlaceholder')}
              size='large'
            />
          </Form.Item>

          {isDev && (
            <div className='flex gap-x-sm mb-4'>
              <Button
                size='small'
                onClick={() => {
                  form.setFieldsValue({
                    username: mockAdminAccount.username,
                    password: mockAdminAccount.password,
                  });
                }}
              >
                admin
              </Button>
            </div>
          )}

          <Form.Item>
            <Button
              type='primary'
              htmlType='submit'
              size='large'
              block
              loading={loggingIn}
            >
              {t('logIn')}
            </Button>
          </Form.Item>
        </Form>
        <div className='grid grid-cols-3 full-width'>
          <div className='col-span-1 '>
            <Switch
              className='relative bottom-[2px]'
              checked={!isDark}
              onChange={() => setIsDark(!isDark)}
              checkedChildren={<SunOutlined />}
              unCheckedChildren={<MoonOutlined />}
            />
          </div>
          <div className='col-span-2 flex justify-end'>
            <DropdownLocale />
          </div>
        </div>
      </Card>
    </div>
  );
}
