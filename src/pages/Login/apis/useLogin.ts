import { useNavigate } from 'react-router-dom';
import { apiClient } from '@/api/apiClient';
import {
  useMasterMutation,
  type UseMasterMutationProps,
} from '@/hooks/useApiMaster';
import { useAuthStore } from '@/stores';
import type { YFRole } from '@/types';

type LoginRes = {
  userName: string;
  token: string;
  expiresAt: string;
  userId: string;
  roles: Array<YFRole>;
};
type LoginProps = {
  username: string;
  password: string;
};

const useLogin = (useProps: UseMasterMutationProps<LoginRes, LoginProps>) => {
  const { ...config } = useProps;

  const navigate = useNavigate();
  const { setLoginRes } = useAuthStore();

  const mutation = useMasterMutation<LoginRes, LoginProps>({
    ...config,
    mutationFn: (props) => {
      const request = apiClient
        .post('/admin/auth/login', props)
        .then(({ data }) => data);
      return request;
    },
    onError: (error) => {
      setLoginRes(null);
      config.onError?.(error);
    },
    onSuccess: (res) => {
      setLoginRes(res);
      setTimeout(() => {
        navigate('/');
      }, 200);
    },
  });
  return mutation;
};

export { useLogin };
export type { LoginRes, LoginProps };
