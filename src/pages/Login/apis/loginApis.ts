import { mockAdminAccount, mockDelay, mockLoginRes } from '@/api/mockData';
import type { ApiResponse, LoginProps, LoginRes } from '@/types';

export const login = async (
  loginProps: LoginProps,
): Promise<ApiResponse<LoginRes | null>> => {
  // Simulate API delay
  await mockDelay();

  if (
    loginProps.username === mockAdminAccount.username &&
    loginProps.password === mockAdminAccount.password
  )
    return {
      data: mockLoginRes,
      success: true,
      message: 'Login successfully',
    };

  return {
    data: null,
    success: false,
    message: 'Invalid Username or Password',
  };
};
