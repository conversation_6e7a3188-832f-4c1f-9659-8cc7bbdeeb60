import { SaveOutlined, LockOutlined } from '@ant-design/icons';
import { Card, Form, Input, Button, Row, Col, Space, Alert } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useChangePassword } from '../apis';
import { useLogout } from '@/hooks';
import { useAuthStore, useNotifyStore } from '@/stores';

const { Password } = Input;

interface SecurityData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
  // twoFactorAuth: boolean;
  // sessionTimeout: number;
  // passwordExpiry: number;
  // loginNotifications: boolean;
  // ipWhitelist: boolean;
  // allowedIPs: string[];
}

// interface LoginSession {
//   id: string;
//   device: string;
//   location: string;
//   ipAddress: string;
//   loginTime: string;
//   lastActivity: string;
//   status: 'active' | 'expired';
//   deviceType: 'desktop' | 'mobile' | 'tablet';
// }

const SecuritySettings: React.FC = () => {
  // const [showSessions, setShowSessions] = useState(false);
  //
  const { t } = useTranslation('securitySettings');
  const [form] = Form.useForm();
  const { loginRes } = useAuthStore();
  const { pushBSQ } = useNotifyStore();
  const logout = useLogout();
  const { mutate: changePassword, isPending: changingPassword } =
    useChangePassword({
      onSuccess: () => {
        pushBSQ([
          {
            title: import.meta.env.VITE_APP_TITLE || 'YF Pay Admin',
            des: t('changePasswordSuccessDesc'),
          },
        ]);

        // Clear password fields
        form.setFieldsValue({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });
      },
    });

  // // Mock data
  // const initialData: SecurityData = {
  //   currentPassword: '',
  //   newPassword: '',
  //   confirmPassword: '',
  //   twoFactorAuth: true,
  //   sessionTimeout: 30,
  //   passwordExpiry: 90,
  //   loginNotifications: true,
  //   ipWhitelist: false,
  //   allowedIPs: ['*************', '*********'],
  // };
  //
  // const mockSessions: LoginSession[] = [
  //   {
  //     id: '1',
  //     device: 'Chrome on Windows',
  //     location: 'Las Vegas, NV',
  //     ipAddress: '*************',
  //     loginTime: '2024-02-24T09:00:00Z',
  //     lastActivity: '2024-02-24T14:30:00Z',
  //     status: 'active',
  //     deviceType: 'desktop',
  //   },
  //   {
  //     id: '2',
  //     device: 'Safari on iPhone',
  //     location: 'Las Vegas, NV',
  //     ipAddress: '*************',
  //     loginTime: '2024-02-23T15:20:00Z',
  //     lastActivity: '2024-02-23T18:45:00Z',
  //     status: 'expired',
  //     deviceType: 'mobile',
  //   },
  //   {
  //     id: '3',
  //     device: 'Firefox on MacOS',
  //     location: 'Henderson, NV',
  //     ipAddress: '*********',
  //     loginTime: '2024-02-22T11:15:00Z',
  //     lastActivity: '2024-02-22T16:20:00Z',
  //     status: 'expired',
  //     deviceType: 'desktop',
  //   },
  // ];

  const handleSave = async (values: SecurityData) => {
    if (!loginRes?.userId) {
      logout();
      return;
    }

    changePassword({
      userId: loginRes?.userId,
      password: values.currentPassword,
      newPassword: values.newPassword,
    });
  };
  // const handleTerminateSession = () => {
  //   Modal.confirm({
  //     title: t('settings.security.terminateSession'),
  //     content: t('settings.security.terminateSessionConfirm'),
  //     icon: <ExclamationCircleOutlined />,
  //     onOk() {
  //       message.success(t('settings.security.sessionTerminated'));
  //     },
  //   });
  // };
  //
  // const getDeviceIcon = (deviceType: string) => {
  //   switch (deviceType) {
  //     case 'mobile':
  //       return <MobileOutlined />;
  //     case 'tablet':
  //       return <TabletOutlined />;
  //     default:
  //       return <DesktopOutlined />;
  //   }
  // };
  //
  // const sessionColumns = [
  //   {
  //     title: t('settings.security.device'),
  //     dataIndex: 'device',
  //     key: 'device',
  //     render: (device: string, record: LoginSession) => (
  //       <div className='flex items-center gap-2'>
  //         {getDeviceIcon(record.deviceType)}
  //         <div>
  //           <div className='font-medium'>{device}</div>
  //           <div className='text-sm text-gray-500'>{record.location}</div>
  //         </div>
  //       </div>
  //     ),
  //   },
  //   {
  //     title: t('settings.security.ipAddress'),
  //     dataIndex: 'ipAddress',
  //     key: 'ipAddress',
  //     render: (ip: string) => (
  //       <span className='font-mono text-sm bg-gray-100 px-2 py-1 rounded'>
  //         {ip}
  //       </span>
  //     ),
  //   },
  //   {
  //     title: t('settings.security.lastActivity'),
  //     dataIndex: 'lastActivity',
  //     key: 'lastActivity',
  //     render: (time: string) => dayjs(time).format('MMM DD, YYYY HH:mm'),
  //   },
  //   {
  //     title: t('settings.security.status'),
  //     dataIndex: 'status',
  //     key: 'status',
  //     render: (status: string) => (
  //       <Tag color={status === 'active' ? 'green' : 'red'}>
  //         {t(`settings.security.sessionStatus.${status}`)}
  //       </Tag>
  //     ),
  //   },
  //   {
  //     title: t('common.actions'),
  //     key: 'actions',
  //     render: (_: unknown, record: LoginSession) => (
  //       <Space>
  //         <Button
  //           type='text'
  //           icon={<EyeOutlined />}
  //           size='small'
  //           onClick={() => message.info('View session details')}
  //         />
  //         {record.status === 'active' && (
  //           <Button
  //             type='text'
  //             icon={<DeleteOutlined />}
  //             size='small'
  //             danger
  //             onClick={handleTerminateSession}
  //           />
  //         )}
  //       </Space>
  //     ),
  //   },
  // ];

  return (
    <div className='space-y-6'>
      <Form
        form={form}
        layout='vertical'
        // initialValues={initialData}
        onFinish={handleSave}
        className='space-y-6'
      >
        {/* Password Change */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <LockOutlined className='text-blue-600' />
              {t('changePassword')}
            </span>
          }
          className='rounded-lg'
        >
          <Alert
            message={t('passwordRequirements')}
            description={t('passwordRequirementsDesc')}
            type='info'
            showIcon
            className='!mb-4'
          />

          <Row gutter={[24, 16]}>
            <Col xs={24}>
              <Form.Item
                name='currentPassword'
                label={t('currentPasswordLabel')}
                rules={[
                  {
                    required: true,
                    message: t('currentPasswordError'),
                  },
                ]}
              >
                <Password
                  size='large'
                  placeholder={t('currentPasswordPlaceholder')}
                />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name='newPassword'
                label={t('newPasswordLabel')}
                rules={[
                  { required: true, message: t('newPasswordError') },
                  {
                    validator: (_, value) => {
                      const isValid = /^(?=.*[a-z])(?=.*[A-Z]).{8,}$/.test(
                        value,
                      );
                      if (!isValid) {
                        return Promise.reject(t('newPasswordLengthError'));
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Password
                  size='large'
                  placeholder={t('newPasswordPlaceholder')}
                />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name='confirmPassword'
                label={t('confirmPasswordLabel')}
                dependencies={['newPassword']}
                rules={[
                  {
                    required: true,
                    message: t('confirmPasswordError'),
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error(t('passwordMismatch')));
                    },
                  }),
                ]}
              >
                <Password
                  size='large'
                  placeholder={t('confirmPasswordPlaceholder')}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Two-Factor Authentication */}
        {/* <Card */}
        {/*   title={ */}
        {/*     <span className='flex items-center gap-2'> */}
        {/*       <SafetyOutlined className='text-blue-600' /> */}
        {/*       {t('twoFactorAuth')} */}
        {/*     </span> */}
        {/*   } */}
        {/*   className='rounded-lg' */}
        {/* > */}
        {/*   <div className='space-y-4'> */}
        {/*     <div className='flex items-center justify-between'> */}
        {/*       <div> */}
        {/*         <Text className='font-medium'>{t('enable2FA')}</Text> */}
        {/*         <div className='text-sm text-gray-500'> */}
        {/*           {t('enable2FADesc')} */}
        {/*         </div> */}
        {/*       </div> */}
        {/*       <Form.Item */}
        {/*         name='twoFactorAuth' */}
        {/*         valuePropName='checked' */}
        {/*         className='mb-0' */}
        {/*       > */}
        {/*         <Switch /> */}
        {/*       </Form.Item> */}
        {/*     </div> */}
        {/**/}
        {/*     <Divider /> */}
        {/**/}
        {/*     <div className='bg-gray-50 p-4 rounded-lg'> */}
        {/*       <Text className='font-medium block mb-2'> */}
        {/*         {t('authenticatorApp')} */}
        {/*       </Text> */}
        {/*       <Text className='text-sm text-gray-600 block mb-3'> */}
        {/*         {t('authenticatorAppDesc')} */}
        {/*       </Text> */}
        {/*       <Button icon={<KeyOutlined />}>{t('setupAuthenticator')}</Button> */}
        {/*     </div> */}
        {/*   </div> */}
        {/* </Card> */}
        {/**/}
        {/* Session Management */}
        {/* <Card */}
        {/*   title={ */}
        {/*     <span className='flex items-center gap-2'> */}
        {/*       <SecurityScanOutlined className='text-blue-600' /> */}
        {/*       {t('sessionManagement')} */}
        {/*     </span> */}
        {/*   } */}
        {/*   className='rounded-lg' */}
        {/* > */}
        {/*   <Row gutter={[24, 16]}> */}
        {/*     <Col xs={24} lg={12}> */}
        {/*       <Form.Item */}
        {/*         name='sessionTimeout' */}
        {/*         label={t('sessionTimeout')} */}
        {/*         rules={[ */}
        {/*           { */}
        {/*             required: true, */}
        {/*             message: t('sessionTimeoutError'), */}
        {/*           }, */}
        {/*         ]} */}
        {/*       > */}
        {/*         <Select size='large'> */}
        {/*           <Option value={15}>15 {t('minutes')}</Option> */}
        {/*           <Option value={30}>30 {t('minutes')}</Option> */}
        {/*           <Option value={60}>1 {t('hour')}</Option> */}
        {/*           <Option value={120}>2 {t('hours')}</Option> */}
        {/*           <Option value={480}>8 {t('hours')}</Option> */}
        {/*         </Select> */}
        {/*       </Form.Item> */}
        {/*     </Col> */}
        {/*     <Col xs={24} lg={12}> */}
        {/*       <Form.Item */}
        {/*         name='passwordExpiry' */}
        {/*         label={t('passwordExpiryLabel')} */}
        {/*         rules={[ */}
        {/*           { */}
        {/*             required: true, */}
        {/*             message: t('passwordExpiryError'), */}
        {/*           }, */}
        {/*         ]} */}
        {/*       > */}
        {/*         <Select size='large'> */}
        {/*           <Option value={30}>30 {t('days')}</Option> */}
        {/*           <Option value={60}>60 {t('days')}</Option> */}
        {/*           <Option value={90}>90 {t('days')}</Option> */}
        {/*           <Option value={180}>180 {t('days')}</Option> */}
        {/*           <Option value={365}>1 {t('year')}</Option> */}
        {/*         </Select> */}
        {/*       </Form.Item> */}
        {/*     </Col> */}
        {/*   </Row> */}
        {/**/}
        {/*   <Divider /> */}
        {/**/}
        {/*   <div className='space-y-4'> */}
        {/*     <div className='flex items-center justify-between'> */}
        {/*       <div> */}
        {/*         <Text className='font-medium'>{t('loginNotifications')}</Text> */}
        {/*         <div className='text-sm text-gray-500'> */}
        {/*           {t('loginNotificationsDesc')} */}
        {/*         </div> */}
        {/*       </div> */}
        {/*       <Form.Item */}
        {/*         name='loginNotifications' */}
        {/*         valuePropName='checked' */}
        {/*         className='mb-0' */}
        {/*       > */}
        {/*         <Switch /> */}
        {/*       </Form.Item> */}
        {/*     </div> */}
        {/**/}
        {/*     <div className='flex items-center justify-between'> */}
        {/*       <div> */}
        {/*         <Text className='font-medium'>{t('activeSessions')}</Text> */}
        {/*         <div className='text-sm text-gray-500'> */}
        {/*           {t('activeSessionsDesc')} */}
        {/*         </div> */}
        {/*       </div> */}
        {/*       <Button onClick={() => setShowSessions(true)}> */}
        {/*         {t('manageSessions')} */}
        {/*       </Button> */}
        {/*     </div> */}
        {/*   </div> */}
        {/* </Card> */}
        {/**/}
        {/* IP Whitelist */}
        {/* <Card */}
        {/*   title={ */}
        {/*     <span className='flex items-center gap-2'> */}
        {/*       <SecurityScanOutlined className='text-blue-600' /> */}
        {/*       {t('ipWhitelist')} */}
        {/*     </span> */}
        {/*   } */}
        {/*   className='rounded-lg' */}
        {/* > */}
        {/*   <div className='space-y-4'> */}
        {/*     <div className='flex items-center justify-between'> */}
        {/*       <div> */}
        {/*         <Text className='font-medium'>{t('enableIPWhitelist')}</Text> */}
        {/*         <div className='text-sm text-gray-500'> */}
        {/*           {t('enableIPWhitelistDesc')} */}
        {/*         </div> */}
        {/*       </div> */}
        {/*       <Form.Item */}
        {/*         name='ipWhitelist' */}
        {/*         valuePropName='checked' */}
        {/*         className='mb-0' */}
        {/*       > */}
        {/*         <Switch /> */}
        {/*       </Form.Item> */}
        {/*     </div> */}
        {/**/}
        {/*     <Form.Item */}
        {/*       name='allowedIPs' */}
        {/*       label={t('allowedIPsLabel')} */}
        {/*       dependencies={['ipWhitelist']} */}
        {/*     > */}
        {/*       <Select */}
        {/*         mode='tags' */}
        {/*         size='large' */}
        {/*         placeholder={t('allowedIPsPlaceholder')} */}
        {/*         disabled={!form.getFieldValue('ipWhitelist')} */}
        {/*         className='w-full' */}
        {/*       /> */}
        {/*     </Form.Item> */}
        {/*   </div> */}
        {/* </Card> */}

        {/* Action Buttons */}
        <Card className='rounded-lg'>
          <div className='flex justify-end'>
            <Space>
              <Button size='large'>{t('cancel')}</Button>
              <Button
                type='primary'
                icon={<SaveOutlined />}
                htmlType='submit'
                loading={changingPassword}
                size='large'
                className='bg-blue-600 hover:bg-blue-700'
              >
                {t('saveChanges')}
              </Button>
            </Space>
          </div>
        </Card>
      </Form>

      {/* Sessions Modal */}
      {/* <Modal */}
      {/*   title={t('settings.security.activeSessions')} */}
      {/*   open={showSessions} */}
      {/*   onCancel={() => setShowSessions(false)} */}
      {/*   footer={null} */}
      {/*   width={800} */}
      {/* > */}
      {/*   <Table */}
      {/*     dataSource={mockSessions} */}
      {/*     columns={sessionColumns} */}
      {/*     rowKey='id' */}
      {/*     pagination={false} */}
      {/*     size='small' */}
      {/*   /> */}
      {/* </Modal> */}
    </div>
  );
};

export default SecuritySettings;
