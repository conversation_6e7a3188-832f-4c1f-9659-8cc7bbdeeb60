import {
  SaveOutlined,
  UserOutlined,
  UploadOutlined,
  CameraOutlined,
  MailOutlined,
  PhoneOutlined,
  HomeOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import {
  Card,
  Form,
  Input,
  Button,
  Row,
  Col,
  Typography,
  Avatar,
  Upload,
  Space,
  message,
  Select,
  DatePicker,
} from 'antd';
import type { UploadChangeParam } from 'antd/es/upload';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface ProfileData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  department: string;
  dateOfBirth: dayjs.Dayjs;
  address: string;
  bio: string;
  avatar: string;
}

const ProfileSettings: React.FC = () => {
  const { t } = useTranslation('profileSettings');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState<string>('');

  // Mock initial data - in real app this would come from API
  const initialData: ProfileData = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '******-0123',
    position: 'System Administrator',
    department: 'IT Department',
    dateOfBirth: dayjs('1990-01-15'),
    address: '123 Admin Street, Las Vegas, NV 89101',
    bio: 'Experienced system administrator with 5+ years in casino management systems.',
    avatar: '',
  };
  const handleSave = async () => {
    setLoading(true);
    try {
      // Mock API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      message.success(t('settings.profile.saveSuccess'));
    } catch {
      message.error(t('settings.profile.saveError'));
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarChange = (info: UploadChangeParam) => {
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      // Get this url from response in real world
      setAvatarUrl(info.file.response?.url || '');
      message.success(t('settings.profile.avatarUploadSuccess'));
    }
  };

  const beforeUpload = (file: File) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error(t('settings.profile.avatarFormatError'));
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error(t('settings.profile.avatarSizeError'));
    }
    return isJpgOrPng && isLt2M;
  };

  return (
    <div className='space-y-6'>
      <Form
        form={form}
        layout='vertical'
        initialValues={initialData}
        onFinish={handleSave}
        className='space-y-6'
      >
        {/* Profile Picture */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <CameraOutlined className='text-blue-600' />
              {t('profilePicture')}
            </span>
          }
          className='rounded-lg'
        >
          <div className='flex flex-col items-center gap-6 sm:flex-row'>
            <Avatar
              size={120}
              src={avatarUrl}
              icon={<UserOutlined />}
              className='bg-gradient-to-br from-blue-500 to-purple-600'
            />
            <div className='flex-1'>
              <Title level={4} className='mb-2'>
                {t('changeAvatar')}
              </Title>
              <Text className='mb-4 block text-gray-500'>
                {t('avatarDescription')}
              </Text>
              <Upload
                name='avatar'
                listType='picture'
                className='avatar-uploader'
                showUploadList={false}
                action='/api/upload'
                beforeUpload={beforeUpload}
                onChange={handleAvatarChange}
              >
                <Button icon={<UploadOutlined />} size='large'>
                  {t('uploadAvatar')}
                </Button>
              </Upload>
            </div>
          </div>
        </Card>

        {/* Personal Information */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <UserOutlined className='text-blue-600' />
              {t('personalInfo')}
            </span>
          }
          className='rounded-lg'
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} lg={12}>
              <Form.Item
                name='firstName'
                label={t('firstNameLabel')}
                rules={[
                  {
                    required: true,
                    message: t('firstNameError'),
                  },
                ]}
              >
                <Input size='large' prefix={<UserOutlined />} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name='lastName'
                label={t('lastNameLabel')}
                rules={[
                  {
                    required: true,
                    message: t('lastNameError'),
                  },
                ]}
              >
                <Input size='large' prefix={<UserOutlined />} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name='email'
                label={t('emailLabel')}
                rules={[
                  {
                    required: true,
                    message: t('emailError'),
                  },
                  {
                    type: 'email',
                    message: t('emailInvalidError'),
                  },
                ]}
              >
                <Input size='large' prefix={<MailOutlined />} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name='phone'
                label={t('phoneLabel')}
                rules={[
                  {
                    required: true,
                    message: t('phoneError'),
                  },
                ]}
              >
                <Input size='large' prefix={<PhoneOutlined />} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item name='dateOfBirth' label={t('dateOfBirth')}>
                <DatePicker
                  size='large'
                  className='w-full'
                  format='YYYY-MM-DD'
                  defaultValue={dayjs('1990-01-15', 'YYYY-MM-DD')}
                />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name='position'
                label={t('positionLabel')}
                rules={[
                  {
                    required: true,
                    message: t('positionError'),
                  },
                ]}
              >
                <Select size='large' placeholder={t('positionPlaceholder')}>
                  <Option value='System Administrator'>
                    {t('systemAdmin')}
                  </Option>
                  <Option value='Manager'>{t('manager')}</Option>
                  <Option value='Supervisor'>{t('supervisor')}</Option>
                  <Option value='Analyst'>{t('analyst')}</Option>
                  <Option value='Operator'>{t('operator')}</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item name='address' label={t('addressLabel')}>
                <TextArea
                  rows={3}
                  size='large'
                  placeholder={t('addressPlaceholder')}
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item name='bio' label={t('bioLabel')}>
                <TextArea
                  rows={4}
                  size='large'
                  placeholder={t('bioPlaceholder')}
                  maxLength={500}
                  showCount
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Work Information */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <HomeOutlined className='text-blue-600' />
              {t('workInfo')}
            </span>
          }
          className='rounded-lg'
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} lg={12}>
              <Form.Item
                name='department'
                label={t('departmentLabel')}
                rules={[
                  {
                    required: true,
                    message: t('departmentError'),
                  },
                ]}
              >
                <Select size='large' placeholder={t('departmentPlaceholder')}>
                  <Option value='IT Department'>{t('IT Department')}</Option>
                  <Option value='Management'>{t('management')}</Option>
                  <Option value='Gaming Operations'>
                    {t('gamingOperation')}
                  </Option>
                  <Option value='Security'>{t('security')}</Option>
                  <Option value='Customer Service'>{t('customService')}</Option>
                  <Option value='Finance'>{t('finance')}</Option>
                  <Option value='Human Resources'>{t('humanResources')}</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item label={t('employeeId')}>
                <Input
                  size='large'
                  value='EMP001'
                  disabled
                  className='bg-gray-50'
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Account Statistics */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <CalendarOutlined className='text-blue-600' />
              {t('accountStats')}
            </span>
          }
          className='rounded-lg'
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={8}>
              <div className='rounded-lg bg-blue-50 p-4 text-center'>
                <div className='text-2xl font-bold text-blue-600'>2</div>
                <div className='text-sm text-blue-700'>{t('yearsActive')}</div>
              </div>
            </Col>
            <Col xs={24} sm={8}>
              <div className='rounded-lg bg-green-50 p-4 text-center'>
                <div className='text-2xl font-bold text-green-600'>1,247</div>
                <div className='text-sm text-green-700'>
                  {t('loginSessions')}
                </div>
              </div>
            </Col>
            <Col xs={24} sm={8}>
              <div className='rounded-lg bg-purple-50 p-4 text-center'>
                <div className='text-2xl font-bold text-purple-600'>98.5%</div>
                <div className='text-sm text-purple-700'>{t('uptime')}</div>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Action Buttons */}
        <Card className='rounded-lg'>
          <div className='flex justify-end'>
            <Space>
              <Button size='large'>{t('cancel')}</Button>
              <Button
                type='primary'
                icon={<SaveOutlined />}
                htmlType='submit'
                loading={loading}
                size='large'
                className='bg-blue-600 hover:bg-blue-700'
              >
                {t('saveChanges')}
              </Button>
            </Space>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default ProfileSettings;
