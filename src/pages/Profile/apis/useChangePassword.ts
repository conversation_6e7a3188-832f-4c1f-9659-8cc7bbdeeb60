import { apiClient } from '@/api/apiClient';
import {
  useMasterMutation,
  type UseMasterMutationProps,
} from '@/hooks/useApiMaster';

type ChangePasswordRes = unknown;
type ChangePasswordProps = {
  userId: string;
  password: string;
  newPassword: string;
};

const useChangePassword = (
  useProps: UseMasterMutationProps<ChangePasswordRes, ChangePasswordProps>,
) => {
  const { ...config } = useProps;

  const mutation = useMasterMutation<ChangePasswordRes, ChangePasswordProps>({
    ...config,
    mutationFn: (props) => {
      const request = apiClient
        .put('/admin/me/password', props)
        .then(({ data }) => data);
      return request;
    },
    onError: (error) => {
      config.onError?.(error);
    },
    onSuccess: (res) => {
      config.onSuccess?.(res);
    },
  });
  return mutation;
};

export { useChangePassword };
