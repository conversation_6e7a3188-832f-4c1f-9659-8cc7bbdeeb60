import { UserOutlined, SecurityScanOutlined } from '@ant-design/icons';
import { Tabs } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import ProfileSettings from './components/ProfileSettings';
import SecuritySettings from './components/SecuritySettings';

const Settings: React.FC = () => {
  const { t } = useTranslation('profile');
  const [activeTab, setActiveTab] = useState('profile');

  const tabItems = [
    {
      key: 'profile',
      label: (
        <span className='flex items-center gap-2'>
          <UserOutlined />
          {t('profile')}
        </span>
      ),
      children: <ProfileSettings />,
    },
    {
      key: 'security',
      label: (
        <span className='flex items-center gap-2'>
          <SecurityScanOutlined />
          {t('security')}
        </span>
      ),
      children: <SecuritySettings />,
    },
  ];

  return (
    <Tabs
      activeKey={activeTab}
      onChange={setActiveTab}
      items={tabItems}
      size='large'
      className='settings-tabs'
      tabPosition='top'
    />
  );
};

export default Settings;
