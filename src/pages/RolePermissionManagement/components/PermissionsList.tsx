import {
  UserOutlined,
  DollarOutlined,
  TrophyOutlined,
  SafetyOutlined,
  Bar<PERSON><PERSON>Outlined,
  SettingOutlined,
  KeyOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  ControlOutlined,
} from '@ant-design/icons';
import {
  Card,
  Collapse,
  Tag,
  Typography,
  Space,
  Input,
  Select,
  Row,
  Col,
  Badge,
  Tooltip,
} from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useResponsive } from '@/hooks/useResponsive.ts';
import { useUserStore } from '@/stores';
import type { Permission, PermissionCategory, PermissionAction } from '@/types';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;
const { Panel } = Collapse;

interface PermissionsListProps {
  permissions: Permission[];
  categories: PermissionCategory[];
  loading: boolean;
}

const PermissionsList: React.FC<PermissionsListProps> = (props) => {
  const { permissions, categories } = props || {};
  const { isDark } = useUserStore();
  const { t } = useTranslation('permissionsList');
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const { isMobile } = useResponsive();
  const getCategoryIcon = (iconName?: string) => {
    switch (iconName) {
      case 'UserOutlined':
        return <UserOutlined className='!text-blue-600' />;
      case 'DollarOutlined':
        return <DollarOutlined className='!text-green-600' />;
      case 'TrophyOutlined':
        return <TrophyOutlined className='!text-yellow-600' />;
      case 'SafetyOutlined':
        return <SafetyOutlined className='!text-red-600' />;
      case 'BarChartOutlined':
        return <BarChartOutlined className='!text-purple-600' />;
      case 'SettingOutlined':
        return <SettingOutlined className='!text-gray-600' />;
      default:
        return <KeyOutlined className='!text-blue-600' />;
    }
  };

  const getActionIcon = (type: PermissionAction['type']) => {
    switch (type) {
      case 'view':
        return <EyeOutlined className='!text-blue-500' />;
      case 'create':
        return <PlusOutlined className='!text-green-500' />;
      case 'edit':
        return <EditOutlined className='!text-orange-500' />;
      case 'delete':
        return <DeleteOutlined className='!text-red-500' />;
      case 'manage':
        return <ControlOutlined className='!text-purple-500' />;
      default:
        return <KeyOutlined className='!text-gray-500' />;
    }
  };

  const getActionColor = (type: PermissionAction['type']) => {
    switch (type) {
      case 'view':
        return 'blue';
      case 'create':
        return 'green';
      case 'edit':
        return 'orange';
      case 'delete':
        return 'red';
      case 'manage':
        return 'purple';
      default:
        return 'default';
    }
  };

  // Filter permissions
  const filteredPermissions = permissions.filter((permission) => {
    const matchesSearch =
      !searchTerm ||
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory =
      !categoryFilter || permission.category.id === categoryFilter;

    return matchesSearch && matchesCategory;
  });

  // Group permissions by category
  const permissionsByCategory = categories
    .map((category) => ({
      category,
      permissions: filteredPermissions.filter(
        (p) => p.category.id === category.id,
      ),
    }))
    .filter((group) => group.permissions.length > 0);
  console.log(permissionsByCategory);

  return (
    <div className='space-y-6'>
      {/* Filters */}
      <div className='flex flex-col sm:flex-row gap-4'>
        <Search
          placeholder={t('searchPermissions')}
          allowClear
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className='sm:w-80'
          size={isMobile ? 'middle' : 'large'}
        />

        <Select
          placeholder={t('filterByCategory')}
          allowClear
          value={categoryFilter || undefined}
          onChange={setCategoryFilter}
          className={isMobile ? 'w-full' : 'w-40'}
          size={isMobile ? 'middle' : 'large'}
        >
          {categories.map((category) => (
            <Option key={category.id} value={category.id}>
              <Space>
                {getCategoryIcon(category.icon)}
                {category.name}
              </Space>
            </Option>
          ))}
        </Select>
      </div>

      {/* Statistics */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8}>
          <Card
            className={`text-center ${
              isDark
                ? '!border !border-blue-700 !bg-blue-900'
                : '!border !border-blue-300 !bg-blue-100'
            }`}
          >
            <div
              className={`text-2xl font-bold ${
                isDark ? '!text-white' : '!text-blue-600'
              }`}
            >
              {permissions.length}
            </div>
            <div
              className={`text-sm ${isDark ? '!text-blue-200' : '!text-blue-700'}`}
            >
              {t('totalPermissions')}
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={8}>
          <Card
            className={`text-center ${
              isDark
                ? '!border !border-green-700 !bg-green-900'
                : '!border !border-green-300 !bg-green-100'
            }`}
          >
            <div
              className={`text-2xl font-bold ${
                isDark ? '!text-white' : '!text-green-600'
              }`}
            >
              {categories.length}
            </div>
            <div
              className={`text-sm ${isDark ? '!text-green-200' : '!text-green-700'}`}
            >
              {t('categories')}
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={8}>
          <Card
            className={`text-center ${
              isDark
                ? '!border !border-purple-700 !bg-purple-900'
                : '!border !border-purple-300 !bg-purple-100'
            }`}
          >
            <div
              className={`text-2xl font-bold ${
                isDark ? '!text-white' : '!text-purple-600'
              }`}
            >
              {permissions.reduce((sum, p) => sum + p.actions.length, 0)}
            </div>
            <div
              className={`text-sm ${isDark ? '!text-purple-200' : '!text-purple-700'}`}
            >
              {t('totalActions')}
            </div>
          </Card>
        </Col>
      </Row>

      {/* Permissions by Category */}
      <Collapse
        defaultActiveKey={categories.map((c) => c.id)}
        className='permission-collapse'
        size='large'
      >
        {permissionsByCategory.map(
          ({ category, permissions: categoryPermissions }) => (
            <Panel
              key={category.id}
              className='permission-panel'
              header={
                <div className='flex items-center justify-between w-full pr-4'>
                  <div className='flex items-center gap-3'>
                    {getCategoryIcon(category.icon)}
                    <div>
                      <Title level={5} className='mb-0'>
                        {category.name}
                      </Title>
                      <Text className='text-sm text-gray-500'>
                        {category.description}
                      </Text>
                    </div>
                  </div>
                  <Badge
                    count={categoryPermissions.length}
                    style={{
                      backgroundColor: '#698dcf',
                    }}
                  />
                </div>
              }
            >
              <div className='space-y-4'>
                {categoryPermissions.map((permission) => (
                  <Card
                    key={permission.id}
                    className='border border-gray-200 hover:border-blue-300 transition-colors'
                    size='small'
                  >
                    <div className='space-y-3'>
                      {/* Permission Header */}
                      <div className='flex items-start justify-between'>
                        <div className='flex-1'>
                          <div className='flex items-center gap-2 mb-1'>
                            <KeyOutlined className='text-blue-500' />
                            <Title level={5} className='mb-0'>
                              {permission.name}
                            </Title>
                          </div>
                          <Paragraph className='text-gray-600 mb-0 text-sm'>
                            {permission.description}
                          </Paragraph>
                        </div>
                        <Tag color='blue' className='ml-2'>
                          {permission.actions.length} {t('actions')}
                        </Tag>
                      </div>

                      {/* Permission Actions */}
                      <div>
                        <Text className='text-sm font-medium text-gray-700 block mb-2'>
                          {t('availableActions')}:
                        </Text>
                        <div className='flex flex-wrap gap-2'>
                          {permission.actions.map((action) => (
                            <Tooltip
                              key={action.id}
                              title={action.description}
                              placement='top'
                            >
                              <Tag
                                color={getActionColor(action.type)}
                                className='cursor-help'
                              >
                                {getActionIcon(action.type)}
                                <span>{action.name}</span>
                              </Tag>
                            </Tooltip>
                          ))}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </Panel>
          ),
        )}
      </Collapse>

      {filteredPermissions.length === 0 && (
        <Card className='text-center py-8'>
          <KeyOutlined className='text-gray-400 text-4xl mb-4' />
          <Title level={4} className='text-gray-500'>
            {t('noPermissionsFound')}
          </Title>
          <Text className='text-gray-400'>{t('tryDifferentSearch')}</Text>
        </Card>
      )}
    </div>
  );
};

export default PermissionsList;
