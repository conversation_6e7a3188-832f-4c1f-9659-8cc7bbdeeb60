import {
  UserOutlined,
  DeleteOutlined,
  MailOutlined,
  CalendarOutlined,
  SafetyOutlined,
} from '@ant-design/icons';
import {
  Tag,
  Button,
  Space,
  Tooltip,
  Popconfirm,
  Avatar,
  Typography,
} from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { GalaxyTable } from '@/components';
import { useResponsive } from '@/hooks/useResponsive.ts';
import { useRemoveUserRole } from '@/hooks/useRoles.ts';
import type { UserRole, Role } from '@/types';
import { formatDate } from '@/utils/tableUtils.ts';

const { Text } = Typography;

interface UserRolesListProps {
  userRoles: UserRole[];
  roles: Role[];
  loading: boolean;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
}

const UserRolesList: React.FC<UserRolesListProps> = ({
  userRoles,
  roles,
  loading,
  pagination,
}) => {
  const { t } = useTranslation('userRolesList');
  const { isMobile } = useResponsive();
  const removeUserRoleMutation = useRemoveUserRole();

  const handleRemoveRole = async (userRoleId: string) => {
    removeUserRoleMutation.mutate(userRoleId);
  };

  const getRoleInfo = (roleId: string) => {
    return roles.find((role) => role.id === roleId);
  };

  const getRoleColor = (roleId: string) => {
    const role = getRoleInfo(roleId);
    if (!role) return 'default';

    if (role.isSystem) return 'gold';
    if (role.status === 'active') return 'green';
    return 'red';
  };

  const columns = [
    {
      title: t('user'),
      key: 'user',
      render: (record: UserRole) => (
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
          <Avatar
            size={isMobile ? 32 : 40}
            className='bg-gradient-to-br from-blue-500 to-purple-600 text-white font-medium'
            icon={<UserOutlined />}
          >
            {record.userName.charAt(0).toUpperCase()}
          </Avatar>
          <div className='min-w-0 flex-1'>
            <Text
              className={`font-medium text-gray-900 block ${isMobile ? 'text-sm' : ''}`}
            >
              {record.userName}
            </Text>
            <div
              className={`flex items-center gap-1 ${isMobile ? 'text-xs' : 'text-sm'} text-gray-500`}
            >
              <MailOutlined className='text-xs' />
              <span className='truncate'>{record.userEmail}</span>
            </div>
            {!isMobile && (
              <Text className='text-xs text-gray-400'>ID: {record.userId}</Text>
            )}
          </div>
        </div>
      ),
    },
    {
      title: t('assignedRole'),
      key: 'role',
      render: (record: UserRole) => {
        const role = getRoleInfo(record.roleId);
        return (
          <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
            <SafetyOutlined className='text-blue-500' />
            <div className='min-w-0 flex-1'>
              <Tag
                color={getRoleColor(record.roleId)}
                className={`font-medium ${isMobile ? 'text-xs' : ''}`}
              >
                {record.roleName}
              </Tag>
              {role && !isMobile && (
                <div className='text-xs text-gray-500 mt-1'>
                  {role.permissions.length} {t('permissions')}
                  {role.isSystem && (
                    <span className='ml-2 text-yellow-600'>
                      • {t('system')}
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      title: t('assignedAt'),
      dataIndex: 'assignedAt',
      key: 'assignedAt',
      width: isMobile ? 100 : 150,
      render: (date: string) => (
        <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
          <CalendarOutlined className='text-gray-400' />
          <Text className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
            {isMobile ? formatDate(date).slice(0, 10) : formatDate(date)}
          </Text>
        </div>
      ),
      sorter: (a: UserRole, b: UserRole) =>
        new Date(a.assignedAt).getTime() - new Date(b.assignedAt).getTime(),
      responsive: isMobile ? ['md'] : undefined,
    },
    {
      title: t('assignedBy'),
      dataIndex: 'assignedBy',
      key: 'assignedBy',
      width: isMobile ? 100 : 120,
      render: (assignedBy: string) => (
        <Text className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
          {assignedBy}
        </Text>
      ),
      responsive: isMobile ? ['lg'] : undefined,
    },
    {
      title: t('actions'),
      key: 'actions',
      width: isMobile ? 60 : 100,
      render: (record: UserRole) => {
        const role = getRoleInfo(record.roleId);
        const isSystemRole = role?.isSystem;

        return (
          <Space size='small'>
            <Popconfirm
              title={t('removeRoleConfirmTitle')}
              description={t('removeRoleConfirmDescription')}
              onConfirm={() => handleRemoveRole(record.id)}
              okText={t('common.yes')}
              cancelText={t('common.no')}
              okButtonProps={{ danger: true }}
              disabled={isSystemRole}
            >
              <Tooltip
                title={
                  isSystemRole ? t('cannotRemoveSystemRole') : t('removeRole')
                }
              >
                <Button
                  type='text'
                  icon={<DeleteOutlined />}
                  className='text-red-600 hover:text-red-800'
                  loading={removeUserRoleMutation.isPending}
                  disabled={isSystemRole}
                  size={isMobile ? 'small' : 'middle'}
                />
              </Tooltip>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <GalaxyTable
      data={userRoles}
      columns={columns}
      loading={loading}
      rowKey='id'
      pagination={{
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        onChange: pagination.onChange,
        showSizeChanger: !isMobile,
        showQuickJumper: !isMobile,
        showTotal: !isMobile
          ? (total, range) =>
              `${range[0]}-${range[1]} ${t('of')} ${total} ${t('assignments')}`
          : undefined,
      }}
      scroll={isMobile ? { x: 800 } : undefined}
      size={isMobile ? 'small' : 'middle'}
      className='user-roles-table'
    />
  );
};

export default UserRolesList;
