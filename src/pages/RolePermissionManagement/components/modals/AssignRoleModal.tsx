import {
  UserOutlined,
  TeamOutlined,
  KeyOutlined,
  MailOutlined,
  SafetyOutlined,
} from '@ant-design/icons';
import {
  Modal,
  Form,
  Select,
  Card,
  Typography,
  Tag,
  Button,
  Avatar,
  Divider,
  Alert,
} from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useEmployees } from '@/hooks/useManagement.ts';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import { useAssignUserRole } from '@/hooks/useRoles.ts';
import type { Role } from '@/types';

const { Title, Paragraph } = Typography;
const { Option } = Select;

interface AssignRoleModalProps {
  visible: boolean;
  onClose: () => void;
  roles: Role[];
}

const AssignRoleModal: React.FC<AssignRoleModalProps> = ({
  visible,
  onClose,
  roles,
}) => {
  const { t } = useTranslation('assignRoleModal');
  const { isMobile } = useResponsive();
  const [form] = Form.useForm();
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [_, setSelectedUser] = useState<string>('');
  const assignRoleMutation = useAssignUserRole();

  // Responsive values
  const modalWidth = useResponsiveValue({
    xs: '95%',
    sm: '90%',
    md: '600px',
    lg: '600px',
    xl: '600px',
    '2xl': '600px',
  });

  // Fetch employees for user selection
  const { data: employeesResponse } = useEmployees({ pageSize: 100 });
  const employees = employeesResponse?.data?.data || [];

  const handleRoleChange = (roleId: string) => {
    const role = roles.find((r) => r.id === roleId);
    setSelectedRole(role || null);
  };

  const handleSubmit = async (values: any) => {
    try {
      await assignRoleMutation.mutateAsync({
        userId: values.userId,
        roleId: values.roleId,
      });
      form.resetFields();
      setSelectedRole(null);
      setSelectedUser('');
      onClose();
    } catch (error) {
      console.error('Failed to assign role:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setSelectedRole(null);
    setSelectedUser('');
    onClose();
  };

  const getRoleColor = (role: Role) => {
    if (role.isSystem) return 'gold';
    return 'blue';
  };

  return (
    <Modal
      title={
        <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
          <TeamOutlined className='text-green-600' />
          <span className={isMobile ? 'text-base' : ''}>{t('assignRole')}</span>
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      width={modalWidth}
      centered
      footer={[
        <Button
          key='cancel'
          onClick={handleCancel}
          className={isMobile ? 'w-full mb-2' : ''}
          size={isMobile ? 'middle' : 'large'}
        >
          {t('cancel')}
        </Button>,
        <Button
          key='submit'
          type='primary'
          loading={assignRoleMutation.isPending}
          onClick={() => form.submit()}
          icon={<UserOutlined />}
          className={`bg-green-600 hover:bg-green-700 ${isMobile ? 'w-full' : ''}`}
          size={isMobile ? 'middle' : 'large'}
        >
          {isMobile ? t('assign') : t('assignRole')}
        </Button>,
      ]}
      styles={{
        body: {
          padding: isMobile ? '16px' : '24px',
          maxHeight: isMobile ? '70vh' : '75vh',
          overflowY: 'auto',
        },
        footer: { padding: isMobile ? '16px' : '16px 24px' },
      }}
    >
      <Form form={form} layout='vertical' onFinish={handleSubmit}>
        <Alert
          message={isMobile ? t('assIF') : t('assignRoleInfo')}
          description={!isMobile ? t('assignRoleInfoDescription') : undefined}
          type='info'
          className={isMobile ? 'mb-4' : 'mb-6'}
          showIcon
        />

        {/* User Selection */}
        <Form.Item
          name='userId'
          label={
            <span className={isMobile ? 'text-sm' : ''}>{t('selectUser')}</span>
          }
          rules={[{ required: true, message: t('userRequired') }]}
          className={isMobile ? 'mb-4' : 'mb-6'}
        >
          <Select
            placeholder={t('selectUserPlaceholder')}
            size={isMobile ? 'middle' : 'large'}
            showSearch
            optionFilterProp='children'
            style={{
              height: isMobile ? '50px' : '70px',
              caretColor: 'transparent',
            }}
            filterOption={(input, option) =>
              (option?.children as unknown as string)
                ?.toLowerCase()
                .includes(input.toLowerCase())
            }
            onChange={setSelectedUser}
          >
            {employees.map((employee) => (
              <Option key={employee.id} value={employee.id}>
                <div
                  className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'} py-1`}
                >
                  <Avatar
                    size={isMobile ? 24 : 32}
                    className='bg-gradient-to-br from-blue-500 to-purple-600 text-white font-medium'
                  >
                    {employee.name.charAt(0).toUpperCase()}
                  </Avatar>
                  <div className='min-w-0 flex-1'>
                    <div className={`font-medium ${isMobile ? 'text-sm' : ''}`}>
                      {employee.name}
                    </div>
                    <div
                      className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 flex items-center gap-1`}
                    >
                      <MailOutlined className='text-xs' />
                      <span className='truncate'>{employee.email}</span>
                    </div>
                  </div>
                </div>
              </Option>
            ))}
          </Select>
        </Form.Item>

        {/* Role Selection */}
        <Form.Item
          name='roleId'
          label={
            <span className={isMobile ? 'text-sm' : ''}>{t('selectRole')}</span>
          }
          rules={[{ required: true, message: t('roleRequired') }]}
          className={isMobile ? 'mb-4' : 'mb-6'}
        >
          <Select
            placeholder={t('selectRolePlaceholder')}
            size={isMobile ? 'middle' : 'large'}
            style={{
              height: isMobile ? '50px' : '70px',
              caretColor: 'transparent',
            }}
            onChange={handleRoleChange}
          >
            {roles.map((role) => (
              <Option key={role.id} value={role.id}>
                <div
                  className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'} py-1`}
                >
                  <SafetyOutlined className='text-blue-500' />
                  <div className='min-w-0 flex-1'>
                    <div
                      className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}
                    >
                      <span
                        className={`font-medium ${isMobile ? 'text-sm' : ''}`}
                      >
                        {role.name}
                      </span>
                      <Tag
                        color={getRoleColor(role)}
                        className={isMobile ? 'text-xs' : ''}
                      >
                        {role.isSystem ? t('system') : t('custom')}
                      </Tag>
                    </div>
                    {!isMobile && (
                      <div className='text-sm text-gray-500 truncate'>
                        {role.description}
                      </div>
                    )}
                  </div>
                </div>
              </Option>
            ))}
          </Select>
        </Form.Item>

        {/* Role Details */}
        {selectedRole && (
          <>
            <Divider />
            <div className={isMobile ? 'mb-3' : 'mb-4'}>
              <Title
                level={5}
                className={`text-gray-900 ${isMobile ? 'mb-2 text-base' : 'mb-3'}`}
              >
                {t('roleDetails')}
              </Title>

              <Card className='border border-blue-200 bg-blue-50/50'>
                <div
                  className={`flex items-start ${isMobile ? 'gap-2' : 'gap-3'}`}
                >
                  <SafetyOutlined
                    className={`text-blue-600 ${isMobile ? 'text-lg' : 'text-xl'} mt-1`}
                  />
                  <div className='flex-1 min-w-0'>
                    <div
                      className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'} mb-2`}
                    >
                      <Title
                        level={5}
                        className={`mb-0 ${isMobile ? 'text-sm' : ''}`}
                      >
                        {selectedRole.name}
                      </Title>
                      <Tag
                        color={getRoleColor(selectedRole)}
                        className={isMobile ? 'text-xs' : ''}
                      >
                        {selectedRole.isSystem ? t('system') : t('custom')}
                      </Tag>
                    </div>

                    {!isMobile && (
                      <Paragraph className='text-gray-600 mb-3'>
                        {selectedRole.description}
                      </Paragraph>
                    )}

                    <div
                      className={`grid ${isMobile ? 'grid-cols-1 gap-2' : 'grid-cols-2 gap-4'}`}
                    >
                      <div
                        className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}
                      >
                        <KeyOutlined className='text-blue-500' />
                        <div>
                          <div
                            className={`font-medium text-blue-600 ${isMobile ? 'text-sm' : ''}`}
                          >
                            {selectedRole.permissions.length}
                          </div>
                          <div
                            className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500`}
                          >
                            {t('permissions')}
                          </div>
                        </div>
                      </div>
                      <div
                        className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}
                      >
                        <UserOutlined className='text-green-500' />
                        <div>
                          <div
                            className={`font-medium text-green-600 ${isMobile ? 'text-sm' : ''}`}
                          >
                            {selectedRole.userCount}
                          </div>
                          <div
                            className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500`}
                          >
                            {t('assignedUsers')}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </>
        )}
      </Form>
    </Modal>
  );
};

export default AssignRoleModal;
