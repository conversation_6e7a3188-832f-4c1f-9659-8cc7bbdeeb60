import {
  SaveOutlined,
  KeyOutlined,
  UserOutlined,
  DollarOutlined,
  TrophyOutlined,
  SafetyOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  ControlOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import {
  Modal,
  Form,
  Input,
  Checkbox,
  Card,
  Collapse,
  Typography,
  Tag,
  Tooltip,
  Button,
  Divider,
  Alert,
} from 'antd';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useUpdateRole } from '@/hooks/useRoles.ts';
import { useUserStore } from '@/stores';
import type {
  Role,
  Permission,
  PermissionCategory,
  UpdateRoleRequest,
} from '@/types';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Panel } = Collapse;

interface EditRoleModalProps {
  visible: boolean;
  role: Role | null;
  onClose: () => void;
  permissions: Permission[];
  categories: PermissionCategory[];
}

const EditRoleModal: React.FC<EditRoleModalProps> = (props) => {
  const { visible, role, onClose, permissions, categories } = props || {};
  const { isDark } = useUserStore();
  const { t } = useTranslation('editRoleModal');
  const [form] = Form.useForm();
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const updateRoleMutation = useUpdateRole();

  useEffect(() => {
    if (role && visible) {
      form.setFieldsValue({
        name: role.name,
        description: role.description,
      });
      setSelectedPermissions(role.permissions);
    }
  }, [role, visible, form]);

  const getCategoryIcon = (iconName?: string) => {
    switch (iconName) {
      case 'UserOutlined':
        return <UserOutlined className='text-blue-600' />;
      case 'DollarOutlined':
        return <DollarOutlined className='text-green-600' />;
      case 'TrophyOutlined':
        return <TrophyOutlined className='text-yellow-600' />;
      case 'SafetyOutlined':
        return <SafetyOutlined className='text-red-600' />;
      case 'BarChartOutlined':
        return <BarChartOutlined className='text-purple-600' />;
      case 'SettingOutlined':
        return <SettingOutlined className='text-gray-600' />;
      default:
        return <KeyOutlined className='text-blue-600' />;
    }
  };

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'view':
        return <EyeOutlined className='text-blue-500' />;
      case 'create':
        return <PlusOutlined className='text-green-500' />;
      case 'edit':
        return <EditOutlined className='text-orange-500' />;
      case 'delete':
        return <DeleteOutlined className='text-red-500' />;
      case 'manage':
        return <ControlOutlined className='text-purple-500' />;
      default:
        return <KeyOutlined className='text-gray-500' />;
    }
  };

  const getActionColor = (type: string) => {
    switch (type) {
      case 'view':
        return 'blue';
      case 'create':
        return 'green';
      case 'edit':
        return 'orange';
      case 'delete':
        return 'red';
      case 'manage':
        return 'purple';
      default:
        return 'default';
    }
  };

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions([...selectedPermissions, permissionId]);
    } else {
      setSelectedPermissions(
        selectedPermissions.filter((id) => id !== permissionId),
      );
    }
  };

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    const categoryPermissions = permissions
      .filter((p) => p.category.id === categoryId)
      .map((p) => p.id);

    if (checked) {
      const newPermissions = [
        ...new Set([...selectedPermissions, ...categoryPermissions]),
      ];
      setSelectedPermissions(newPermissions);
    } else {
      setSelectedPermissions(
        selectedPermissions.filter((id) => !categoryPermissions.includes(id)),
      );
    }
  };

  const isCategorySelected = (categoryId: string) => {
    const categoryPermissions = permissions
      .filter((p) => p.category.id === categoryId)
      .map((p) => p.id);

    return categoryPermissions.every((id) => selectedPermissions.includes(id));
  };

  const isCategoryIndeterminate = (categoryId: string) => {
    const categoryPermissions = permissions
      .filter((p) => p.category.id === categoryId)
      .map((p) => p.id);

    const selectedCount = categoryPermissions.filter((id) =>
      selectedPermissions.includes(id),
    ).length;
    return selectedCount > 0 && selectedCount < categoryPermissions.length;
  };

  const handleSubmit = async (values: any) => {
    if (!role) return;

    const updateData: UpdateRoleRequest = {
      name: values.name,
      description: values.description,
      permissions: selectedPermissions,
    };

    try {
      await updateRoleMutation.mutateAsync({ id: role.id, data: updateData });
      onClose();
    } catch (error) {
      console.error('Failed to update role:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    if (role) {
      setSelectedPermissions(role.permissions);
    }
    onClose();
  };

  if (!role) return null;

  // Group permissions by category
  const permissionsByCategory = categories.map((category) => ({
    category,
    permissions: permissions.filter((p) => p.category.id === category.id),
  }));

  return (
    <Modal
      title={
        <div className='flex items-center gap-2'>
          <EditOutlined className='text-blue-600' />
          {t('editRole')} - {role.name}
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      width={800}
      footer={[
        <Button key='cancel' onClick={handleCancel}>
          {t('cancel')}
        </Button>,
        <Button
          key='submit'
          type='primary'
          loading={updateRoleMutation.isPending}
          onClick={() => form.submit()}
          icon={<SaveOutlined />}
          className='bg-blue-600 hover:bg-blue-700'
          disabled={role.isSystem}
        >
          {t('save')}
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout='vertical'
        onFinish={handleSubmit}
        className='max-h-[70vh] overflow-y-auto'
      >
        {/* System Role Warning */}
        {role.isSystem && (
          <Alert
            message={t('systemRoleWarning')}
            description={t('systemRoleWarningDescription')}
            type='warning'
            icon={<WarningOutlined />}
            className='mb-6'
            showIcon
          />
        )}

        {/* Role Info */}
        <div className='mb-6'>
          <div className='flex items-center gap-2 mb-4'>
            <Title level={5} className='text-gray-900 mb-0 mt-2'>
              {t('roleInformation')}
            </Title>
            {role.isSystem && <Tag color='gold'>{t('system')}</Tag>}
          </div>

          <div
            className={`${isDark ? 'bg-gray-600' : 'bg-gray-300'} grid grid-cols-2 gap-4 mb-4 p-4 rounded-lg`}
          >
            <div>
              <Text className='text-sm text-gray-500'>
                {t('assignedUsers')}
              </Text>
              <div className='font-medium text-gray-900'>{role.userCount}</div>
            </div>
            <div>
              <Text className='text-sm text-gray-500'>
                {t('currentPermissions')}
              </Text>
              <div className='font-medium text-gray-900'>
                {role.permissions.length}
              </div>
            </div>
          </div>

          <Form.Item
            name='name'
            label={t('roleName')}
            rules={[
              { required: true, message: t('roleNameRequired') },
              { min: 2, message: t('roleNameMinLength') },
              { max: 50, message: t('roleNameMaxLength`') },
            ]}
          >
            <Input
              placeholder={t('roleNamePlaceholder')}
              size='large'
              disabled={role.isSystem}
            />
          </Form.Item>

          <Form.Item
            name='description'
            label={t('roleDescription')}
            rules={[
              {
                required: true,
                message: t('roleDescriptionRequired'),
              },
              {
                max: 200,
                message: t('roleDescriptionMaxLength'),
              },
            ]}
          >
            <TextArea
              rows={3}
              placeholder={t('roleDescriptionPlaceholder')}
              size='large'
              disabled={role.isSystem}
            />
          </Form.Item>
        </div>

        <Divider />

        {/* Permission Selection */}
        <div className='mb-6'>
          <div className='flex items-center justify-between mb-4'>
            <Title level={5} className='text-gray-900 mb-0'>
              {t('updatePermissions')}
            </Title>
            <Tag color='blue' className='font-medium'>
              {selectedPermissions.length} {t('selected')}
            </Tag>
          </div>

          {role.userCount > 0 && (
            <Alert
              message={t('permissionChangeWarning')}
              description={t('permissionChangeWarningDescription', {
                count: role.userCount,
              })}
              type='info'
              className='!mb-4'
              showIcon
            />
          )}

          <Collapse
            defaultActiveKey={categories.map((c) => c.id)}
            className='permission-selection-collapse'
          >
            {permissionsByCategory.map(
              ({ category, permissions: categoryPermissions }) => (
                <Panel
                  key={category.id}
                  header={
                    <div className='flex items-center justify-between w-full pr-4'>
                      <div className='flex items-center gap-3'>
                        <Checkbox
                          checked={isCategorySelected(category.id)}
                          indeterminate={isCategoryIndeterminate(category.id)}
                          onChange={(e) =>
                            handleCategoryChange(category.id, e.target.checked)
                          }
                          onClick={(e) => e.stopPropagation()}
                          disabled={role.isSystem}
                        />
                        {getCategoryIcon(category.icon)}
                        <div>
                          <Text className='font-medium'>{category.name}</Text>
                          <div className='text-sm text-gray-500'>
                            {category.description}
                          </div>
                        </div>
                      </div>
                      <Tag color='blue'>
                        {categoryPermissions.length}
                        {t('permissions')}
                      </Tag>
                    </div>
                  }
                >
                  <div className='space-y-4 pl-8'>
                    {categoryPermissions.map((permission) => (
                      <Card
                        key={permission.id}
                        size='small'
                        className='border border-gray-200'
                      >
                        <div className='flex items-start gap-3'>
                          <Checkbox
                            checked={selectedPermissions.includes(
                              permission.id,
                            )}
                            onChange={(e) =>
                              handlePermissionChange(
                                permission.id,
                                e.target.checked,
                              )
                            }
                            disabled={role.isSystem}
                          />
                          <div className='flex-1'>
                            <div className='flex items-center gap-2 mb-1'>
                              <KeyOutlined className='text-blue-500 text-sm' />
                              <Text className='font-medium'>
                                {permission.name}
                              </Text>
                            </div>
                            <Paragraph className='text-gray-600 mb-2 text-sm'>
                              {permission.description}
                            </Paragraph>
                            <div className='flex flex-wrap gap-1'>
                              {permission.actions.map((action) => (
                                <Tooltip
                                  key={action.id}
                                  title={action.description}
                                  placement='top'
                                >
                                  <Tag
                                    color={getActionColor(action.type)}
                                    className='flex items-center gap-1'
                                  >
                                    {getActionIcon(action.type)}
                                    {action.name}
                                  </Tag>
                                </Tooltip>
                              ))}
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </Panel>
              ),
            )}
          </Collapse>
        </div>
      </Form>
    </Modal>
  );
};

export default EditRoleModal;
