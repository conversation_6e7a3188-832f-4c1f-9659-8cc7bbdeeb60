import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  DollarOutlined,
  ContactsOutlined,
  ClockCircleOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import {
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Row,
  Col,
  TimePicker,
  Checkbox,
  Divider,
  Button,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import { useUserStore } from '@/stores';
import type { Employee, UpdateEmployeeRequest } from '@/types';

const { Option } = Select;
const { TextArea } = Input;

interface EditEmployeeModalProps {
  visible: boolean;
  employee: Employee | null;
  onClose: () => void;

  onSave: (employeeId: string, data: UpdateEmployeeRequest) => void;
  loading?: boolean;
}

const EditEmployeeModal: React.FC<EditEmployeeModalProps> = (props) => {
  const { visible, employee, onClose, onSave, loading = false } = props || {};
  const { isDark } = useUserStore();
  const { t } = useTranslation('editEmployeeModal');
  const { isMobile } = useResponsive();
  const [form] = Form.useForm();

  // Responsive values
  const modalWidth = useResponsiveValue({
    xs: '95%',
    sm: '90%',
    md: '800px',
    lg: '900px',
    xl: '900px',
    '2xl': '900px',
  });

  useEffect(() => {
    if (employee && visible) {
      // Set form values when employee data is available
      form.setFieldsValue({
        name: employee.name,
        email: employee.email,
        phone: employee.phone,
        role: employee.role,
        department: employee.department,
        salary: employee.salary,
        address: employee.address,
        emergencyContactName: employee.emergencyContact?.name,
        emergencyContactPhone: employee.emergencyContact?.phone,
        emergencyContactRelationship: employee.emergencyContact?.relationship,
        permissions: employee.permissions,
        // Schedule
        mondayOff: employee.schedule?.monday.off,
        mondayStart: employee.schedule?.monday.start
          ? dayjs(employee.schedule.monday.start, 'HH:mm')
          : null,
        mondayEnd: employee.schedule?.monday.end
          ? dayjs(employee.schedule.monday.end, 'HH:mm')
          : null,
        tuesdayOff: employee.schedule?.tuesday.off,
        tuesdayStart: employee.schedule?.tuesday.start
          ? dayjs(employee.schedule.tuesday.start, 'HH:mm')
          : null,
        tuesdayEnd: employee.schedule?.tuesday.end
          ? dayjs(employee.schedule.tuesday.end, 'HH:mm')
          : null,
        wednesdayOff: employee.schedule?.wednesday.off,
        wednesdayStart: employee.schedule?.wednesday.start
          ? dayjs(employee.schedule.wednesday.start, 'HH:mm')
          : null,
        wednesdayEnd: employee.schedule?.wednesday.end
          ? dayjs(employee.schedule.wednesday.end, 'HH:mm')
          : null,
        thursdayOff: employee.schedule?.thursday.off,
        thursdayStart: employee.schedule?.thursday.start
          ? dayjs(employee.schedule.thursday.start, 'HH:mm')
          : null,
        thursdayEnd: employee.schedule?.thursday.end
          ? dayjs(employee.schedule.thursday.end, 'HH:mm')
          : null,
        fridayOff: employee.schedule?.friday.off,
        fridayStart: employee.schedule?.friday.start
          ? dayjs(employee.schedule.friday.start, 'HH:mm')
          : null,
        fridayEnd: employee.schedule?.friday.end
          ? dayjs(employee.schedule.friday.end, 'HH:mm')
          : null,
        saturdayOff: employee.schedule?.saturday.off,
        saturdayStart: employee.schedule?.saturday.start
          ? dayjs(employee.schedule.saturday.start, 'HH:mm')
          : null,
        saturdayEnd: employee.schedule?.saturday.end
          ? dayjs(employee.schedule.saturday.end, 'HH:mm')
          : null,
        sundayOff: employee.schedule?.sunday.off,
        sundayStart: employee.schedule?.sunday.start
          ? dayjs(employee.schedule.sunday.start, 'HH:mm')
          : null,
        sundayEnd: employee.schedule?.sunday.end
          ? dayjs(employee.schedule.sunday.end, 'HH:mm')
          : null,
      });
    }
  }, [employee, visible, form]);
  const handleSubmit = async (values: {
    name: string;
    email: string;
    phone: string;
    role: Employee['role'];
    department: Employee['department'];
    salary: number;
    address: string;
    permissions: string[];

    emergencyContactName: string;
    emergencyContactPhone: string;
    emergencyContactRelationship: string;

    mondayOff: boolean;
    mondayStart?: dayjs.Dayjs;
    mondayEnd?: dayjs.Dayjs;

    tuesdayOff: boolean;
    tuesdayStart?: dayjs.Dayjs;
    tuesdayEnd?: dayjs.Dayjs;

    wednesdayOff: boolean;
    wednesdayStart?: dayjs.Dayjs;
    wednesdayEnd?: dayjs.Dayjs;

    thursdayOff: boolean;
    thursdayStart?: dayjs.Dayjs;
    thursdayEnd?: dayjs.Dayjs;

    fridayOff: boolean;
    fridayStart?: dayjs.Dayjs;
    fridayEnd?: dayjs.Dayjs;

    saturdayOff: boolean;
    saturdayStart?: dayjs.Dayjs;
    saturdayEnd?: dayjs.Dayjs;

    sundayOff: boolean;
    sundayStart?: dayjs.Dayjs;
    sundayEnd?: dayjs.Dayjs;
  }) => {
    if (!employee) return;

    const updateData: UpdateEmployeeRequest = {
      name: values.name,
      email: values.email,
      phone: values.phone,
      role: values.role,
      department: values.department,
      salary: values.salary,
      address: values.address,
      permissions: values.permissions,
      emergencyContact: {
        name: values.emergencyContactName,
        phone: values.emergencyContactPhone,
        relationship: values.emergencyContactRelationship,
      },
      schedule: {
        monday: {
          off: values.mondayOff,
          start: values.mondayOff
            ? ''
            : values.mondayStart?.format('HH:mm') || '',
          end: values.mondayOff ? '' : values.mondayEnd?.format('HH:mm') || '',
        },
        tuesday: {
          off: values.tuesdayOff,
          start: values.tuesdayOff
            ? ''
            : values.tuesdayStart?.format('HH:mm') || '',
          end: values.tuesdayOff
            ? ''
            : values.tuesdayEnd?.format('HH:mm') || '',
        },
        wednesday: {
          off: values.wednesdayOff,
          start: values.wednesdayOff
            ? ''
            : values.wednesdayStart?.format('HH:mm') || '',
          end: values.wednesdayOff
            ? ''
            : values.wednesdayEnd?.format('HH:mm') || '',
        },
        thursday: {
          off: values.thursdayOff,
          start: values.thursdayOff
            ? ''
            : values.thursdayStart?.format('HH:mm') || '',
          end: values.thursdayOff
            ? ''
            : values.thursdayEnd?.format('HH:mm') || '',
        },
        friday: {
          off: values.fridayOff,
          start: values.fridayOff
            ? ''
            : values.fridayStart?.format('HH:mm') || '',
          end: values.fridayOff ? '' : values.fridayEnd?.format('HH:mm') || '',
        },
        saturday: {
          off: values.saturdayOff,
          start: values.saturdayOff
            ? ''
            : values.saturdayStart?.format('HH:mm') || '',
          end: values.saturdayOff
            ? ''
            : values.saturdayEnd?.format('HH:mm') || '',
        },
        sunday: {
          off: values.sundayOff,
          start: values.sundayOff
            ? ''
            : values.sundayStart?.format('HH:mm') || '',
          end: values.sundayOff ? '' : values.sundayEnd?.format('HH:mm') || '',
        },
      },
    };

    onSave(employee.id, updateData);
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  const availablePermissions = [
    'all',
    'manage_employees',
    'view_reports',
    'manage_shifts',
    'operate_register',
    'process_transactions',
    'monitor_premises',
    'incident_reports',
    'equipment_maintenance',
    'facility_repairs',
  ];

  const renderScheduleRow = (day: string, dayKey: string) => (
    <Row gutter={isMobile ? 8 : 16} key={day} className='mb-4'>
      <Col xs={24} sm={4}>
        <div
          className={`font-medium ${isDark ? 'text-black' : 'text-gray-700'} ${isMobile ? 'mb-2' : 'pt-2'} ${isMobile ? 'text-sm' : ''}`}
        >
          {t(`${day.toLowerCase()}`)}
        </div>
      </Col>
      <Col xs={24} sm={4}>
        <Form.Item
          name={`${dayKey}Off`}
          valuePropName='checked'
          className='mb-0'
        >
          <Checkbox className={isMobile ? 'text-sm' : ''}>
            {isMobile ? 'Off' : t('dayOff')}
          </Checkbox>
        </Form.Item>
      </Col>
      <Col xs={12} sm={8}>
        <Form.Item
          name={`${dayKey}Start`}
          className='mb-0'
          dependencies={[`${dayKey}Off`]}
        >
          <TimePicker
            format='HH:mm'
            placeholder={isMobile ? 'Start' : t('startTime')}
            disabled={form.getFieldValue(`${dayKey}Off`)}
            className='w-full'
            size={isMobile ? 'middle' : 'large'}
          />
        </Form.Item>
      </Col>
      <Col xs={12} sm={8}>
        <Form.Item
          name={`${dayKey}End`}
          className='mb-0'
          dependencies={[`${dayKey}Off`]}
        >
          <TimePicker
            format='HH:mm'
            placeholder={isMobile ? 'End' : t('endTime')}
            disabled={form.getFieldValue(`${dayKey}Off`)}
            className='w-full'
            size={isMobile ? 'middle' : 'large'}
          />
        </Form.Item>
      </Col>
    </Row>
  );

  return (
    <Modal
      title={
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-2'}`}>
          <UserOutlined className='text-blue-600' />
          <span className={isMobile ? 'text-sm' : ''}>{t('editEmployee')}</span>
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      width={modalWidth}
      className={isMobile ? 'top-4' : 'top-8'}
      footer={[
        <Button
          key='cancel'
          onClick={handleCancel}
          className={isMobile ? 'w-full mb-2' : ''}
          size={isMobile ? 'middle' : 'large'}
        >
          {t('cancel')}
        </Button>,
        <Button
          key='save'
          type='primary'
          loading={loading}
          onClick={() => form.submit()}
          icon={<SaveOutlined />}
          className={`bg-blue-600 hover:bg-blue-700 ${isMobile ? 'w-full' : ''}`}
          size={isMobile ? 'middle' : 'large'}
        >
          {t('save')}
        </Button>,
      ]}
      styles={{
        body: { padding: isMobile ? '16px' : '24px' },
        footer: { padding: isMobile ? '16px' : '16px 24px' },
      }}
    >
      <Form
        form={form}
        layout='vertical'
        onFinish={handleSubmit}
        className={`${isMobile ? 'max-h-[60vh]' : 'max-h-[70vh]'} overflow-y-auto`}
      >
        {/* Basic Information */}
        <div className={`${isMobile ? 'mb-4' : 'mb-6'}`}>
          <div
            className={`flex items-center ${isMobile ? 'gap-2' : 'gap-2'} ${isMobile ? 'mb-3' : 'mb-4'}`}
          >
            <UserOutlined className='text-blue-600' />
            <span
              className={`font-medium text-gray-900 ${isMobile ? 'text-sm' : ''}`}
            >
              {t('basicInfo')}
            </span>
          </div>

          <Row gutter={isMobile ? 8 : 16}>
            <Col xs={24} sm={12}>
              <Form.Item
                name='name'
                label={t('name')}
                rules={[
                  {
                    required: true,
                    message: t('nameRequired'),
                  },
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  size={isMobile ? 'middle' : 'large'}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name='email'
                label={t('email')}
                rules={[
                  {
                    required: true,
                    message: t('emailRequired'),
                  },
                  {
                    type: 'email',
                    message: t('emailInvalid'),
                  },
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  size={isMobile ? 'middle' : 'large'}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={isMobile ? 8 : 16}>
            <Col xs={24} sm={12}>
              <Form.Item
                name='phone'
                label={t('phone')}
                rules={[
                  {
                    required: true,
                    message: t('phoneRequired'),
                  },
                ]}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  size={isMobile ? 'middle' : 'large'}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name='salary'
                rules={[
                  {
                    required: true,
                    message: t('salaryRequired'),
                  },
                ]}
              >
                <InputNumber
                  prefix={<DollarOutlined />}
                  className='w-full'
                  min={0}
                  size={isMobile ? 'middle' : 'large'}
                  formatter={(value) =>
                    `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  // parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name='role'
                label={t('role')}
                rules={[
                  {
                    required: true,
                    message: t('roleRequired'),
                  },
                ]}
              >
                <Select>
                  <Option value='admin'>{t('admin')}</Option>
                  <Option value='manager'>{t('manager')}</Option>
                  <Option value='cashier'>{t('cashier')}</Option>
                  <Option value='security'>{t('security')}</Option>
                  <Option value='maintenance'>{t('maintenance')}</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name='department'
                label={t('department')}
                rules={[
                  {
                    required: true,
                    message: t('departmentRequired'),
                  },
                ]}
              >
                <Select>
                  <Option value='management'>{t('management')}</Option>
                  <Option value='gaming'>{t('gaming')}</Option>
                  <Option value='security'>{t('security')}</Option>
                  <Option value='maintenance'>{t('maintenance')}</Option>
                  <Option value='customer_service'>
                    {t('customer_service')}
                  </Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name='address' label={t('address')}>
            <TextArea rows={2} />
          </Form.Item>
        </div>

        <Divider />

        {/* Emergency Contact */}
        <div className='mb-6'>
          <div className='flex items-center gap-2 mb-4'>
            <ContactsOutlined className='text-blue-600' />
            <span className='font-medium text-gray-900'>
              {t('emergencyContact')}
            </span>
          </div>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name='emergencyContactName' label={t('contactName')}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name='emergencyContactPhone' label={t('contactPhone')}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name='emergencyContactRelationship'
                label={t('relationship')}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
        </div>

        <Divider />

        {/* Permissions */}
        <div className='mb-6'>
          <div className='flex items-center gap-2 mb-4'>
            <UserOutlined className='text-blue-600' />
            <span className='font-medium text-gray-900'>
              {t('permissions')}
            </span>
          </div>

          <Form.Item
            name='permissions'
            rules={[
              {
                required: true,
                message: t('permissionsRequired'),
              },
            ]}
          >
            <Select
              mode='multiple'
              placeholder={t('selectPermissions')}
              className='w-full'
            >
              {availablePermissions.map((permission) => (
                <Option key={permission} value={permission}>
                  {permission.replace('_', ' ').toUpperCase()}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </div>

        <Divider />

        {/* Schedule */}
        <div className='mb-6'>
          <div className='flex items-center gap-2 mb-4'>
            <ClockCircleOutlined className='!text-blue-600' />
            <span
              className={`font-medium text-lg ${isDark ? 'text-blue-600' : 'text-gray-500'}`}
            >
              {t('schedule')}
            </span>
          </div>

          <div
            className={`p-4 rounded-lg ${isDark ? 'bg-[#766e67]' : 'bg-[#b4bbca]'}`}
          >
            <Row gutter={16} className='mb-2'>
              <Col span={4}>
                <strong>{t('day')}</strong>
              </Col>
              <Col span={4}>
                <strong>{t('dayOff')}</strong>
              </Col>
              <Col span={8}>
                <strong>{t('startTime')}</strong>
              </Col>
              <Col span={8}>
                <strong>{t('endTime')}</strong>
              </Col>
            </Row>

            {renderScheduleRow('Monday', 'monday')}
            {renderScheduleRow('Tuesday', 'tuesday')}
            {renderScheduleRow('Wednesday', 'wednesday')}
            {renderScheduleRow('Thursday', 'thursday')}
            {renderScheduleRow('Friday', 'friday')}
            {renderScheduleRow('Saturday', 'saturday')}
            {renderScheduleRow('Sunday', 'sunday')}
          </div>
        </div>
      </Form>
    </Modal>
  );
};

export default EditEmployeeModal;
