import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  CalendarOutlined,
  HomeOutlined,
  ContactsOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  CloseOutlined,
  CheckCircleOutlined,
  StarOutlined,
} from '@ant-design/icons';
import {
  Modal,
  Descriptions,
  Tag,
  Typography,
  Card,
  Row,
  Col,
  Statistic,
  Avatar,
  Divider,
  Badge,
  Space,
  Progress,
} from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { GalaxyTable } from '@/components';
import { useUserStore } from '@/stores';
import type { Employee } from '@/types';
import { formatDate } from '@/utils/tableUtils.ts';

const { Title, Text } = Typography;

interface EmployeeDetailsModalProps {
  visible: boolean;
  employee: Employee | null;
  onClose: () => void;
}

const EmployeeDetailsModal: React.FC<EmployeeDetailsModalProps> = (props) => {
  const { visible, employee, onClose } = props || {};
  const { isDark } = useUserStore();
  const { t } = useTranslation('employeeDetailsModal');

  if (!employee) return null;
  const getRoleColor = (role: Employee['role']) => {
    switch (role) {
      case 'admin':
        return 'red';
      case 'manager':
        return 'blue';
      case 'cashier':
        return 'green';
      case 'security':
        return 'orange';
      case 'maintenance':
        return 'purple';
      default:
        return 'default';
    }
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return '#52c41a';
    if (score >= 70) return '#faad14';
    return '#ff4d4f';
  };

  const scheduleColumns = [
    {
      title: t('day'),
      dataIndex: 'day',
      key: 'day',
    },
    {
      title: t('startTime'),
      dataIndex: 'start',
      key: 'start',
      render: (start: string, record: { off: boolean }) =>
        record.off ? (
          <Tag color='red'>{t('dayOff')}</Tag>
        ) : (
          <span>{start}</span>
        ),
    },
    {
      title: t('endTime'),
      dataIndex: 'end',
      key: 'end',
      render: (end: string, record: { off: boolean }) =>
        record.off ? <Tag color='red'>{t('dayOff')}</Tag> : <span>{end}</span>,
    },
    {
      title: t('hours'),
      key: 'hours',
      render: (
        _: unknown,
        record: { off: boolean; start: string; end: string },
      ) => {
        if (record.off) return '-';
        const start = new Date(`2024-01-01 ${record.start}`);
        const end = new Date(`2024-01-01 ${record.end}`);
        let hours = (end.getTime() - start.getTime()) / (1000 * 60 * 60);
        if (hours < 0) hours += 24; // Handle overnight shifts
        return `${hours.toFixed(1)}h`;
      },
    },
  ];

  const scheduleData = employee.schedule
    ? [
        {
          key: 'monday',
          day: t('monday'),
          ...employee.schedule.monday,
        },
        {
          key: 'tuesday',
          day: t('tuesday'),
          ...employee.schedule.tuesday,
        },
        {
          key: 'wednesday',
          day: t('wednesday'),
          ...employee.schedule.wednesday,
        },
        {
          key: 'thursday',
          day: t('thursday'),
          ...employee.schedule.thursday,
        },
        {
          key: 'friday',
          day: t('friday'),
          ...employee.schedule.friday,
        },
        {
          key: 'saturday',
          day: t('saturday'),
          ...employee.schedule.saturday,
        },
        {
          key: 'sunday',
          day: t('sunday'),
          ...employee.schedule.sunday,
        },
      ]
    : [];

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      className='top-8'
      closeIcon={
        <CloseOutlined className='text-gray-400 hover:text-gray-600' />
      }
    >
      <div className='p-6'>
        {/* Header */}
        <div className='flex items-center justify-between mb-6'>
          <div className='flex items-center gap-4'>
            <Avatar
              size={64}
              className='bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold text-xl'
            >
              {employee.name.charAt(0).toUpperCase()}
            </Avatar>
            <div>
              <div className='flex items-center gap-3'>
                <Title level={3} className='text-gray-900 mb-0 font-bold'>
                  {employee.name}
                </Title>
                <Badge
                  status={employee.status === 'active' ? 'success' : 'error'}
                  text={
                    <span
                      className={`font-medium ${employee.status === 'active' ? 'text-green-600' : 'text-red-600'}`}
                    >
                      {t(`${employee.status}`)}
                    </span>
                  }
                />
              </div>
              <div className='flex items-center gap-2 mt-1'>
                <Tag
                  color={getRoleColor(employee.role)}
                  className='font-medium'
                >
                  {t(`${employee.role}`)}
                </Tag>
                <Text className='text-gray-600'>
                  {t(`${employee.department}`)}
                </Text>
              </div>
            </div>
          </div>
        </div>

        <Divider className='my-6' />

        {/* Performance Statistics */}
        <Row gutter={[16, 16]} className='mb-6'>
          <Col xs={24} sm={6}>
            <Card
              className={`text-center !border ${
                isDark
                  ? '!border-blue-600 !bg-blue-900'
                  : '!border-blue-100 !bg-blue-100'
              }`}
            >
              <Statistic
                title={
                  <span className={isDark ? 'text-white' : ''}>
                    {t('performanceScore')}
                  </span>
                }
                value={employee.performanceScore || 0}
                suffix='%'
                valueStyle={{
                  color: getPerformanceColor(employee.performanceScore || 0),
                  fontSize: '20px',
                }}
              />
              <Progress
                percent={employee.performanceScore || 0}
                size='small'
                strokeColor={getPerformanceColor(
                  employee.performanceScore || 0,
                )}
                showInfo={false}
                className='mt-2'
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card
              className={`text-center !border ${
                isDark
                  ? '!border-green-600 !bg-green-900'
                  : '!border-green-100 !bg-green-100'
              }`}
            >
              <Statistic
                title={
                  <span className={isDark ? 'text-white' : ''}>
                    {t('attendanceRate')}
                  </span>
                }
                value={employee.attendanceRate || 0}
                suffix='%'
                prefix={<CheckCircleOutlined className='text-green-600' />}
                valueStyle={{
                  color: isDark ? 'rgb(123 196 173)' : '#059669',
                  fontSize: '20px',
                }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card
              className={`text-center !border ${
                isDark
                  ? '!border-purple-600 !bg-purple-900'
                  : '!border-purple-300 !bg-purple-100'
              }`}
            >
              <Statistic
                title={
                  <span className={isDark ? 'text-white' : ''}>
                    {t('salary')}
                  </span>
                }
                value={employee.salary}
                prefix='$'
                precision={0}
                valueStyle={{
                  color: isDark ? 'rgb(201 181 237)' : '#7c3aed',
                  fontSize: '20px',
                }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card
              className={`text-center !border ${
                isDark
                  ? '!border-orange-600 !bg-orange-900'
                  : '!border-orange-100 !bg-orange-100'
              }`}
            >
              <Statistic
                title={
                  <span className={isDark ? 'text-white' : 'rgb(214 176 156)'}>
                    {t('experience')}
                  </span>
                }
                value={
                  Math.floor(
                    (new Date().getTime() -
                      new Date(employee.hireDate).getTime()) /
                      (1000 * 60 * 60 * 24 * 365 * 100),
                  ) / 100
                }
                suffix='years'
                prefix={<TrophyOutlined className='text-orange-600' />}
                valueStyle={{ color: '#ea580c', fontSize: '20px' }}
                precision={1}
              />
            </Card>
          </Col>
        </Row>

        {/* Employee Information */}
        <Row gutter={[24, 24]} className='mb-6'>
          <Col xs={24} lg={12}>
            <Card
              title={
                <span className='flex items-center gap-2'>
                  <UserOutlined className='text-blue-600' />
                  {t('personalInfo')}
                </span>
              }
            >
              <Descriptions column={1} size='middle'>
                <Descriptions.Item
                  label={
                    <span className='flex items-center gap-2'>
                      <MailOutlined className='text-gray-500' />
                      {t('email')}
                    </span>
                  }
                >
                  <a
                    href={`mailto:${employee.email}`}
                    className='text-blue-600 hover:text-blue-800'
                  >
                    {employee.email}
                  </a>
                </Descriptions.Item>

                <Descriptions.Item
                  label={
                    <span className='flex items-center gap-2'>
                      <PhoneOutlined className='text-gray-500' />
                      {t('phone')}
                    </span>
                  }
                >
                  <a
                    href={`tel:${employee.phone}`}
                    className='text-blue-600 hover:text-blue-800'
                  >
                    {employee.phone}
                  </a>
                </Descriptions.Item>

                <Descriptions.Item
                  label={
                    <span className='flex items-center gap-2'>
                      <CalendarOutlined className='text-gray-500' />
                      {t('hireDate')}
                    </span>
                  }
                >
                  {formatDate(employee.hireDate)}
                </Descriptions.Item>

                {employee.address && (
                  <Descriptions.Item
                    label={
                      <span className='flex items-center gap-2'>
                        <HomeOutlined className='text-gray-500' />
                        {t('address')}
                      </span>
                    }
                  >
                    {employee.address}
                  </Descriptions.Item>
                )}

                {employee.lastLogin && (
                  <Descriptions.Item
                    label={
                      <span className='flex items-center gap-2'>
                        <ClockCircleOutlined className='text-gray-500' />
                        {t('lastLogin')}
                      </span>
                    }
                  >
                    {formatDate(employee.lastLogin)}
                  </Descriptions.Item>
                )}
              </Descriptions>

              {employee.emergencyContact && (
                <div className='mt-4'>
                  <div className='flex items-center gap-2 mb-2'>
                    <ContactsOutlined className='text-gray-500' />
                    <Text className='font-medium text-gray-700'>
                      {t('emergencyContact')}
                    </Text>
                  </div>
                  <div className='ml-6'>
                    <div>{employee.emergencyContact.name}</div>
                    <div className='text-sm text-gray-400'>
                      {employee.emergencyContact.phone}
                    </div>
                    <div className='text-xs text-gray-400'>
                      {employee.emergencyContact.relationship}
                    </div>
                  </div>
                </div>
              )}
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            {/* Performance Metrics */}
            {employee.performanceMetrics && (
              <Card
                title={
                  <span className='flex items-center gap-2'>
                    <StarOutlined className='text-blue-600' />
                    {t('performanceMetrics')}
                  </span>
                }
                className='mb-4'
              >
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Statistic
                      title={t('tasksCompleted')}
                      value={employee.performanceMetrics.tasksCompleted}
                      valueStyle={{ fontSize: '16px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title={t('customerSatisfaction')}
                      value={employee.performanceMetrics.customerSatisfaction}
                      suffix='%'
                      valueStyle={{ fontSize: '16px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title={t('punctuality')}
                      value={employee.performanceMetrics.punctuality}
                      suffix='%'
                      valueStyle={{ fontSize: '16px' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title={t('teamwork')}
                      value={employee.performanceMetrics.teamwork}
                      suffix='%'
                      valueStyle={{ fontSize: '16px' }}
                    />
                  </Col>
                </Row>

                <Divider />

                <div className='mb-4'>
                  <Text className='font-medium text-gray-700 block mb-2'>
                    {t('goals')}
                  </Text>
                  {employee.performanceMetrics.goals.map((goal, index) => (
                    <div key={index} className='flex items-center gap-2 mb-1'>
                      <CheckCircleOutlined className='text-blue-500 text-xs' />
                      <Text className='text-sm'>{goal}</Text>
                    </div>
                  ))}
                </div>

                <div>
                  <Text className='font-medium text-gray-700 block mb-2'>
                    {t('achievements')}
                  </Text>
                  {employee.performanceMetrics.achievements.map(
                    (achievement, index) => (
                      <div key={index} className='flex items-center gap-2 mb-1'>
                        <TrophyOutlined className='text-yellow-500 text-xs' />
                        <Text className='text-sm'>{achievement}</Text>
                      </div>
                    ),
                  )}
                </div>
              </Card>
            )}
          </Col>
        </Row>

        {/* Schedule */}
        {employee.schedule && (
          <Card
            title={
              <span className='flex items-center gap-2'>
                <ClockCircleOutlined className='text-blue-600' />
                {t('schedule')}
              </span>
            }
            className='mb-6'
          >
            <GalaxyTable
              data={scheduleData}
              columns={scheduleColumns}
              // pagination={false}
              size='small'
            />
          </Card>
        )}

        {/* Permissions */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <UserOutlined className='text-blue-600' />
              {t('permissions')}
            </span>
          }
        >
          <Space wrap>
            {employee.permissions.map((permission, index) => (
              <Tag key={index} color='blue' className='mb-2'>
                {permission.replace('_', ' ').toUpperCase()}
              </Tag>
            ))}
          </Space>
        </Card>
      </div>
    </Modal>
  );
};

export default EmployeeDetailsModal;
