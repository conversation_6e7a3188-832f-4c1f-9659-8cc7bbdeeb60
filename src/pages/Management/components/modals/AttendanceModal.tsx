import {
  ClockCircleOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import {
  Modal,
  Table,
  Tag,
  Button,
  DatePicker,
  Select,
  Space,
  Card,
  Row,
  Col,
  Statistic,
  Calendar,
  Badge,
  Typography,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import { useUserStore } from '@/stores';
import type { Employee, AttendanceRecord } from '@/types';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Title } = Typography;

interface AttendanceModalProps {
  visible: boolean;
  employee: Employee | null;
  onClose: () => void;
}

// Mock attendance data - in real app this would come from API
const generateMockAttendance = (employeeId: string): AttendanceRecord[] => {
  const records: AttendanceRecord[] = [];
  const today = dayjs();

  for (let i = 0; i < 30; i++) {
    const date = today.subtract(i, 'day');
    const isWeekend = date.day() === 0 || date.day() === 6;

    // Skip some weekend days
    if (isWeekend && Math.random() > 0.3) continue;

    const statuses: AttendanceRecord['status'][] = [
      'present',
      'late',
      'absent',
      'sick',
      'vacation',
    ];
    const weights = [0.7, 0.15, 0.05, 0.05, 0.05]; // Probability weights

    let status: AttendanceRecord['status'] = 'present';
    const rand = Math.random();
    let cumulative = 0;

    for (let j = 0; j < statuses.length; j++) {
      cumulative += weights[j];
      if (rand <= cumulative) {
        status = statuses[j];
        break;
      }
    }

    const clockIn =
      status === 'present' || status === 'late'
        ? date
            .hour(9)
            .minute(Math.random() > 0.8 ? 30 : 0)
            .format('YYYY-MM-DD HH:mm:ss')
        : undefined;

    const clockOut = clockIn
      ? date
          .hour(17)
          .minute(Math.random() > 0.8 ? 30 : 0)
          .format('YYYY-MM-DD HH:mm:ss')
      : undefined;

    const totalHours =
      clockIn && clockOut
        ? dayjs(clockOut).diff(dayjs(clockIn), 'hour', true)
        : undefined;

    records.push({
      id: `ATT_${employeeId}_${i}`,
      employeeId,
      date: date.format('YYYY-MM-DD'),
      clockIn,
      clockOut,
      totalHours,
      status,
      notes:
        status === 'sick'
          ? 'Medical leave'
          : status === 'vacation'
            ? 'Planned vacation'
            : status === 'late'
              ? 'Traffic delay'
              : undefined,
    });
  }

  return records.reverse();
};

const AttendanceModal: React.FC<AttendanceModalProps> = (props) => {
  const { visible, employee, onClose } = props || {};
  const { isDark } = useUserStore();
  const { t } = useTranslation('attendanceModal');
  const { isMobile } = useResponsive();
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [viewMode, setViewMode] = useState<'table' | 'calendar'>('table');

  // Responsive values
  const modalWidth = useResponsiveValue({
    xs: '95%',
    sm: '90%',
    md: '900px',
    lg: '1000px',
    xl: '1000px',
    '2xl': '1000px',
  });

  const selectWidth = useResponsiveValue({
    xs: '100%',
    sm: '150px',
    md: '150px',
    lg: '150px',
    xl: '150px',
    '2xl': '150px',
  });

  if (!employee) return null;

  const attendanceRecords = generateMockAttendance(employee.id);

  // Filter records based on date range and status
  const filteredRecords = attendanceRecords.filter((record) => {
    const recordDate = dayjs(record.date);
    const inDateRange =
      recordDate >= dateRange[0] && recordDate <= dateRange[1];
    const matchesStatus = !statusFilter || record.status === statusFilter;
    return inDateRange && matchesStatus;
  });

  // Calculate statistics
  const totalDays = filteredRecords.length;
  const presentDays = filteredRecords.filter(
    (r) => r.status === 'present',
  ).length;
  const lateDays = filteredRecords.filter((r) => r.status === 'late').length;
  const absentDays = filteredRecords.filter(
    (r) => r.status === 'absent',
  ).length;
  // const sickDays = filteredRecords.filter(r => r.status === 'sick').length;
  // const vacationDays = filteredRecords.filter(r => r.status === 'vacation').length;
  const attendanceRate =
    totalDays > 0 ? ((presentDays + lateDays) / totalDays) * 100 : 0;
  const avgHours =
    filteredRecords
      .filter((r) => r.totalHours)
      .reduce((sum, r) => sum + (r.totalHours || 0), 0) /
      filteredRecords.filter((r) => r.totalHours).length || 0;

  const getStatusColor = (status: AttendanceRecord['status']) => {
    switch (status) {
      case 'present':
        return 'green';
      case 'late':
        return 'orange';
      case 'absent':
        return 'red';
      case 'sick':
        return 'purple';
      case 'vacation':
        return 'blue';
      case 'half_day':
        return 'yellow';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: AttendanceRecord['status']) => {
    switch (status) {
      case 'present':
        return <CheckCircleOutlined />;
      case 'late':
        return <WarningOutlined />;
      case 'absent':
        return <CloseCircleOutlined />;
      case 'sick':
      case 'vacation':
      case 'half_day':
        return <ExclamationCircleOutlined />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const columns: ColumnsType<AttendanceRecord> = [
    {
      title: t('date'),
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => (
        <span className={isMobile ? 'text-sm' : ''}>
          {dayjs(date).format(isMobile ? 'MMM DD' : 'MMM DD, YYYY')}
        </span>
      ),
      sorter: (a: AttendanceRecord, b: AttendanceRecord) =>
        dayjs(a.date).unix() - dayjs(b.date).unix(),
      width: isMobile ? 80 : undefined,
    },
    {
      title: t('status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: AttendanceRecord['status']) => (
        <Tag
          color={getStatusColor(status)}
          icon={!isMobile ? getStatusIcon(status) : undefined}
          className={isMobile ? 'text-xs' : ''}
        >
          {isMobile
            ? status.charAt(0).toUpperCase() + status.slice(1)
            : t(`${status}`)}
        </Tag>
      ),
      width: isMobile ? 80 : undefined,
    },
    {
      title: t('clockIn'),
      dataIndex: 'clockIn',
      key: 'clockIn',
      render: (clockIn: string) => (
        <span className={isMobile ? 'text-sm' : ''}>
          {clockIn ? dayjs(clockIn).format('HH:mm') : '-'}
        </span>
      ),
      width: isMobile ? 70 : undefined,
      responsive: isMobile ? ['md'] : undefined,
    },
    {
      title: t('clockOut'),
      dataIndex: 'clockOut',
      key: 'clockOut',
      render: (clockOut: string) => (
        <span className={isMobile ? 'text-sm' : ''}>
          {clockOut ? dayjs(clockOut).format('HH:mm') : '-'}
        </span>
      ),
      width: isMobile ? 70 : undefined,
      responsive: isMobile ? ['md'] : undefined,
    },
    {
      title: t('totalHours'),
      dataIndex: 'totalHours',
      key: 'totalHours',
      render: (hours: number) => (
        <span className={isMobile ? 'text-sm' : ''}>
          {hours ? `${hours.toFixed(1)}h` : '-'}
        </span>
      ),
      width: isMobile ? 70 : undefined,
    },
    {
      title: t('notes'),
      dataIndex: 'notes',
      key: 'notes',
      render: (notes: string) => (
        <span className={isMobile ? 'text-xs' : ''}>{notes || '-'}</span>
      ),
      responsive: isMobile ? ['lg'] : undefined,
    },
  ];

  const dateCellRender = (value: dayjs.Dayjs) => {
    const dateStr = value.format('YYYY-MM-DD');
    const record = attendanceRecords.find((r) => r.date === dateStr);

    if (!record) return null;

    return (
      <div className='text-center'>
        <Badge
          status={getStatusColor(record.status) as any}
          text={t(`${record.status}`)}
        />
      </div>
    );
  };

  return (
    <Modal
      title={
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-2'}`}>
          <CalendarOutlined className='text-blue-600' />
          <span className={isMobile ? 'text-sm' : ''}>
            {t('attendanceTracking')} -
            {isMobile ? employee.name.split(' ')[0] : employee.name}
          </span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={modalWidth}
      className={isMobile ? 'top-4' : 'top-8'}
      footer={[
        <Button
          key='close'
          onClick={onClose}
          className={isMobile ? 'w-full' : ''}
          size={isMobile ? 'middle' : 'large'}
        >
          {t('close')}
        </Button>,
      ]}
      styles={{
        body: { padding: isMobile ? '16px' : '24px' },
      }}
    >
      <div className='space-y-6'>
        {/* Controls */}
        <div
          className={`flex ${isMobile ? 'flex-col' : 'flex-col sm:flex-row'} ${isMobile ? 'gap-3' : 'gap-4'} items-start sm:items-center justify-between`}
        >
          <Space
            wrap
            className={isMobile ? 'w-full' : ''}
            direction={isMobile ? 'vertical' : 'horizontal'}
          >
            <RangePicker
              value={dateRange}
              onChange={(dates) =>
                dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])
              }
              format={isMobile ? 'MMM DD' : 'MMM DD, YYYY'}
              size={isMobile ? 'middle' : 'large'}
              className={isMobile ? 'w-full' : ''}
            />
            <Select
              placeholder={t('filterByStatus')}
              allowClear
              value={statusFilter || undefined}
              onChange={setStatusFilter}
              style={{ width: selectWidth }}
              size={isMobile ? 'middle' : 'large'}
            >
              <Option value='present'>{t('present')}</Option>
              <Option value='late'>{t('late')}</Option>
              <Option value='absent'>{t('absent')}</Option>
              <Option value='sick'>{t('sick')}</Option>
              <Option value='vacation'>{t('vacation')}</Option>
            </Select>
          </Space>

          <Space
            className={isMobile ? 'w-full' : ''}
            direction={isMobile ? 'vertical' : 'horizontal'}
          >
            <Button
              type={viewMode === 'table' ? 'primary' : 'default'}
              onClick={() => setViewMode('table')}
              size={isMobile ? 'middle' : 'large'}
              className={isMobile ? 'w-full' : ''}
            >
              {isMobile ? t('table') : t('tableView')}
            </Button>
            <Button
              type={viewMode === 'calendar' ? 'primary' : 'default'}
              onClick={() => setViewMode('calendar')}
              size={isMobile ? 'middle' : 'large'}
              className={isMobile ? 'w-full' : ''}
            >
              {isMobile ? t('calendar') : t('calendarView')}
            </Button>
          </Space>
        </div>

        {/* Statistics */}
        <Row
          gutter={[
            { xs: 8, sm: 12, lg: 16 },
            { xs: 8, sm: 12, lg: 16 },
          ]}
        >
          <Col xs={12} sm={8} lg={4}>
            <Card
              className={`text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} ${isMobile ? 'p-2' : ''}`}
            >
              <Statistic
                title={
                  <span
                    className={`${isMobile ? 'text-xs' : ''} ${isDark ? 'text-white' : ''}`}
                  >
                    {isMobile ? t('rate') : t('attendanceRate')}
                  </span>
                }
                value={attendanceRate}
                precision={1}
                suffix='%'
                valueStyle={{
                  color:
                    attendanceRate >= 95
                      ? '#52c41a'
                      : attendanceRate >= 85
                        ? '#faad14'
                        : '#ff4d4f',
                  fontSize: isMobile ? '16px' : '24px',
                }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} lg={4}>
            <Card
              className={`text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} ${isMobile ? 'p-2' : ''}`}
            >
              <Statistic
                title={
                  <span
                    className={`${isMobile ? 'text-xs' : ''} ${isDark ? 'text-white' : ''}`}
                  >
                    {isMobile ? t('present') : t('presentDays')}
                  </span>
                }
                value={presentDays}
                valueStyle={{
                  color: '#52c41a',
                  fontSize: isMobile ? '16px' : '24px',
                }}
                prefix={!isMobile ? <CheckCircleOutlined /> : undefined}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} lg={4}>
            <Card
              className={`text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} ${isMobile ? 'p-2' : ''}`}
            >
              <Statistic
                title={
                  <span
                    className={`${isMobile ? 'text-xs' : ''} ${isDark ? 'text-white' : ''}`}
                  >
                    {isMobile ? t('late') : t('lateDays')}
                  </span>
                }
                value={lateDays}
                valueStyle={{
                  color: '#faad14',
                  fontSize: isMobile ? '16px' : '24px',
                }}
                prefix={!isMobile ? <WarningOutlined /> : undefined}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} lg={4}>
            <Card
              className={`text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} ${isMobile ? 'p-2' : ''}`}
            >
              <Statistic
                title={
                  <span
                    className={`${isMobile ? 'text-xs' : ''} ${isDark ? 'text-white' : ''}`}
                  >
                    {isMobile ? t('absent') : t('absentDays')}
                  </span>
                }
                value={absentDays}
                valueStyle={{
                  color: '#ff4d4f',
                  fontSize: isMobile ? '16px' : '24px',
                }}
                prefix={!isMobile ? <CloseCircleOutlined /> : undefined}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} lg={4}>
            <Card
              className={`text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} ${isMobile ? 'p-2' : ''}`}
            >
              <Statistic
                title={
                  <span
                    className={`${isMobile ? 'text-xs' : ''} ${isDark ? 'text-white' : ''}`}
                  >
                    {isMobile ? t('avgH') : t('avgHours')}
                  </span>
                }
                value={avgHours}
                precision={1}
                suffix='h'
                valueStyle={{
                  color: '#1890ff',
                  fontSize: isMobile ? '16px' : '24px',
                }}
              />
            </Card>
          </Col>
          <Col xs={12} sm={8} lg={4}>
            <Card
              className={`text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} ${isMobile ? 'p-2' : ''}`}
            >
              <Statistic
                title={
                  <span
                    className={`${isMobile ? 'text-xs' : ''} ${isDark ? 'text-white' : ''}`}
                  >
                    {isMobile ? t('total') : t('totalDays')}
                  </span>
                }
                value={totalDays}
                valueStyle={{
                  color: '#722ed1',
                  fontSize: isMobile ? '16px' : '24px',
                }}
                prefix={!isMobile ? <CalendarOutlined /> : undefined}
              />
            </Card>
          </Col>
        </Row>

        {/* Content */}
        {viewMode === 'table' ? (
          <Table
            dataSource={filteredRecords}
            columns={columns}
            rowKey='id'
            pagination={{
              pageSize: isMobile ? 5 : 10,
              showSizeChanger: !isMobile,
              showQuickJumper: !isMobile,
              showTotal: !isMobile
                ? (total, range) =>
                    `${range[0]}-${range[1]} ${t('of')} ${total} ${t('records')}`
                : undefined,
            }}
            scroll={isMobile ? { x: 600 } : undefined}
            size={isMobile ? 'small' : 'middle'}
          />
        ) : (
          <Calendar
            dateCellRender={dateCellRender}
            headerRender={({ value, onChange }) => (
              <div
                className={`flex ${isMobile ? 'flex-col gap-2' : 'items-center justify-between'} ${isMobile ? 'p-2' : 'p-4'}`}
              >
                <Title
                  level={4}
                  className={`mb-0 ${isMobile ? 'text-base text-center' : ''}`}
                >
                  {value.format(isMobile ? 'MMM YYYY' : 'MMMM YYYY')}
                </Title>
                <Space className={isMobile ? 'w-full justify-center' : ''}>
                  <Button
                    onClick={() => onChange(value.subtract(1, 'month'))}
                    size={isMobile ? 'small' : 'middle'}
                  >
                    {isMobile ? t('prev') : t('previous')}
                  </Button>
                  <Button
                    onClick={() => onChange(dayjs())}
                    size={isMobile ? 'small' : 'middle'}
                  >
                    {t('today')}
                  </Button>
                  <Button
                    onClick={() => onChange(value.add(1, 'month'))}
                    size={isMobile ? 'small' : 'middle'}
                  >
                    {t('next')}
                  </Button>
                </Space>
              </div>
            )}
            className={isMobile ? 'mobile-calendar' : ''}
          />
        )}
      </div>
    </Modal>
  );
};

export default AttendanceModal;
