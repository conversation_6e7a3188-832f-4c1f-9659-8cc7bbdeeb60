import { Button, Flex, Form, Input } from 'antd';
import Title from 'antd/es/typography/Title';
import { useTranslation } from 'react-i18next';
import ModalPageYF from '@/components/ModalYF';
import { useAddStaff, type AddStaffProps } from '@/pages/Management/apis';
import { useNotifyStore } from '@/stores';

interface AddStaffModalProps {
  open: boolean;
  onCancel: () => void;
  refetchStaffs: () => void;
}

const AddStaffModal = (props: AddStaffModalProps) => {
  const { open, onCancel, refetchStaffs } = props || {};

  const { t } = useTranslation('addStaffModal');
  const { pushBSQ } = useNotifyStore();
  const [form] = Form.useForm();
  const resetForm = () => {
    form.resetFields();
  };
  const { mutate: addStaff, isPending: addingStaff } = useAddStaff({
    onSuccess: () => {
      pushBSQ([
        {
          title: import.meta.env.VITE_APP_TITLE || 'YF Pay Admin',
          des: t('addStaffSuccessDesc'),
        },
      ]);
      refetchStaffs();
      handleCancel();
    },
  });

  const handleAddStaff = (values: AddStaffProps) => {
    addStaff({ ...values });
  };
  const handleCancel = () => {
    onCancel();
    resetForm();
  };

  return (
    <ModalPageYF
      open={open}
      onCancel={handleCancel}
      title={<Title level={3}>{t('title')}</Title>}
      footer={null}
    >
      <Form form={form} layout='vertical' onFinish={handleAddStaff}>
        <Form.Item
          name='username'
          label={t('usernameLabel')}
          rules={[{ required: true, message: t('usernameRequired') }]}
        >
          <Input
            size='large'
            placeholder={t('usernamePlaceholder')}
            disabled={addingStaff}
          />
        </Form.Item>
        <Form.Item
          name='password'
          label={t('passwordLabel')}
          rules={[
            { required: true, message: t('passwordRequired') },
            {
              validator: (_, value) => {
                const isValid = /^(?=.*[a-z])(?=.*[A-Z]).{8,}$/.test(value);
                if (!isValid) {
                  return Promise.reject(t('passwordInvalid'));
                }

                return Promise.resolve();
              },
            },
          ]}
        >
          <Input.Password
            size='large'
            placeholder={t('passwordPlaceholder')}
            disabled={addingStaff}
          />
        </Form.Item>
        <Form.Item
          name='confirmPassword'
          label={t('confirmPasswordLabel')}
          rules={[
            { required: true, message: t('confirmPasswordRequired') },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (getFieldValue('password') !== value) {
                  return Promise.reject(new Error(t('passwordNotMatch')));
                }

                return Promise.resolve();
              },
            }),
          ]}
        >
          <Input.Password
            size='large'
            placeholder={t('confirmPasswordPlaceholder')}
            disabled={addingStaff}
          />
        </Form.Item>
        <Form.Item>
          <Flex justify='end' gap={4}>
            <Button
              type='link'
              size='large'
              onClick={handleCancel}
              loading={addingStaff}
            >
              {t('cancel')}
            </Button>
            <Button
              type='primary'
              size='large'
              htmlType='submit'
              loading={addingStaff}
            >
              {t('add')}
            </Button>
          </Flex>
        </Form.Item>
      </Form>
    </ModalPageYF>
  );
};

export default AddStaffModal;
