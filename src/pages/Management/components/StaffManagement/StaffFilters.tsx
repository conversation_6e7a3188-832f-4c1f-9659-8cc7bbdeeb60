import {
  ClearOutlined,
  FilterOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { <PERSON><PERSON>, Card, DatePicker, Divider, Flex, Select } from 'antd';
import Search from 'antd/es/input/Search';
import Title from 'antd/es/typography/Title';
import type { Dayjs } from 'dayjs';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Txt } from '@/components';
import { useUserStore } from '@/stores';
import {
  dateFormator,
  StaffStatusEnums,
  staffStatusEnumsOptions,
} from '@/utils';

interface IStaffFilters {
  filterByUsername: (username: string) => void;
  clearUsernameFilter: () => void;
  filterByStatus: (status: StaffStatusEnums) => void;
  clearStatusFilter: () => void;
  filterByDateRange: (startDate: string, endDate: string) => void;
  clearDateRangeFilter: () => void;
  filterByUpdateDateRange: (startDate: string, endDate: string) => void;
  clearUpdateDateRangeFilter: () => void;
  clearFilters: () => void;
}

const StaffFilters = (props: IStaffFilters) => {
  const {
    filterByUsername,
    clearUsernameFilter,
    filterByStatus,
    clearStatusFilter,
    filterByDateRange,
    clearDateRangeFilter,
    filterByUpdateDateRange,
    clearUpdateDateRangeFilter,
    clearFilters,
  } = props || {};

  const { t } = useTranslation('staffFilters');
  const { t: optionsT } = useTranslation('options');
  const { isDark } = useUserStore();

  const [showMoreFilters, setShowMoreFilters] = useState<boolean>(false);
  const [username, setUsername] = useState<string>('');
  const [status, setStatus] = useState<StaffStatusEnums>();
  const [createdAt, setCreatedAt] = useState<[Dayjs, Dayjs]>();
  const [updatedAt, setUpdatedAt] = useState<[Dayjs, Dayjs]>();

  const handleFilterByUsername = (value: string) => {
    if (value.trim()) {
      filterByUsername(value);
    } else {
      clearUsernameFilter();
    }
  };

  const handleFilterByStatus = (status: StaffStatusEnums) => {
    setStatus(status);
    if (status !== undefined) filterByStatus(status);
    else clearStatusFilter();
  };

  const handleFilterByDateRange = (
    dates: [Dayjs | null, Dayjs | null] | null,
  ) => {
    if (dates && dates.length === 2) {
      const [startDate, endDate] = dates;
      if (startDate && endDate) {
        setCreatedAt([startDate, endDate]);
        filterByDateRange(
          startDate.format(dateFormator.date),
          endDate.format(dateFormator.date),
        );
      }
    } else {
      clearDateRangeFilter();
    }
  };

  const handleFilterByUpdateDateRange = (
    dates: [Dayjs | null, Dayjs | null] | null,
  ) => {
    if (dates && dates.length === 2) {
      const [startDate, endDate] = dates;
      if (startDate && endDate) {
        setUpdatedAt([startDate, endDate]);
        filterByUpdateDateRange(
          startDate.format(dateFormator.date),
          endDate.format(dateFormator.date),
        );
      }
    } else {
      clearUpdateDateRangeFilter();
    }
  };

  const handleClearFilters = () => {
    console.log(isDark);
    clearFilters();

    setUsername('');
    setStatus(undefined);
    setCreatedAt(undefined);
    setUpdatedAt(undefined);
  };

  return (
    <Card>
      <Flex vertical gap={16} className='w-full'>
        <Flex justify='space-between' gap={16} className='flex-wrap'>
          {/* Description */}
          <Flex gap={16}>
            <div
              className={`p-3 h-fit ${isDark ? 'bg-blue-800' : 'bg-blue-100'} rounded-xl`}
            >
              <SearchOutlined className='text-xl' />
            </div>
            <div>
              <Title level={4}>{t('title')}</Title>
              <Txt>{t('description')}</Txt>
            </div>
          </Flex>

          {/* Buttons */}
          <Flex gap={16}>
            <Button
              icon={<ClearOutlined />}
              onClick={handleClearFilters}
              className='bg-red-50 border-red-200 text-red-600 hover:bg-red-100 hover:border-red-300'
              size='large'
            >
              {t('clearAll')}
            </Button>
            <Button
              icon={<FilterOutlined />}
              onClick={() => {
                setShowMoreFilters(!showMoreFilters);
              }}
              type={showMoreFilters ? 'primary' : 'default'}
              size='large'
            >
              {showMoreFilters ? t('hideFilters') : t('moreFilters')}
            </Button>
          </Flex>
        </Flex>

        {/* Default filters */}
        <Flex gap={16} className='flex-col sm:flex-row sm:w-fit w-full'>
          <Flex vertical>
            <span className='font-medium'>{t('usernameLabel')}</span>
            <Search
              allowClear
              size='large'
              placeholder={t('usernamePlaceholder')}
              value={username}
              onChange={(e) => {
                setUsername(e.target.value);
              }}
              onSearch={handleFilterByUsername}
            />
          </Flex>

          <Flex vertical>
            <span className='font-medium'>{t('statusLabel')}</span>
            <Select
              allowClear
              size='large'
              // mode='multiple'
              placeholder={t('statusPlaceholder')}
              value={status}
              onChange={handleFilterByStatus}
            >
              {staffStatusEnumsOptions.map((option) => (
                <Select.Option value={option.value}>
                  {optionsT(option.label)}
                </Select.Option>
              ))}
            </Select>
          </Flex>
        </Flex>

        {/* Advance filters */}
        {showMoreFilters && (
          <Flex vertical>
            <Divider className='!mt-2 !mb-4' />

            <Flex gap={16} className='flex-col sm:flex-row'>
              <Flex vertical className='w-full'>
                <span className='font-medium'>{t('createdAtLabel')}</span>
                <DatePicker.RangePicker
                  size='large'
                  className='w-full'
                  placeholder={[
                    t('createdAtFromPlaceholder'),
                    t('createdAtToPlaceholder'),
                  ]}
                  allowClear
                  showToday
                  value={createdAt}
                  onChange={handleFilterByDateRange}
                />
              </Flex>

              <Flex vertical className='w-full'>
                <span className='font-medium'>{t('updatedAtLabel')}</span>
                <DatePicker.RangePicker
                  size='large'
                  className='w-full'
                  placeholder={[
                    t('updatedAtFromPlaceholder'),
                    t('updatedAtToPlaceholder'),
                  ]}
                  allowClear
                  showToday
                  value={updatedAt}
                  onChange={handleFilterByUpdateDateRange}
                />
              </Flex>
            </Flex>
          </Flex>
        )}
      </Flex>
    </Card>
  );
};

export default StaffFilters;
