import { useApiFilters } from '@/hooks';
import type { StaffStatusEnums } from '@/utils';

const useStaffFilters = () => {
  const apiFilters = useApiFilters();

  const filterByUsername = (username: string) => {
    apiFilters.addFilter({
      key: 'userName',
      operator: 'c',
      value: username,
    });
  };
  const clearUsernameFilter = () => {
    apiFilters.removeFilter('userName');
  };

  const filterByStatus = (status: StaffStatusEnums) => {
    apiFilters.addFilter({
      key: 'status',
      operator: 'eq',
      value: status,
    });
  };
  const clearStatusFilter = () => {
    apiFilters.removeFilter('status');
  };

  const filterByDateRange = (startDate: string, endDate: string) => {
    apiFilters.addFilter({
      key: 'createdAt',
      operator: 'gte',
      value: startDate,
    });
    apiFilters.addFilter({
      key: 'createdAt',
      operator: 'lte',
      value: endDate,
    });
  };
  const clearDateRangeFilter = () => {
    apiFilters.removeFilter('createdAt');
  };

  const filterByUpdateDateRange = (startDate: string, endDate: string) => {
    apiFilters.addFilter({
      key: 'updatedAt',
      operator: 'gte',
      value: startDate,
    });
    apiFilters.addFilter({
      key: 'updatedAt',
      operator: 'lte',
      value: endDate,
    });
  };
  const clearUpdateDateRangeFilter = () => {
    apiFilters.removeFilter('updatedAt');
  };

  return {
    ...apiFilters,
    filterByUsername,
    clearUsernameFilter,
    filterByStatus,
    clearStatusFilter,
    filterByDateRange,
    clearDateRangeFilter,
    filterByUpdateDateRange,
    clearUpdateDateRangeFilter,
  };
};

export default useStaffFilters;
