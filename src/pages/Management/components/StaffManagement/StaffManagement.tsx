import { PlusOutlined } from '@ant-design/icons';
import { <PERSON>ton, Flex } from 'antd';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import AddStaffModal from './AddStaffModal';
import StaffFilters from './StaffFilters';
import useStaffFilters from './useStaffFilters';
import useStaffColumns from './useStaffManagementColumns';
import { GalaxyTable } from '@/components';
import {
  useDeleteStaff,
  useSetStaffStatus,
  useStaffs,
  type StaffsProps,
} from '@/pages/Management/apis';
import type { TabKey } from '@/pages/Management/Management';
import { useNotifyStore } from '@/stores';
import type { StaffStatusEnums } from '@/utils';

const DEFAULT_SORT = ['createdAt:desc'];

interface StaffManagementProps {
  activeTab: TabKey;
}

const StaffManagement = (props: StaffManagementProps) => {
  const { activeTab } = props || {};

  const [openAddStaffModal, setOpenAddStaffModal] = useState(false);
  const [sort, setSort] = useState<Array<string> | undefined>(DEFAULT_SORT);

  const { t } = useTranslation('staffManagement');
  const { pushBSQ } = useNotifyStore();
  const {
    page,
    pageSize,
    updatePage,
    updatePageSize,
    apiParams: filterParams,
    ...filters
  } = useStaffFilters();

  const params: StaffsProps = useMemo(
    () => ({
      sort,
      filterParams,
    }),
    [sort, filterParams],
  );
  const {
    data: staffs,
    isPending: loadingStaffs,
    isError,
    refetch: refetchStaffs,
    isRefetching: refetchingStaffs,
  } = useStaffs({
    enabled: activeTab === 'staffs',
    params,
  });
  const { mutate: setStaffStatus, isPending: togglingStaffStatus } =
    useSetStaffStatus({
      onSuccess: () => {
        refetchStaffs();
      },
    });
  const { mutate: deleteStaff, isPending: deletingStaff } = useDeleteStaff({
    onSuccess: () => {
      pushBSQ([
        {
          title: import.meta.env.VITE_APP_TITLE || 'YF Pay Admin',
          des: t('deleteSuccessDesc'),
        },
      ]);
      refetchStaffs();
    },
  });

  const handleToggleStatus = useCallback(
    (staffId: string, status: StaffStatusEnums) => {
      setStaffStatus({
        staffId,
        status: status ? 0 : 1,
      });
    },
    [setStaffStatus],
  );
  const handleDeleteStaff = useCallback(
    (userId: string) => {
      deleteStaff({ userId });
    },
    [deleteStaff],
  );
  const { columns } = useStaffColumns({
    handleToggleStatus,
    togglingStaffStatus,
    handleDeleteStaff,
    deletingStaff,
  });

  const dataSource = useMemo(() => {
    if (!staffs || isError) return [];

    return staffs.items;
  }, [staffs, isError]);

  const handleOpenAddStaffModal = () => {
    setOpenAddStaffModal(true);
  };
  const handleCloseAddStaffModal = () => {
    setOpenAddStaffModal(false);
  };

  return (
    <>
      <Flex vertical gap={16}>
        <StaffFilters {...filters} />
        <Button
          type='primary'
          size='large'
          icon={<PlusOutlined />}
          onClick={handleOpenAddStaffModal}
          className='self-end'
        >
          {t('addStaff')}
        </Button>

        <GalaxyTable
          columns={columns}
          data={dataSource}
          pagination={{
            current: page,
            pageSize: pageSize,
            total: staffs?.totalItems || 0,
            onChange: (newPage, newPageSize) => {
              updatePage(newPage);
              if (pageSize !== newPageSize) updatePageSize(newPageSize);
            },
          }}
          loading={loadingStaffs || refetchingStaffs}
          onSortChange={(newSort) => {
            setSort(newSort);
          }}
          rowKey='id'
        />
      </Flex>

      <AddStaffModal
        open={openAddStaffModal}
        onCancel={handleCloseAddStaffModal}
        refetchStaffs={refetchStaffs}
      />
    </>
  );
};

export default StaffManagement;
