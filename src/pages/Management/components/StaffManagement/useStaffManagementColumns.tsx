import { DeleteOutlined } from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rm,
  Switch,
  Tooltip,
  type TableColumnType,
} from 'antd';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import Txt from '@/components/Txt';
import type { Staff } from '@/pages/Management/apis';
import { dateFormator, StaffStatusEnums } from '@/utils';

interface UseProps {
  handleToggleStatus: (staffId: string, status: StaffStatusEnums) => void;
  togglingStaffStatus: boolean;
  handleDeleteStaff: (userId: string) => void;
  deletingStaff: boolean;
}

const useStaffColumns = (props: UseProps) => {
  const {
    handleToggleStatus,
    togglingStaffStatus,
    handleDeleteStaff,
    deletingStaff,
  } = props || {};

  const { t } = useTranslation('useStaffManagementColumns');
  const { t: optionsT } = useTranslation('options');

  const columns: Array<TableColumnType<Staff>> = useMemo(
    () => [
      {
        key: 'userName',
        dataIndex: 'userName',
        title: <Txt>{t('userName')}</Txt>,
        sorter: {
          multiple: 1,
        },
        render: (_, { userName }) => {
          return <Txt>{userName}</Txt>;
        },
      },
      {
        key: 'createdByName',
        dataIndex: 'createdByName',
        title: <Txt>{t('createdBy')}</Txt>,
        sorter: {
          multiple: 1,
        },
        render: (_, { createdByName }) => {
          return <Txt>{createdByName}</Txt>;
        },
      },
      {
        key: 'createdAt',
        dataIndex: 'createdAt',
        title: <Txt>{t('createdAt')}</Txt>,
        sorter: {
          multiple: 1,
        },
        render: (_, { createdAt }) => {
          return (
            <Txt>
              {createdAt
                ? dayjs(createdAt).format(dateFormator.accurate)
                : '--'}
            </Txt>
          );
        },
      },
      {
        key: 'updatedAt',
        dataIndex: 'updatedAt',
        title: <Txt>{t('updatedAt')}</Txt>,
        sorter: {
          multiple: 1,
        },
        render: (_, { updatedAt }) => {
          return (
            <Txt>
              {updatedAt
                ? dayjs(updatedAt).format(dateFormator.accurate)
                : '--'}
            </Txt>
          );
        },
      },
      {
        key: 'status',
        dataIndex: 'status',
        title: <Txt>{t('status')}</Txt>,
        sorter: {
          multiple: 1,
        },
        align: 'center',
        render: (_, { id, status }) => {
          return (
            <Tooltip title={t('toggleStatusTooltip')}>
              <Switch
                loading={togglingStaffStatus}
                checked={!!status}
                checkedChildren={optionsT('activeStaffStatusEnums')}
                unCheckedChildren={optionsT('inactiveStaffStatusEnums')}
                onClick={() => {
                  handleToggleStatus(id, status);
                }}
              ></Switch>
            </Tooltip>
          );
        },
      },
      {
        key: 'actions',
        title: <Txt>{t('actions')}</Txt>,
        align: 'center',
        render: (_, { id }) => {
          return (
            <Popconfirm
              title={t('confirmDeleteTitle')}
              cancelText={t('no')}
              okText={t('yes')}
              onConfirm={() => {
                handleDeleteStaff(id);
              }}
            >
              <Button
                type='link'
                size='small'
                icon={<DeleteOutlined />}
                loading={deletingStaff}
                className='!text-red-400'
              >
                {t('delete')}
              </Button>
            </Popconfirm>
          );
        },
      },
    ],
    [
      t,
      optionsT,
      handleToggleStatus,
      togglingStaffStatus,
      handleDeleteStaff,
      deletingStaff,
    ],
  );

  return { columns };
};

export default useStaffColumns;
