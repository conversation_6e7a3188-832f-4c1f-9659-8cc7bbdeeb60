// FE team use later

// import {
//   PlusOutlined,
//   MoreOutlined,
//   UserOutlined,
//   MailOutlined,
//   PhoneOutlined,
//   DollarOutlined,
//   EyeOutlined,
//   EditOutlined,
//   StopOutlined,
//   PlayCircleOutlined,
//   CalendarOutlined,
//   Bar<PERSON>hartOutlined,
//   TeamOutlined,
//   TrophyOutlined,
//   ClockCircleOutlined,
//   CheckCircleOutlined,
//   DownOutlined,
// } from '@ant-design/icons';
// import {
//   Button,
//   Input,
//   Select,
//   Tag,
//   Dropdown,
//   Modal,
//   Form,
//   Row,
//   Col,
//   Card,
//   Progress,
//   Statistic,
//   Collapse,
//   Typography,
// } from 'antd';
// import React, { useState } from 'react';
// import { useTranslation } from 'react-i18next';
// import GalaxyTable from '@/components/GalaxyTable';
// import {
//   useEmployees,
//   useCreateEmployee,
//   useUpdateEmployeeStatus,
//   useUpdateEmployee,
// } from '@/hooks/useManagement.ts';
// import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
// import AttendanceModal from '@/pages/Management/components/modals/AttendanceModal';
// import EditEmployeeModal from '@/pages/Management/components/modals/EditEmployeeModal';
// import EmployeeDetailsModal from '@/pages/Management/components/modals/EmployeeDetailsModal';
// import { useUserStore } from '@/stores';
// import type {
//   Employee,
//   CreateEmployeeRequest,
//   UpdateEmployeeRequest,
// } from '@/types';
// import { formatDate, formatCurrency } from '@/utils/tableUtils.ts';

// const { Search } = Input;
// const { Option } = Select;
// const { Title, Text } = Typography;

// const EmployeesManagement: React.FC = () => {
//   const { t } = useTranslation('employeesManagement');
//   const { isDark } = useUserStore();
//   const { isMobile } = useResponsive();
//   const [searchTerm, setSearchTerm] = useState('');
//   const [roleFilter, setRoleFilter] = useState<string>('');
//   const [departmentFilter, setDepartmentFilter] = useState<string>('');
//   const [statusFilter, setStatusFilter] = useState<string>('');
//   const [showAddForm, setShowAddForm] = useState(false);
//   const [showDetailsModal, setShowDetailsModal] = useState(false);
//   const [showEditModal, setShowEditModal] = useState(false);
//   const [showAttendanceModal, setShowAttendanceModal] = useState(false);
//   const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
//     null,
//   );
//   const [pagination, setPagination] = useState({
//     current: 1,
//     pageSize: 10,
//   });

//   // Responsive values
//   const searchWidth = useResponsiveValue({
//     xs: '100%',
//     sm: '100%',
//     md: '320px',
//     lg: '320px',
//     xl: '320px',
//     '2xl': '320px',
//   });

//   const selectWidth = useResponsiveValue({
//     xs: '100%',
//     sm: '48%',
//     md: '128px',
//     lg: '128px',
//     xl: '128px',
//     '2xl': '128px',
//   });

//   const statisticFontSize = useResponsiveValue({
//     xs: '18px',
//     sm: '20px',
//     md: '24px',
//     lg: '24px',
//     xl: '24px',
//     '2xl': '24px',
//   });

//   const [form] = Form.useForm();
//   const createEmployeeMutation = useCreateEmployee();
//   const updateStatusMutation = useUpdateEmployeeStatus();
//   const updateEmployeeMutation = useUpdateEmployee();

//   const { data: employeesResponse, isLoading } = useEmployees({
//     page: pagination.current,
//     pageSize: pagination.pageSize,
//     search: searchTerm,
//     role: roleFilter || undefined,
//     department: departmentFilter || undefined,
//     status: statusFilter || undefined,
//   });

//   const employees = employeesResponse?.data?.data || [];
//   const total = employeesResponse?.data?.total || 0;

//   // Calculate enhanced statistics
//   const activeEmployees = employees.filter(
//     (emp) => emp.status === 'active',
//   ).length;
//   const inactiveEmployees = employees.filter(
//     (emp) => emp.status === 'inactive',
//   ).length;
//   const suspendedEmployees = employees.filter(
//     (emp) => emp.status === 'suspended',
//   ).length;
//   const avgPerformance =
//     employees.length > 0
//       ? employees.reduce((sum, emp) => sum + (emp.performanceScore || 0), 0) /
//         employees.length
//       : 0;
//   const avgAttendance =
//     employees.length > 0
//       ? employees.reduce((sum, emp) => sum + (emp.attendanceRate || 0), 0) /
//         employees.length
//       : 0;
//   const totalSalary = employees.reduce((sum, emp) => sum + emp.salary, 0);

//   // Department breakdown
//   const departmentCounts = employees.reduce(
//     (acc, emp) => {
//       acc[emp.department] = (acc[emp.department] || 0) + 1;
//       return acc;
//     },
//     {} as Record<string, number>,
//   );

//   // Role breakdown
//   const roleCounts = employees.reduce(
//     (acc, emp) => {
//       acc[emp.role] = (acc[emp.role] || 0) + 1;
//       return acc;
//     },
//     {} as Record<string, number>,
//   );

//   const handleSearch = (value: string) => {
//     setSearchTerm(value);
//     setPagination({ ...pagination, current: 1 });
//   };

//   const handleFilterChange = (type: string, value: string) => {
//     switch (type) {
//       case 'role':
//         setRoleFilter(value);
//         break;
//       case 'department':
//         setDepartmentFilter(value);
//         break;
//       case 'status':
//         setStatusFilter(value);
//         break;
//     }
//     setPagination({ ...pagination, current: 1 });
//   };

//   const handlePaginationChange = (page: number, pageSize: number) => {
//     setPagination({ current: page, pageSize });
//   };

//   const handleAddEmployee = async (values: CreateEmployeeRequest) => {
//     try {
//       await createEmployeeMutation.mutateAsync(values);
//       setShowAddForm(false);
//       form.resetFields();
//     } catch (error) {
//       console.error('Failed to create employee:', error);
//     }
//   };

//   const handleViewEmployee = (employee: Employee) => {
//     setSelectedEmployee(employee);
//     setShowDetailsModal(true);
//   };

//   const handleEditEmployee = (employee: Employee) => {
//     setSelectedEmployee(employee);
//     setShowEditModal(true);
//   };

//   const handleViewAttendance = (employee: Employee) => {
//     setSelectedEmployee(employee);
//     setShowAttendanceModal(true);
//   };

//   const handleUpdateEmployee = async (
//     employeeId: string,
//     data: UpdateEmployeeRequest,
//   ) => {
//     try {
//       await updateEmployeeMutation.mutateAsync({ id: employeeId, data });
//       setShowEditModal(false);
//       setSelectedEmployee(null);
//     } catch (error) {
//       console.error('Failed to update employee:', error);
//     }
//   };

//   // const handleToggleStatus = async (employee: Employee) => {
//   //   const newStatus = employee.status === 'active' ? 'inactive' : 'active';
//   //   try {
//   //     await updateStatusMutation.mutateAsync({ id: employee.id, status: newStatus });
//   //   } catch (error) {
//   //     console.error('Failed to update employee status:', error);
//   //   }
//   // };

//   const handleStatusChange = async (
//     employeeId: string,
//     newStatus: Employee['status'],
//   ) => {
//     try {
//       await updateStatusMutation.mutateAsync({
//         id: employeeId,
//         status: newStatus,
//       });
//     } catch (error) {
//       console.error('Failed to update employee status:', error);
//     }
//   };

//   const getStatusColor = (status: Employee['status']) => {
//     switch (status) {
//       case 'active':
//         return 'green';
//       case 'inactive':
//         return 'red';
//       case 'suspended':
//         return 'orange';
//       default:
//         return 'default';
//     }
//   };

//   const getRoleColor = (role: Employee['role']) => {
//     switch (role) {
//       case 'admin':
//         return 'purple';
//       case 'manager':
//         return 'blue';
//       case 'cashier':
//         return 'green';
//       case 'security':
//         return 'orange';
//       case 'maintenance':
//         return 'gray';
//       default:
//         return 'default';
//     }
//   };

//   const getActionMenuItems = (employee: Employee) => [
//     {
//       key: 'view',
//       label: t('view'),
//       icon: <EyeOutlined />,
//       onClick: () => handleViewEmployee(employee),
//     },
//     {
//       key: 'edit',
//       label: t('edit'),
//       icon: <EditOutlined />,
//       onClick: () => handleEditEmployee(employee),
//     },
//     {
//       key: 'attendance',
//       label: t('attendance'),
//       icon: <CalendarOutlined />,
//       onClick: () => handleViewAttendance(employee),
//     },
//     {
//       type: 'divider' as const,
//     },
//     {
//       key: 'activate',
//       label: t('activate'),
//       disabled: employee.status === 'active',
//       icon: <PlayCircleOutlined />,
//       onClick: () => handleStatusChange(employee.id, 'active'),
//     },
//     {
//       key: 'deactivate',
//       label: t('deactivate'),
//       disabled: employee.status === 'inactive',
//       onClick: () => handleStatusChange(employee.id, 'inactive'),
//       icon: <StopOutlined />,
//     },
//     {
//       key: 'suspend',
//       label: t('suspend'),
//       disabled: employee.status === 'suspended',
//       onClick: () => handleStatusChange(employee.id, 'suspended'),
//       icon: <StopOutlined />,
//     },
//   ];

//   const columns = [
//     {
//       title: t('name'),
//       dataIndex: 'name',
//       key: 'name',
//       render: (name: string, record: Employee) => (
//         <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
//           <div
//             className={`${isMobile ? 'w-8 h-8' : 'w-10 h-10'} bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0`}
//           >
//             <UserOutlined
//               className={`text-white ${isMobile ? 'text-sm' : ''}`}
//             />
//           </div>
//           <div className='min-w-0 flex-1'>
//             <div
//               className={`font-medium  ${isMobile ? 'text-sm' : ''} truncate`}
//             >
//               {name}
//             </div>
//             {!isMobile && (
//               <div
//                 className={`${isDark ? 'text-gray-400' : ''} text-sm truncate`}
//               >
//                 {record.email}
//               </div>
//             )}
//           </div>
//         </div>
//       ),
//       width: isMobile ? 150 : undefined,
//     },
//     {
//       title: t('role'),
//       dataIndex: 'role',
//       key: 'role',
//       render: (role: Employee['role']) => (
//         <Tag
//           color={getRoleColor(role)}
//           className={`font-medium ${isMobile ? 'text-xs' : ''}`}
//         >
//           {t(`${role}`)}
//         </Tag>
//       ),
//       width: isMobile ? 80 : undefined,
//     },
//     {
//       title: t('department'),
//       dataIndex: 'department',
//       key: 'department',
//       render: (department: Employee['department']) => (
//         <span
//           className={`${isDark ? 'text-white' : 'text-gray-700'} ${isMobile ? 'text-sm' : ''}`}
//         >
//           {t(`${department}`)}
//         </span>
//       ),
//       responsive: isMobile ? ['lg'] : undefined,
//     },
//     {
//       title: t('status'),
//       dataIndex: 'status',
//       key: 'status',
//       render: (status: Employee['status']) => (
//         <Tag
//           color={getStatusColor(status)}
//           className={`font-medium ${isMobile ? 'text-xs' : ''}`}
//         >
//           {t(`${status}`)}
//         </Tag>
//       ),
//       width: isMobile ? 80 : undefined,
//     },
//     {
//       title: t('performance'),
//       dataIndex: 'performanceScore',
//       key: 'performanceScore',
//       render: (score: number) => (
//         <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
//           <Progress
//             percent={score}
//             size='small'
//             className={isMobile ? 'w-12' : 'w-16'}
//             strokeColor={
//               score >= 90 ? '#52c41a' : score >= 70 ? '#faad14' : '#ff4d4f'
//             }
//           />
//           <span className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium`}>
//             {score}%
//           </span>
//         </div>
//       ),
//       width: isMobile ? 80 : undefined,
//       responsive: isMobile ? ['md'] : undefined,
//     },
//     {
//       title: t('salary'),
//       dataIndex: 'salary',
//       key: 'salary',
//       render: (salary: number) => (
//         <span
//           className={`font-medium text-green-600 ${isMobile ? 'text-sm' : ''}`}
//         >
//           {formatCurrency(salary)}
//         </span>
//       ),
//       width: isMobile ? 100 : undefined,
//       responsive: isMobile ? ['lg'] : undefined,
//     },
//     {
//       title: t('hireDate'),
//       dataIndex: 'hireDate',
//       key: 'hireDate',
//       render: (date: string) => (
//         <span
//           className={`${isDark ? 'text-white' : 'text-gray-700'} ${isMobile ? 'text-xs' : ''}`}
//         >
//           {formatDate(date)}
//         </span>
//       ),
//       responsive: isMobile ? ['xl'] : undefined,
//     },
//     {
//       title: t('actions'),
//       key: 'actions',
//       width: isMobile ? 50 : undefined,
//       render: (_: any, record: Employee) => (
//         <Dropdown
//           menu={{ items: getActionMenuItems(record) }}
//           trigger={['click']}
//         >
//           <Button
//             type='text'
//             icon={<MoreOutlined />}
//             className='hover:bg-gray-100'
//             size={isMobile ? 'small' : 'middle'}
//           />
//         </Dropdown>
//       ),
//     },
//   ];

//   return (
//     <div className={`space-y-4 ${isMobile ? 'sm:space-y-4' : 'sm:space-y-6'}`}>
//       {/* Controls Section */}
//       <div
//         className={`flex ${isMobile ? 'flex-col' : 'flex-col lg:flex-row'} ${isMobile ? 'gap-3' : 'gap-4'} justify-between`}
//       >
//         <div
//           className={`flex ${isMobile ? 'flex-col' : 'flex-col sm:flex-row'} ${isMobile ? 'gap-3' : 'gap-4'} flex-1`}
//         >
//           <Search
//             placeholder={t('sreachE')}
//             allowClear
//             onSearch={handleSearch}
//             style={{ width: searchWidth }}
//             size={isMobile ? 'middle' : 'large'}
//           />

//           <div className={`flex ${isMobile ? 'flex-col gap-2' : 'gap-2'}`}>
//             <Select
//               placeholder={t('rolePlc')}
//               allowClear
//               value={roleFilter || undefined}
//               onChange={(value) => handleFilterChange('role', value || '')}
//               style={{ width: selectWidth }}
//               size={isMobile ? 'middle' : 'large'}
//             >
//               <Option value='admin'>{t('admin')}</Option>
//               <Option value='manager'>{t('manager')}</Option>
//               <Option value='cashier'>{t('cashier')}</Option>
//               <Option value='security'>{t('security')}</Option>
//               <Option value='maintenance'>{t('maintenance')}</Option>
//             </Select>

//             <Select
//               placeholder='Department'
//               allowClear
//               value={departmentFilter || undefined}
//               onChange={(value) =>
//                 handleFilterChange('department', value || '')
//               }
//               style={{ width: selectWidth }}
//               size={isMobile ? 'middle' : 'large'}
//             >
//               <Option value='management'>{t('management')}</Option>
//               <Option value='gaming'>{t('gaming')}</Option>
//               <Option value='security'>{t('security')}</Option>
//               <Option value='maintenance'>{t('maintenance')}</Option>
//               <Option value='customer_service'>{t('customer_service')}</Option>
//             </Select>

//             <Select
//               placeholder={t('pclStt')}
//               allowClear
//               value={statusFilter || undefined}
//               onChange={(value) => handleFilterChange('status', value || '')}
//               style={{ width: selectWidth }}
//               size={isMobile ? 'middle' : 'large'}
//             >
//               <Option value='active'>{t('active')}</Option>
//               <Option value='inactive'>{t('inactive')}</Option>
//               <Option value='suspended'>{t('suspended')}</Option>
//             </Select>
//           </div>
//         </div>

//         <Button
//           type='primary'
//           icon={<PlusOutlined />}
//           onClick={() => setShowAddForm(true)}
//           size={isMobile ? 'middle' : 'large'}
//           className={`bg-blue-600 hover:bg-blue-700 ${isMobile ? 'w-full' : ''}`}
//         >
//           {t('addEmployee')}
//         </Button>
//       </div>

//       {/* Enhanced Statistics - Collapsible */}
//       <Card
//         styles={{ body: { padding: isMobile ? '0px' : '' } }}
//         className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card`}
//       >
//         <Collapse
//           ghost
//           expandIcon={({ isActive }) => (
//             <DownOutlined
//               rotate={isActive ? 180 : 0}
//               className={`transition-transform duration-200 ${isMobile ? 'text-sm' : ''}`}
//             />
//           )}
//           items={[
//             {
//               key: 'statistics',
//               label: (
//                 <div
//                   className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}
//                 >
//                   <div
//                     className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} bg-blue-100 rounded-lg flex items-center justify-center`}
//                   >
//                     <BarChartOutlined
//                       className={`!text-black ${isMobile ? 'text-sm' : ''}`}
//                     />
//                   </div>
//                   <div>
//                     <Title
//                       level={5}
//                       className={`text-gray-900 mb-0 font-bold ${isMobile ? 'text-sm' : ''}`}
//                     >
//                       {t('emStatis')}
//                     </Title>
//                     <Text
//                       className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}
//                     >
//                       {t('perMetric')}
//                     </Text>
//                   </div>
//                 </div>
//               ),
//               children: (
//                 <div className={`${isMobile ? 'mt-3' : 'mt-4'}`}>
//                   <Row
//                     gutter={[
//                       { xs: 12, sm: 16, lg: 24 },
//                       { xs: 12, sm: 16, lg: 24 },
//                     ]}
//                   >
//                     <Col xs={24} sm={12} lg={6}>
//                       <Card
//                         className={`text-center ${
//                           isDark
//                             ? '!bg-blue-950 !border-blue-800'
//                             : '!border-blue-200 !bg-blue-100'
//                         } ${isMobile ? 'p-2' : ''}`}
//                       >
//                         <Statistic
//                           title={
//                             <span
//                               className={`${isDark ? 'text-white' : 'text-blue-700'} font-medium ${isMobile ? 'text-xs' : 'text-lg'}`}
//                             >
//                               {t('totalEmployees')}
//                             </span>
//                           }
//                           value={total}
//                           prefix={<TeamOutlined className='text-blue-600' />}
//                           valueStyle={{
//                             color: '#1d4ed8',
//                             fontSize: statisticFontSize,
//                             fontWeight: 'bold',
//                           }}
//                         />
//                         <div className='mt-2'>
//                           <Progress
//                             percent={
//                               total > 0
//                                 ? Math.round((activeEmployees / total) * 100)
//                                 : 0
//                             }
//                             size='small'
//                             strokeColor='#1d4ed8'
//                             showInfo={false}
//                           />
//                           <div
//                             className={`${isMobile ? 'text-xs' : 'text-xs'} text-blue-600 mt-1`}
//                           >
//                             {Math.round((activeEmployees / total) * 100)}
//                             {t('perActive')}
//                           </div>
//                         </div>
//                       </Card>
//                     </Col>
//                     <Col xs={24} sm={12} lg={6}>
//                       <Card
//                         className={`text-center ${
//                           isDark
//                             ? '!bg-green-950 !border-green-800'
//                             : '!border-green-200 !bg-green-100'
//                         } ${isMobile ? 'p-2' : ''}`}
//                       >
//                         <Statistic
//                           title={
//                             <span
//                               className={`${isDark ? 'text-white' : 'text-green-700'} font-medium ${isMobile ? 'text-xs' : 'text-lg'}`}
//                             >
//                               {t('avgPerformance')}
//                             </span>
//                           }
//                           value={avgPerformance}
//                           precision={1}
//                           suffix='%'
//                           prefix={<TrophyOutlined className='text-green-600' />}
//                           valueStyle={{
//                             color: '#059669',
//                             fontSize: statisticFontSize,
//                             fontWeight: 'bold',
//                           }}
//                         />
//                         <div
//                           className={`mt-2 ${isMobile ? 'text-xs' : 'text-xs'} text-green-600`}
//                         >
//                           {
//                             employees.filter(
//                               (e) => (e.performanceScore || 0) >= 90,
//                             ).length
//                           }{' '}
//                           {isMobile ? t('hight') : t('highPerformers')}
//                         </div>
//                       </Card>
//                     </Col>
//                     <Col xs={24} sm={12} lg={6}>
//                       <Card
//                         className={`text-center ${
//                           isDark
//                             ? '!bg-purple-950 !border-purple-800'
//                             : '!border-purple-200 !bg-purple-100'
//                         } ${isMobile ? 'p-2' : ''}`}
//                       >
//                         <Statistic
//                           title={
//                             <span
//                               className={`${isDark ? 'text-white' : 'text-purple-700'} font-medium ${isMobile ? 'text-xs' : 'text-lg'}`}
//                             >
//                               {t('avgAttendance')}
//                             </span>
//                           }
//                           value={avgAttendance}
//                           precision={1}
//                           suffix='%'
//                           prefix={
//                             <ClockCircleOutlined className='text-purple-600' />
//                           }
//                           valueStyle={{
//                             color: '#7c3aed',
//                             fontSize: statisticFontSize,
//                             fontWeight: 'bold',
//                           }}
//                         />
//                         <div
//                           className={`mt-2 ${isMobile ? 'text-xs' : 'text-xs'} text-purple-600`}
//                         >
//                           {
//                             employees.filter(
//                               (e) => (e.attendanceRate || 0) >= 95,
//                             ).length
//                           }
//                           {isMobile ? t('excellent') : t('excellentAttendance')}
//                         </div>
//                       </Card>
//                     </Col>
//                     <Col xs={24} sm={12} lg={6}>
//                       <Card
//                         className={`text-center ${
//                           isDark
//                             ? '!bg-orange-900 !border-orange-800'
//                             : '!border-orange-200 !bg-orange-100'
//                         } ${isMobile ? 'p-2' : ''}`}
//                       >
//                         <Statistic
//                           title={
//                             <span
//                               className={`${isDark ? 'text-white' : 'text-orange-700'} font-medium ${isMobile ? 'text-xs' : 'text-lg'}`}
//                             >
//                               {t('totalSalary')}
//                             </span>
//                           }
//                           value={totalSalary}
//                           prefix={
//                             <DollarOutlined className='text-orange-600' />
//                           }
//                           precision={0}
//                           valueStyle={{
//                             color: '#ea580c',
//                             fontSize: statisticFontSize,
//                             fontWeight: 'bold',
//                           }}
//                         />
//                         <div
//                           className={`mt-2 ${isMobile ? 'text-xs' : 'text-xs'} text-orange-600`}
//                         >
//                           {t('avg')}
//                           {formatCurrency(
//                             totalSalary / (employees.length || 1),
//                           )}
//                         </div>
//                       </Card>
//                     </Col>
//                   </Row>

//                   {/* Department & Role Breakdown */}
//                   <Row gutter={[24, 24]}>
//                     <Col xs={24} lg={12}>
//                       <Card
//                         title={
//                           <span className='flex items-center gap-2'>
//                             <BarChartOutlined className='text-blue-600' />
//                             {t('departmentDistribution')}
//                           </span>
//                         }
//                         className='rounded-2xl border-0 shadow-card'
//                       >
//                         <div className='space-y-3'>
//                           {Object.entries(departmentCounts).map(
//                             ([dept, count]) => (
//                               <div
//                                 key={dept}
//                                 className='flex items-center justify-between'
//                               >
//                                 <span
//                                   className={
//                                     isDark ? 'text-white' : 'text-gray-700'
//                                   }
//                                 >
//                                   {t(`${dept}`)}
//                                 </span>
//                                 <div className='flex items-center gap-2'>
//                                   <Progress
//                                     percent={Math.round((count / total) * 100)}
//                                     size='small'
//                                     className='w-20'
//                                     showInfo={false}
//                                   />
//                                   <span className='font-medium text-blue-600 w-8 text-right'>
//                                     {count}
//                                   </span>
//                                 </div>
//                               </div>
//                             ),
//                           )}
//                         </div>
//                       </Card>
//                     </Col>

//                     <Col xs={24} lg={12}>
//                       <Card
//                         title={
//                           <span className='flex items-center gap-2'>
//                             <UserOutlined className='text-blue-600' />
//                             {t('roleDistribution')}
//                           </span>
//                         }
//                         className='rounded-2xl border-0 shadow-card'
//                       >
//                         <div className='space-y-3'>
//                           {Object.entries(roleCounts).map(([role, count]) => (
//                             <div
//                               key={role}
//                               className='flex items-center justify-between'
//                             >
//                               <span
//                                 className={
//                                   isDark ? 'text-white' : 'text-gray-700'
//                                 }
//                               >
//                                 {t(`${role}`)}
//                               </span>
//                               <div className='flex items-center gap-2'>
//                                 <Progress
//                                   percent={Math.round((count / total) * 100)}
//                                   size='small'
//                                   className='w-20'
//                                   showInfo={false}
//                                 />
//                                 <span className='font-medium text-blue-600 w-8 text-right'>
//                                   {count}
//                                 </span>
//                               </div>
//                             </div>
//                           ))}
//                         </div>
//                       </Card>
//                     </Col>
//                   </Row>

//                   {/* Status Breakdown */}
//                   <Row gutter={[24, 24]}>
//                     <Col xs={24}>
//                       <Card
//                         title={
//                           <span className='flex items-center gap-2'>
//                             <CheckCircleOutlined className='text-blue-600' />
//                             {t('statusOverview')}
//                           </span>
//                         }
//                         className='rounded-2xl border-0 shadow-card'
//                       >
//                         <Row gutter={[16, 16]}>
//                           <Col xs={24} sm={8}>
//                             <div
//                               className={`text-center p-4 rounded-lg border ${
//                                 isDark
//                                   ? 'bg-green-900 border-green-700 text-green-100'
//                                   : 'bg-green-50 border-green-200 text-green-700'
//                               }`}
//                             >
//                               <div
//                                 className={`text-2xl font-bold ${isDark ? 'text-green-300' : 'text-green-600'}`}
//                               >
//                                 {activeEmployees}
//                               </div>
//                               <div className='text-sm'>
//                                 {t('activeEmployees')}
//                               </div>
//                               <div
//                                 className={`text-xs mt-1 ${isDark ? 'text-green-200' : 'text-green-600'}`}
//                               >
//                                 {Math.round((activeEmployees / total) * 100)}{' '}
//                                 {t('perTotal')}
//                               </div>
//                             </div>
//                           </Col>

//                           <Col xs={24} sm={8}>
//                             <div
//                               className={`text-center p-4 rounded-lg border ${
//                                 isDark
//                                   ? 'bg-red-900 border-red-700 text-red-100'
//                                   : 'bg-red-50 border-red-200 text-red-700'
//                               }`}
//                             >
//                               <div
//                                 className={`text-2xl font-bold ${isDark ? 'text-red-300' : 'text-red-600'}`}
//                               >
//                                 {inactiveEmployees}
//                               </div>
//                               <div className='text-sm'>
//                                 {t('inactiveEmployees')}
//                               </div>
//                               <div
//                                 className={`text-xs mt-1 ${isDark ? 'text-red-200' : 'text-red-600'}`}
//                               >
//                                 {Math.round((inactiveEmployees / total) * 100)}{' '}
//                                 {t('perTotal')}
//                               </div>
//                             </div>
//                           </Col>

//                           <Col xs={24} sm={8}>
//                             <div
//                               className={`text-center p-4 rounded-lg border ${
//                                 isDark
//                                   ? 'bg-orange-900 border-orange-700 text-orange-100'
//                                   : 'bg-orange-50 border-orange-200 text-orange-700'
//                               }`}
//                             >
//                               <div
//                                 className={`text-2xl font-bold ${isDark ? 'text-orange-300' : 'text-orange-600'}`}
//                               >
//                                 {suspendedEmployees}
//                               </div>
//                               <div className='text-sm'>
//                                 {t('suspendedEmployees')}
//                               </div>
//                               <div
//                                 className={`text-xs mt-1 ${isDark ? 'text-orange-200' : 'text-orange-600'}`}
//                               >
//                                 {Math.round((suspendedEmployees / total) * 100)}
//                                 {t('perTotal')}
//                               </div>
//                             </div>
//                           </Col>
//                         </Row>
//                       </Card>
//                     </Col>
//                   </Row>
//                 </div>
//               ),
//             },
//           ]}
//         />
//       </Card>

//       {/* Employees Table */}
//       <Card
//         className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-sm`}
//       >
//         <GalaxyTable
//           data={employees}
//           columns={columns}
//           loading={isLoading}
//           pagination={{
//             current: pagination.current,
//             pageSize: pagination.pageSize,
//             total,
//             onChange: handlePaginationChange,
//             showSizeChanger: !isMobile,
//             showQuickJumper: !isMobile,
//             showTotal: !isMobile
//               ? (total, range) => `${range[0]}-${range[1]} of ${total} items`
//               : undefined,
//           }}
//           rowKey='id'
//           scroll={isMobile ? { x: 1000 } : undefined}
//         />
//       </Card>

//       {/* Add Employee Modal */}
//       <Modal
//         title={
//           <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-2'}`}>
//             <UserOutlined className='text-blue-600' />
//             <span className={isMobile ? 'text-sm' : ''}>
//               {t('addEmployee')}
//             </span>
//           </div>
//         }
//         open={showAddForm}
//         onCancel={() => {
//           setShowAddForm(false);
//           form.resetFields();
//         }}
//         footer={null}
//         width={isMobile ? '95%' : 600}
//         className={isMobile ? 'top-4' : 'top-8'}
//         styles={{
//           body: { padding: isMobile ? '16px' : '24px' },
//         }}
//       >
//         <Form
//           form={form}
//           layout='vertical'
//           onFinish={handleAddEmployee}
//           className='mt-6'
//         >
//           <Row gutter={isMobile ? 8 : 16}>
//             <Col xs={24} sm={12}>
//               <Form.Item
//                 name='name'
//                 label={t('name')}
//                 rules={[{ required: true, message: t('nameRq') }]}
//               >
//                 <Input
//                   autoComplete='off'
//                   prefix={<UserOutlined />}
//                   placeholder={t('namePlc')}
//                   size={isMobile ? 'middle' : 'large'}
//                 />
//               </Form.Item>
//             </Col>
//             <Col xs={24} sm={12}>
//               <Form.Item
//                 name='email'
//                 label={t('email')}
//                 rules={[
//                   { required: true, message: t('mailRq') },
//                   { type: 'email', message: t('inbMail') },
//                 ]}
//               >
//                 <Input
//                   autoComplete='off'
//                   prefix={<MailOutlined />}
//                   placeholder={t('enterMail')}
//                   size={isMobile ? 'middle' : 'large'}
//                 />
//               </Form.Item>
//             </Col>
//           </Row>

//           <Row gutter={isMobile ? 8 : 16}>
//             <Col xs={24} sm={12}>
//               <Form.Item
//                 name='phone'
//                 label={t('phone')}
//                 rules={[{ required: true, message: t('phoneRq') }]}
//               >
//                 <Input
//                   autoComplete='off'
//                   prefix={<PhoneOutlined />}
//                   placeholder={t('enterPhone')}
//                   size={isMobile ? 'middle' : 'large'}
//                 />
//               </Form.Item>
//             </Col>
//             <Col xs={24} sm={12}>
//               <Form.Item
//                 name='salary'
//                 label={t('salary')}
//                 rules={[{ required: true, message: t('salaryRq') }]}
//               >
//                 <Input
//                   prefix={<DollarOutlined />}
//                   autoComplete='off'
//                   type='number'
//                   placeholder={t('enterSalary')}
//                   size={isMobile ? 'middle' : 'large'}
//                 />
//               </Form.Item>
//             </Col>
//           </Row>

//           <Row gutter={isMobile ? 8 : 16}>
//             <Col xs={24} sm={12}>
//               <Form.Item
//                 name='role'
//                 label={t('role')}
//                 rules={[{ required: true, message: t('roleRq') }]}
//               >
//                 <Select
//                   placeholder={t('roleSelect')}
//                   size={isMobile ? 'middle' : 'large'}
//                 >
//                   <Option value='admin'>{t('admin')}</Option>
//                   <Option value='manager'>{t('manager')}</Option>
//                   <Option value='cashier'>{t('cashier')}</Option>
//                   <Option value='security'>{t('security')}</Option>
//                   <Option value='maintenance'>{t('maintenance')}</Option>
//                 </Select>
//               </Form.Item>
//             </Col>
//             <Col xs={24} sm={12}>
//               <Form.Item
//                 name='department'
//                 label={t('department')}
//                 rules={[{ required: true, message: t('depaRq') }]}
//               >
//                 <Select
//                   placeholder={t('depaSelect')}
//                   size={isMobile ? 'middle' : 'large'}
//                 >
//                   <Option value='management'>{t('management')}</Option>
//                   <Option value='gaming'>{t('gaming')}</Option>
//                   <Option value='security'>{t('security')}</Option>
//                   <Option value='maintenance'>{t('maintenance')}</Option>
//                   <Option value='customer_service'>
//                     {t('customer_service')}
//                   </Option>
//                 </Select>
//               </Form.Item>
//             </Col>
//           </Row>

//           <Form.Item
//             name='permissions'
//             label={t('permissions')}
//             rules={[
//               {
//                 required: true,
//                 message: t('perRq'),
//               },
//             ]}
//           >
//             <Select
//               mode='multiple'
//               placeholder={t('perSelect')}
//               size={isMobile ? 'middle' : 'large'}
//               options={[
//                 { label: t('allPer'), value: 'all' },
//                 { label: t('manageE'), value: 'manage_employees' },
//                 { label: t('viewR'), value: 'view_reports' },
//                 { label: t('manageShift'), value: 'manage_shifts' },
//                 { label: t('operateRes'), value: 'operate_register' },
//                 {
//                   label: t('processTrans'),
//                   value: 'process_transactions',
//                 },
//                 { label: t('minitorPre'), value: 'monitor_premises' },
//                 { label: 'Incident Reports', value: 'incident_reports' },
//                 {
//                   label: t('equiMain'),
//                   value: 'equipment_maintenance',
//                 },
//                 { label: t('facilityR'), value: 'facility_repairs' },
//               ]}
//             />
//           </Form.Item>

//           <div
//             className={`flex ${isMobile ? 'flex-col' : 'justify-end'} gap-3 mt-6`}
//           >
//             <Button
//               onClick={() => {
//                 setShowAddForm(false);
//                 form.resetFields();
//               }}
//               size={isMobile ? 'middle' : 'large'}
//               className={isMobile ? 'w-full' : ''}
//             >
//               {t('cancel')}
//             </Button>
//             <Button
//               type='primary'
//               htmlType='submit'
//               loading={createEmployeeMutation.isPending}
//               size={isMobile ? 'middle' : 'large'}
//               className={`bg-blue-600 hover:bg-blue-700 ${isMobile ? 'w-full' : ''}`}
//             >
//               {t('save')}
//             </Button>
//           </div>
//         </Form>
//       </Modal>

//       {/* Employee Details Modal */}
//       <EmployeeDetailsModal
//         visible={showDetailsModal}
//         employee={selectedEmployee}
//         onClose={() => {
//           setShowDetailsModal(false);
//           setSelectedEmployee(null);
//         }}
//       />

//       {/* Edit Employee Modal */}
//       <EditEmployeeModal
//         visible={showEditModal}
//         employee={selectedEmployee}
//         onClose={() => {
//           setShowEditModal(false);
//           setSelectedEmployee(null);
//         }}
//         onSave={handleUpdateEmployee}
//         loading={updateEmployeeMutation.isPending}
//       />

//       {/* Attendance Modal */}
//       <AttendanceModal
//         visible={showAttendanceModal}
//         employee={selectedEmployee}
//         onClose={() => {
//           setShowAttendanceModal(false);
//           setSelectedEmployee(null);
//         }}
//       />
//     </div>
//   );
// };

// export default EmployeesManagement;
