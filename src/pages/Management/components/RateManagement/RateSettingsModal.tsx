import { CheckOutlined } from '@ant-design/icons';
import {
  Button,
  Flex,
  Form,
  Input,
  Modal,
  Select,
  Switch,
  TimePicker,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import DayPicker from '@/components/DayPicker';
import { useUpdateExchangeRate } from '@/hooks/useManagement';
import type { ExchangeRate } from '@/types';
import { dateFormator, type DayValue } from '@/utils/dateUtils';

const { Text, Title } = Typography;

interface RateSettingsModalProps {
  selectedRate: ExchangeRate | undefined;
  setSelectedRate: React.Dispatch<
    React.SetStateAction<RateSettingsModalProps['selectedRate']>
  >;
}

export default function RateSettingsModal(props: RateSettingsModalProps) {
  const { selectedRate, setSelectedRate } = props || {};

  const [form] = Form.useForm();
  const { t } = useTranslation('rateSettingsModal');
  const { mutate: update, isPending: updating } = useUpdateExchangeRate({
    onSuccess: () => {
      closeModal();
    },
  });
  const autoUpdate = Form.useWatch('autoUpdate', form);
  const updateFrequency = Form.useWatch('updateFrequency', form);

  const closeModal = () => {
    setSelectedRate(undefined);
  };

  const handleCancel = () => {
    closeModal();
  };
  const handleSubmit = (values: {
    autoUpdate: boolean;
    updateFrequency: 'hourly' | 'daily' | 'weekly';
    updateTime: string;
    updateDay: DayValue;
  }) => {
    if (
      selectedRate &&
      selectedRate?.autoUpdate === values.autoUpdate &&
      selectedRate.autoUpdateSettings?.frequency === values.updateFrequency &&
      selectedRate.autoUpdateSettings?.time === values.updateTime &&
      selectedRate.autoUpdateSettings?.day === values.updateDay
    ) {
      closeModal();
      return;
    }

    update({
      exchangeRate: {
        ...selectedRate!,
        autoUpdate: values.autoUpdate,
        autoUpdateSettings: values.autoUpdate
          ? {
              frequency: values.updateFrequency,
              time: values.updateTime,
              day: values.updateDay,
            }
          : undefined,
      },
    });
  };

  useEffect(() => {
    if (selectedRate) {
      form.setFieldsValue({
        autoUpdate: selectedRate.autoUpdate,
        updateFrequency: selectedRate.autoUpdateSettings?.frequency || 'hourly',
        updateTime: selectedRate.autoUpdateSettings?.time
          ? dayjs(selectedRate.autoUpdateSettings?.time, dateFormator.time)
          : dayjs('00:00', dateFormator.time),
        updateDay: selectedRate.autoUpdateSettings?.day || 'mon',
      });
    } else {
      form.resetFields();
    }
  }, [selectedRate, form]);

  return (
    <>
      <Modal
        open={!!selectedRate}
        onCancel={handleCancel}
        footer={null}
        loading={updating}
        title={
          <Flex vertical gap={0}>
            <Title level={3} className='mb-0'>
              {t('settings')}
            </Title>
            <Flex gap={4}>
              <Text type='secondary' strong>
                {t('fiatCode')}:
              </Text>
              <Text>{selectedRate?.fiatCode}</Text>
            </Flex>
          </Flex>
        }
      >
        <Form form={form} onFinish={handleSubmit} layout='vertical'>
          <Form.Item
            name='autoUpdate'
            label={
              <Text className='font-medium text-gray-700'>
                {t('autoUpdate')}
              </Text>
            }
          >
            <Switch unCheckedChildren={t('off')} checkedChildren={t('on')} />
          </Form.Item>
          <Flex justify='space-between'>
            <Form.Item
              name='updateFrequency'
              label={
                <Text className='font-medium text-gray-700'>
                  {t('updateFrequency')}
                </Text>
              }
              rules={[
                {
                  required: autoUpdate,
                  message: t('updateFrequencyError'),
                },
              ]}
            >
              <Select
                disabled={!autoUpdate}
                options={[
                  {
                    value: 'hourly',
                    label: t('hourly'),
                  },
                  {
                    value: 'daily',
                    label: t('daily'),
                  },
                  {
                    value: 'weekly',
                    label: t('weekly'),
                  },
                ]}
              />
            </Form.Item>
            <Form.Item
              name='updateTime'
              label={
                <Text className='font-medium text-gray-700'>
                  {t('updateTime')}
                </Text>
              }
              rules={[
                {
                  required: autoUpdate && updateFrequency !== 'hourly',
                  message: t('updateTimeError'),
                },
              ]}
            >
              <TimePicker
                format={dateFormator.time}
                disabled={updateFrequency === 'hourly' || !autoUpdate}
                allowClear={false}
              />
            </Form.Item>
            <Form.Item
              name='updateDay'
              label={
                <Text className='font-medium text-gray-700'>
                  {t('updateDay')}
                </Text>
              }
              rules={[
                {
                  required: autoUpdate && updateFrequency === 'weekly',
                  message: t('updateDayError'),
                },
              ]}
            >
              <DayPicker
                value={form.getFieldValue('updateDay')}
                disabled={updateFrequency !== 'weekly' || !autoUpdate}
              />
            </Form.Item>
          </Flex>
          <Form.Item
            name='password'
            label={
              <Text className='font-medium text-gray-700'>{t('password')}</Text>
            }
            rules={[
              {
                required: true,
                message: t('passwordError'),
              },
            ]}
          >
            <Input.Password
              placeholder={t('passwordPlaceholder')}
              size='large'
            />
          </Form.Item>
          <Flex justify='end' gap={8}>
            <Button size='large' className='mb-0' onClick={handleCancel}>
              {t('cancel')}
            </Button>
            <Button
              type='primary'
              htmlType='submit'
              size='large'
              icon={<CheckOutlined />}
              className='mb-0'
            >
              {t('confirm')}
            </Button>
          </Flex>
        </Form>
      </Modal>
    </>
  );
}
