// FE use Later

// import { Input } from 'antd';
// import { useState } from 'react';
// import { useTranslation } from 'react-i18next';
// import EditRateModal from './EditRateModal';
// import RateSettingsModal from './RateSettingsModal';
// import useRateManagementColumns from './useRateManagementColumns';
// import GalaxyTable from '@/components/GalaxyTable';
// import { useExchangeRates } from '@/hooks/useManagement';
// import { type ExchangeRate } from '@/types';

// export default function RateManagement() {
//   const [orderBy, setOrderBy] = useState<
//     'spotPrice' | 'askPrice' | 'bidPrice' | undefined
//   >();
//   const [orderByDescending, setOrderByDescending] = useState<boolean>();
//   const [openEditPriceFrom, setOpenEditPriceFrom] = useState<ExchangeRate>();
//   const [openSettingsFrom, setOpenSettingsFrom] = useState<ExchangeRate>();
//   const [search, setSearch] = useState<string>('');

//   const { t } = useTranslation('rateManagement');
//   const { data: exchangeRates, isLoading: loadingExchangeRates } =
//     useExchangeRates({
//       orderBy,
//       orderByDescending,
//       fiatCode: search,
//     });
//   const { columns } = useRateManagementColumns({
//     setOpenEditPriceFrom,
//     setOpenSettingsFrom,
//   });

//   const handleSearch = (value: string) => {
//     setSearch(value);
//   };

//   return (
//     <>
//       <Input.Search
//         placeholder={t('search')}
//         allowClear
//         onSearch={handleSearch}
//         className='mb-4 max-w-80'
//         size='large'
//       />
//       <GalaxyTable
//         data={exchangeRates?.data || []}
//         columns={columns}
//         loading={loadingExchangeRates}
//         rowKey='fiatCode'
//         onChange={(_, _filters, sortResults) => {
//           const firstSortResults = (() => {
//             if (Array.isArray(sortResults)) return sortResults[0];
//             return sortResults;
//           })();

//           const { order, columnKey } = firstSortResults || {};

//           setOrderByDescending(() => {
//             if (order === undefined) return order;
//             return order === 'descend';
//           });
//           setOrderBy(() => {
//             if (columnKey === 'spotPrice') return 'spotPrice';
//             if (columnKey === 'askPrice') return 'askPrice';
//             if (columnKey === 'bidPrice') return 'bidPrice';
//             return undefined;
//           });
//         }}
//       />
//       <EditRateModal
//         selectedRate={openEditPriceFrom}
//         setSelectedRate={setOpenEditPriceFrom}
//       />
//       <RateSettingsModal
//         selectedRate={openSettingsFrom}
//         setSelectedRate={setOpenSettingsFrom}
//       />
//     </>
//   );
// }
