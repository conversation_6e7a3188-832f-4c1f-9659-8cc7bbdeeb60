import { CheckOutlined } from '@ant-design/icons';
import { Button, Flex, Form, Input, Modal, Typography } from 'antd';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useUpdateExchangeRate } from '@/hooks/useManagement';
import type { ExchangeRate } from '@/types';
const { Text, Title } = Typography;

interface EditRateModalProps {
  selectedRate: ExchangeRate | undefined;
  setSelectedRate: React.Dispatch<
    React.SetStateAction<EditRateModalProps['selectedRate']>
  >;
}

export default function EditRateModal(props: EditRateModalProps) {
  const { selectedRate, setSelectedRate } = props || {};

  const [form] = Form.useForm();
  const { t } = useTranslation('editRateModal');
  const { mutate: update, isPending: updating } = useUpdateExchangeRate({
    onSuccess: () => {
      closeModal();
    },
  });

  const closeModal = () => {
    setSelectedRate(undefined);
  };

  const handleCancel = () => {
    closeModal();
  };
  const handleSubmit = (values: {
    spotPrice: string;
    askPrice: string;
    bidPrice: string;
    updateNote: string;
  }) => {
    if (
      selectedRate &&
      Number(values.spotPrice) === selectedRate.spotPrice &&
      Number(values.askPrice) === selectedRate.askPrice &&
      Number(values.bidPrice) === selectedRate.bidPrice &&
      values.updateNote === selectedRate.updateNote
    ) {
      closeModal();
      return;
    }

    update({
      exchangeRate: {
        ...selectedRate!,
        spotPrice: Number(values.spotPrice),
        askPrice: Number(values.askPrice),
        bidPrice: Number(values.bidPrice),
        updateNote: values.updateNote,
      },
    });
  };

  useEffect(() => {
    if (selectedRate) {
      form.setFieldsValue(selectedRate);
    } else {
      form.resetFields();
    }
  }, [selectedRate, form]);

  return (
    <>
      <Modal
        open={!!selectedRate}
        onCancel={handleCancel}
        footer={null}
        loading={updating}
        title={
          <Flex vertical gap={0}>
            <Title level={3} className='mb-0'>
              {t('editPrice')}
            </Title>
            <Flex gap={4}>
              <Text type='secondary' strong>
                {t('fiatCode')}:
              </Text>
              <Text>{selectedRate?.fiatCode}</Text>
            </Flex>
          </Flex>
        }
      >
        <Form
          form={form}
          onFinish={handleSubmit}
          initialValues={{
            spotPrice: selectedRate?.spotPrice,
            askPrice: selectedRate?.askPrice,
            bidPrice: selectedRate?.bidPrice,
            updateNote: selectedRate?.updateNote,
          }}
          layout='vertical'
        >
          <Form.Item
            name='bidPrice'
            label={
              <Text className='font-medium text-gray-700'>{t('bidPrice')}</Text>
            }
            rules={[
              {
                required: true,
                message: t('bidPriceError'),
              },
              {
                pattern: /^[0-9]*\.?[0-9]*$/, // allow digits and optional decimal point
                message: t('bidPriceError'),
              },
            ]}
          >
            <Input placeholder={t('bidPricePlaceholder')} size='large' />
          </Form.Item>
          <Form.Item
            name='spotPrice'
            label={
              <Text className='font-medium text-gray-700'>
                {t('spotPrice')}
              </Text>
            }
            rules={[
              {
                required: true,
                message: t('spotPriceError'),
              },
              {
                pattern: /^[0-9]*\.?[0-9]*$/, // allow digits and optional decimal point
                message: t('spotPriceError'),
              },
            ]}
          >
            <Input placeholder={t('spotPricePlaceholder')} size='large' />
          </Form.Item>
          <Form.Item
            name='askPrice'
            label={
              <Text className='font-medium text-gray-700'>{t('askPrice')}</Text>
            }
            rules={[
              {
                required: true,
                message: t('askPriceError'),
              },
              {
                pattern: /^[0-9]*\.?[0-9]*$/, // allow digits and optional decimal point
                message: t('askPriceError'),
              },
            ]}
          >
            <Input placeholder={t('askPricePlaceholder')} size='large' />
          </Form.Item>
          <Form.Item
            name='updateNote'
            label={
              <Text className='font-medium text-gray-700'>
                {t('updateNote')}
              </Text>
            }
          >
            <Input placeholder={t('updateNotePlaceholder')} size='large' />
          </Form.Item>
          <Form.Item
            name='password'
            label={
              <Text className='font-medium text-gray-700'>{t('password')}</Text>
            }
            rules={[
              {
                required: true,
                message: t('passwordError'),
              },
            ]}
          >
            <Input.Password
              placeholder={t('passwordPlaceholder')}
              size='large'
            />
          </Form.Item>
          <Flex justify='end' gap={8}>
            <Button size='large' className='mb-0' onClick={handleCancel}>
              {t('cancel')}
            </Button>
            <Button
              type='primary'
              htmlType='submit'
              size='large'
              icon={<CheckOutlined />}
              className='mb-0'
            >
              {t('confirm')}
            </Button>
          </Flex>
        </Form>
      </Modal>
    </>
  );
}
