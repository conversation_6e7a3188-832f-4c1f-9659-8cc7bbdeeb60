import { ShopOutlined, TableOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { useTranslation } from 'react-i18next';
import { ModalPageYF } from '@/components';
import { useAddCounter, type AddCounterProps } from '@/pages/Management/apis';

interface IAddCounterModalProps {
  open: boolean;
  onClose: () => void;
}
const AddCounterModal = (props: IAddCounterModalProps) => {
  const { open, onClose } = props || {};
  const [addCounterForm] = Form.useForm();

  const { t } = useTranslation('addCounterModal');

  // mutation
  const { mutate: addCounter, isPending: addingC } = useAddCounter({
    onSuccess: () => {
      addCounterForm.resetFields();
      onClose();
    },
  });
  const handleAddCounter = (values: AddCounterProps) => {
    addCounter({
      counterName: values.counterName,
      location: values.location || '',
      description: values.description || '',
    });
  };

  return (
    <ModalPageYF
      title={
        <div className='flex items-center gap-2'>
          <ShopOutlined className='text-blue-600' />
          {t('title')}
        </div>
      }
      open={open}
      onCancel={onClose}
      footer={null}
      width={800}
      className='top-8'
    >
      <Form
        form={addCounterForm}
        layout='vertical'
        onFinish={handleAddCounter}
        className='mt-6'
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name='counterName'
              label={t('counterNameLabel')}
              rules={[{ required: true, message: t('counterNameRequired') }]}
            >
              <Input
                autoComplete='off'
                prefix={<TableOutlined />}
                placeholder={t('counterNamePlaceholder')}
                size='large'
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name='location'
              label={t('locationLabel')}
              rules={[{ required: true, message: t('locationRequired') }]}
            >
              <Input
                autoComplete='off'
                placeholder={t('locationPlaceholder')}
                size='large'
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item name='description' label={t('descriptionLabel')}>
          <TextArea
            placeholder={t('descriptionPlaceholder')}
            rows={2}
            size='large'
          />
        </Form.Item>

        <div className='flex justify-end gap-3 mt-6'>
          <Button
            onClick={() => {
              onClose();
            }}
            size='large'
          >
            {t('cancel')}
          </Button>
          <Button
            type='primary'
            htmlType='submit'
            size='large'
            className='bg-blue-600 hover:bg-blue-700'
            loading={addingC}
          >
            {t('submit')}
          </Button>
        </div>
      </Form>
    </ModalPageYF>
  );
};

export default AddCounterModal;
