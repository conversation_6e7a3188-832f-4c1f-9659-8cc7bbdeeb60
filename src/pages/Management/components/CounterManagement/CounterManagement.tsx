import { PlusOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  useCounterDetails,
  useCounterList,
  useDelCounter,
  type CounterListProps,
} from '../../apis';
import type { TabKey } from '../../Management';
import AddCounterModal from './AddCounterModal';
import CounterDetailsModal from './CounterDetailsModal';
import CounterFilters from './CounterFilter';
import useCounterColumns from './useCounterColumns';
import useCounterFilter from './useCounterFilters';
import GalaxyTable from '@/components/WeAreTableGalaxy/GalaxyTable';

interface CounterManage {
  activeTab: TabKey;
}
const DEFAULT_SORT = ['createdAt:desc'];

const CounterManagement: React.FC<CounterManage> = (props) => {
  const { activeTab } = props || {};
  const [sort, setSort] = useState<Array<string>>(DEFAULT_SORT);
  const [showAddCounterModal, setShowAddCounterModal] = useState(false);
  const [showCounterDModal, setShowCounterDModal] = useState(false);

  const { t } = useTranslation('counterManagement');
  const {
    page,
    pageSize,
    updatePage,
    updatePageSize,
    apiParams: filterParams,
    ...filters
  } = useCounterFilter();
  const params: CounterListProps = useMemo(
    () => ({
      filterParams,
      sort,
    }),
    [filterParams, sort],
  );
  // Queries
  const {
    data: countList,
    isError: counterErr,
    isLoading: countLoading,
    isRefetching: handlingC,
    refetch: handleC,
  } = useCounterList({
    params,
    enabled: activeTab === 'counters',
  });

  // mutate handle

  const countDt = useMemo(() => {
    if (!countList || counterErr) return [];
    return countList.items;
  }, [countList, counterErr]);

  const { mutate: delCounter, isPending: delPen } = useDelCounter({
    onSuccess: () => handleC({}),
  });
  // mutation
  const {
    mutate: detailGet,
    data: counterDetail,
    isError: detailErr,
    isPending: pendingD,
  } = useCounterDetails({});

  const handleDetailGet = (counterId: string) => {
    detailGet({ counterId });
  };

  const dCounterData = useMemo(() => {
    if (!counterDetail || detailErr) return null;
    return counterDetail;
  }, [counterDetail, detailErr]);
  const handleDelCounter = (counterId: string) => {
    delCounter({ counterId });
  };

  const columns = useCounterColumns({
    delCounter: (counterId) => {
      handleDelCounter(counterId);
    },
    detailCounter: (counterId) => {
      handleDetailGet(counterId);
    },
    onViewD: () => setShowCounterDModal(true),
    pendingD,
    delPen,
  });

  return (
    <>
      <div className='space-y-4'>
        <CounterFilters {...filters} />
        <div className='flex justify-end items-center !my-4'>
          <Button
            type='primary'
            icon={<PlusOutlined />}
            onClick={() => setShowAddCounterModal(true)}
            size='large'
            className='bg-blue-600 hover:bg-blue-700'
          >
            {t('addCounter')}
          </Button>
        </div>

        <GalaxyTable
          data={countDt}
          columns={columns}
          loading={countLoading || handlingC}
          pagination={{
            current: page,
            pageSize: pageSize,
            total: countList?.totalItems || 0,
            onChange: (newPage, newPageSize) => {
              updatePage(newPage);
              if (pageSize !== newPageSize) updatePageSize(newPageSize);
            },
          }}
          onSortChange={(newSort) => {
            if (newSort) setSort(newSort);
          }}
          rowKey='counterId'
        />
      </div>

      <AddCounterModal
        open={showAddCounterModal}
        onClose={() => setShowAddCounterModal(false)}
      />
      {dCounterData && (
        <CounterDetailsModal
          handleC={handleC}
          dCounterData={dCounterData}
          open={showCounterDModal}
          onClose={() => setShowCounterDModal(false)}
        />
      )}
    </>
  );
};

export default CounterManagement;
