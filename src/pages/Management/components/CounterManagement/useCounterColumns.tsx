import { DeleteOutlined, EyeOutlined, MoreOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Popconfirm, type TableColumnType } from 'antd';
import { useTranslation } from 'react-i18next';
import type { CounterOptions } from '../../apis';
import { AlphaStatusTag, Txt } from '@/components';
import DropdownAlpha from '@/components/DropdownYF/DropdownAlpha';
import { useResponsive } from '@/hooks/useResponsive';
import { formatDateTime } from '@/utils/tableUtils';

type CounterRowOptions = CounterOptions;

type UseProps = {
  delCounter: (counterId: string) => void;
  detailCounter: (counterId: string) => void;
  onViewD: () => void;
  pendingD: boolean;
  delPen: boolean;
};

const useCounterColumns = (useProps: UseProps) => {
  const { t } = useTranslation('useCounterColumns');
  const { isMobile } = useResponsive();
  const { delCounter, detailCounter, onViewD, pendingD, delPen } = useProps;

  const columns: TableColumnType<CounterRowOptions>[] = [
    {
      title: <Txt>{t('counterName')}</Txt>,
      dataIndex: 'counterName',
      key: 'name',
      sorter: {
        multiple: 1,
      },
      render: (storeName: string) => (
        <div>
          <div className='font-medium '>{storeName}</div>
        </div>
      ),
    },
    {
      title: <Txt>{t('location')}</Txt>,
      dataIndex: 'location',
      key: 'location',
      sorter: {
        multiple: 1,
      },
      render: (location: string) => (
        <div>
          <div className='font-medium '>{location || 'No Location'}</div>
        </div>
      ),
    },
    {
      title: <Txt>{t('description')}</Txt>,
      dataIndex: 'description',
      key: 'description',
      render: (description: string) => (
        <div>
          <div className='font-medium '>{description || 'No description'}</div>
        </div>
      ),
    },
    {
      title: <Txt>{t('createdAt')}</Txt>,
      dataIndex: 'createdAt',
      key: 'createdAt',
      sorter: {
        multiple: 1,
      },
      render: (_: CounterOptions, record: CounterOptions) => (
        <div>
          <div
            className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 mt-1`}
          >
            {isMobile
              ? `${formatDateTime(record.createdAt).slice(0, 6)} - ${formatDateTime(record.updatedAt).slice(0, 6)}`
              : `${formatDateTime(record.createdAt)} `}
          </div>
        </div>
      ),
      width: isMobile ? 120 : undefined,
    },
    {
      title: <Txt>{t('updatedAt')}</Txt>,
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      sorter: {
        multiple: 1,
      },
      render: (_: CounterOptions, record: CounterOptions) => (
        <div>
          <div
            className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 mt-1`}
          >
            {isMobile
              ? ` ${formatDateTime(record.updatedAt).slice(0, 6)}`
              : `${formatDateTime(record.updatedAt)} `}
          </div>
        </div>
      ),
      width: isMobile ? 120 : undefined,
    },
    {
      title: <Txt>{t('status')}</Txt>,
      dataIndex: 'status',
      key: 'status',
      render: (_: CounterOptions, record: CounterOptions) => (
        <AlphaStatusTag status={record.status} />
      ),
    },
    {
      title: <Txt>{t('actions')}</Txt>,
      key: 'actions',
      render: (_: CounterOptions, record: CounterOptions) => {
        const dropItems = [
          {
            key: 'details',
            item: (
              <Button
                type='text'
                className='w-full text-left'
                icon={<EyeOutlined />}
                onClick={() => {
                  detailCounter(record.counterId);
                  onViewD();
                }}
                loading={pendingD}
              >
                {t('details')}
              </Button>
            ),
          },
          {
            key: 'delete',
            item: (
              <Popconfirm
                title={t('confirmDeleteTitle')}
                onConfirm={() => delCounter(record.counterId)}
                okText={t('yes')}
                cancelText={t('no')}
                okButtonProps={{ loading: delPen }}
              >
                <Button
                  icon={<DeleteOutlined />}
                  danger
                  type='text'
                  className='w-full text-left'
                >
                  {t('delete')}
                </Button>
              </Popconfirm>
            ),
          },
        ];

        return (
          <DropdownAlpha
            buttonProps={{
              type: 'default',
              size: isMobile ? 'small' : 'middle',
            }}
            icon={<MoreOutlined />}
            items={dropItems}
            noUnderLink
            gap={8}
            itemHeight='fit-content'
            pannelMaxHeight={200}
            dontStickDown='left'
          />
        );
      },
      width: isMobile ? 80 : 100,
    },
  ];
  return columns;
};

export default useCounterColumns;
export type { CounterRowOptions };
