import {
  ClearOutlined,
  FilterOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { <PERSON><PERSON>, Card, DatePicker, Divider, Flex, Select } from 'antd';
import Search from 'antd/es/input/Search';
import Title from 'antd/es/typography/Title';
import type { Dayjs } from 'dayjs';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Txt } from '@/components';
import { useUserStore } from '@/stores';
import {
  CounterStatusEnums,
  counterStatusEnumsOptions,
  dateFormator,
} from '@/utils';

interface ICounterFilters {
  filterByCounterName: (counterName: string) => void;
  clearCounterNameFilter: () => void;
  filterByStatus: (status: CounterStatusEnums) => void;
  clearStatusFilter: () => void;
  filterByDateRange: (startDate: string, endDate: string) => void;
  clearDateRangeFilter: () => void;
  filterByUpdateDateRange: (startDate: string, endDate: string) => void;
  clearUpdateDateRangeFilter: () => void;
  clearFilters: () => void;
}

const CounterFilters = (props: ICounterFilters) => {
  const {
    filterByCounterName,
    clearCounterNameFilter,
    filterByStatus,
    clearStatusFilter,
    filterByDateRange,
    clearDateRangeFilter,
    filterByUpdateDateRange,
    clearUpdateDateRangeFilter,
    clearFilters,
  } = props || {};

  // const { t: optionsT } = useTranslation('options');
  const { isDark } = useUserStore();
  const { t } = useTranslation('counterFilter');
  const { t: optionsT } = useTranslation('options');
  const [showMoreFilters, setShowMoreFilters] = useState<boolean>(false);
  const [counterName, setCounterName] = useState<string>('');
  const [status, setStatus] = useState<CounterStatusEnums>();
  const [createdAt, setCreatedAt] = useState<[Dayjs, Dayjs]>();
  const [updatedAt, setUpdatedAt] = useState<[Dayjs, Dayjs]>();

  const handleFilterByCounterName = (value: string) => {
    if (value.trim()) {
      filterByCounterName(value);
    } else {
      clearCounterNameFilter();
    }
  };

  const handleFilterByStatus = (status: CounterStatusEnums) => {
    setStatus(status);
    if (status !== undefined) filterByStatus(status);
    else clearStatusFilter();
  };

  const handleFilterByDateRange = (
    dates: [Dayjs | null, Dayjs | null] | null,
  ) => {
    if (dates && dates.length === 2) {
      const [startDate, endDate] = dates;
      if (startDate && endDate) {
        setCreatedAt([startDate, endDate]);
        filterByDateRange(
          startDate.format(dateFormator.date),
          endDate.format(dateFormator.date),
        );
      }
    } else {
      clearDateRangeFilter();
    }
  };

  const handleFilterByUpdateDateRange = (
    dates: [Dayjs | null, Dayjs | null] | null,
  ) => {
    if (dates && dates.length === 2) {
      const [startDate, endDate] = dates;
      if (startDate && endDate) {
        setUpdatedAt([startDate, endDate]);
        filterByUpdateDateRange(
          startDate.format(dateFormator.date),
          endDate.format(dateFormator.date),
        );
      }
    } else {
      clearUpdateDateRangeFilter();
    }
  };

  const handleClearFilters = () => {
    clearFilters();

    setCounterName('');
    // setStatus(undefined);
    setCreatedAt(undefined);
    setUpdatedAt(undefined);
  };

  return (
    <Card>
      <Flex vertical gap={16} className='w-full'>
        <Flex justify='space-between' gap={16} className='flex-wrap'>
          {/* Description */}
          <Flex gap={16}>
            <div
              className={`p-3 h-fit ${isDark ? 'bg-blue-800' : 'bg-blue-100'} rounded-xl`}
            >
              <SearchOutlined className='text-xl' />
            </div>
            <div>
              <Title level={4}>{t('title')}</Title>
              <Txt>{t('description')}</Txt>
            </div>
          </Flex>

          {/* Buttons */}
          <Flex gap={16}>
            <Button
              icon={<ClearOutlined />}
              onClick={handleClearFilters}
              className='bg-red-50 border-red-200 text-red-600 hover:bg-red-100 hover:border-red-300'
              size='large'
            >
              {t('clearAll')}
            </Button>
            <Button
              icon={<FilterOutlined />}
              onClick={() => {
                setShowMoreFilters(!showMoreFilters);
              }}
              type={showMoreFilters ? 'primary' : 'default'}
              size='large'
            >
              {showMoreFilters ? t('hideFilters') : t('moreFilters')}
            </Button>
          </Flex>
        </Flex>

        {/* Default filters */}
        <Flex gap={16} className='flex-col sm:flex-row sm:w-fit w-full'>
          <Flex vertical>
            <span className='font-medium mb-2'>Counter Name</span>
            <Search
              allowClear
              size='large'
              placeholder='Enter and Find By Counter Name'
              value={counterName}
              onChange={(e) => {
                setCounterName(e.target.value);
              }}
              onSearch={handleFilterByCounterName}
            />
          </Flex>

          <Flex vertical>
            <span className='font-medium mb-2'>Status</span>
            <Select
              allowClear
              size='large'
              // mode='multiple'
              placeholder='Select Status...'
              value={status}
              onChange={handleFilterByStatus}
            >
              {counterStatusEnumsOptions.map((option) => (
                <Select.Option value={option.value}>
                  {optionsT(option.label)}
                </Select.Option>
              ))}
            </Select>
          </Flex>
        </Flex>

        {/* Advance filters */}
        {showMoreFilters && (
          <Flex vertical>
            <Divider className='!mt-2 !mb-4' />

            <Flex gap={16} className='flex-col sm:flex-row'>
              <Flex vertical className='w-full'>
                <span className='font-medium mb-2'>{t('creationDate')}</span>
                <DatePicker.RangePicker
                  size='large'
                  className='w-full'
                  placeholder={[t('startDate'), t('endDate')]}
                  allowClear
                  showToday
                  value={createdAt}
                  onChange={handleFilterByDateRange}
                />
              </Flex>

              <Flex vertical className='w-full'>
                <span className='font-medium mb-2'>{t('lastUpdated')}</span>
                <DatePicker.RangePicker
                  size='large'
                  className='w-full'
                  placeholder={[t('startDate'), t('endDate')]}
                  allowClear
                  showToday
                  value={updatedAt}
                  onChange={handleFilterByUpdateDateRange}
                />
              </Flex>
            </Flex>
          </Flex>
        )}
      </Flex>
    </Card>
  );
};

export default CounterFilters;
