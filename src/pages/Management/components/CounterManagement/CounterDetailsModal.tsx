import {
  CalendarOutlined,
  EnvironmentOutlined,
  FileTextOutlined,
  StockOutlined,
  TableOutlined,
  TagOutlined,
  ToolOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Descriptions,
  Form,
  Input,
  Row,
  Select,
  Typography,
} from 'antd';
import TextArea from 'antd/es/input/TextArea';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ModalPageYF } from '@/components';
import { useResponsive } from '@/hooks/useResponsive';
import {
  useEditCounterDetails,
  type CounterOptions,
  type EditCounterDetailsProps,
} from '@/pages/Management/apis';
import { useNotifyStore } from '@/stores';
import { counterStatusEnumsOptions } from '@/utils';
import { formatDateTime } from '@/utils/tableUtils';

interface ICounterDetailsModalProps {
  open: boolean;
  onClose: () => void;
  dCounterData: CounterOptions;
  handleC: () => void;
}

const { Option } = Select;

const CounterDetailsModal: React.FC<ICounterDetailsModalProps> = (props) => {
  const { open, onClose, dCounterData, handleC } = props || {};
  const { pushBEQ, pushBSQ } = useNotifyStore();
  const { t } = useTranslation('counterDetailsModal');
  const { t: optionsT } = useTranslation('options');
  const { isMobile } = useResponsive();
  const { Paragraph } = Typography;
  const [editingD, setEditingD] = useState(false);
  const [dEditCounterForm] = Form.useForm();
  // form to handle reset
  const resetClose = () => dEditCounterForm.resetFields();

  // mutation
  const { mutate: editCounter, isPending: dEditing } = useEditCounterDetails({
    onSuccess: () => {
      resetClose();
      onClose();
      pushBSQ([
        {
          title: import.meta.env.VITE_APP_TITLE || 'YF Page Admin',
          des: `${t('counterWithId')} '${dCounterData.counterId}' ${t('editedSuccessfully')}`,
        },
      ]);
      handleC();
    },
  });

  const needEdit = () => {
    setEditingD(true);
    dEditCounterForm.setFieldsValue({
      counterName: dCounterData?.counterName,
      location: dCounterData?.location,
      description: dCounterData?.description,
      status: dCounterData?.status,
    });
  };
  const handleEditD = (values: EditCounterDetailsProps) => {
    const edited =
      values.counterName !== dCounterData.counterName ||
      values.location !== dCounterData.location ||
      values.description !== dCounterData.description ||
      values.status !== dCounterData.status;
    if (!edited) {
      resetClose();
      onClose();
      pushBEQ([
        {
          title: import.meta.env.VITE_APP_TITLE || 'YF Page Admin',
          des: t('noChangeDescription'),
        },
      ]);
      return;
    }
    editCounter({
      counterId: dCounterData.counterId,
      counterName: values.counterName || '',
      location: values.location || '',
      description: values.description || '',
      status: values.status || 0,
    });
  };

  return (
    <ModalPageYF
      width={800}
      title={
        <div className='flex items-center gap-2 text-lg font-semibold mb-3'>
          <StockOutlined className='!text-blue-600' />
          {t('counterManagement')}
        </div>
      }
      open={open}
      onCancel={() => {
        resetClose();
        onClose();
      }}
      footer={null}
    >
      {/* Counter Details*/}
      <Card
        style={{ marginBottom: '16px' }}
        title={
          <div className='flex items-center text-base gap-1'>
            <FileTextOutlined className='!text-blue-600' />
            {t('counterDetails')}
          </div>
        }
      >
        <Descriptions
          colon={false}
          size={isMobile ? 'small' : 'middle'}
          column={isMobile ? 1 : 2}
          labelStyle={{ fontWeight: 500, color: '#555' }}
        >
          <Descriptions.Item
            label={
              <div className='!text-gray-400'>
                <UserOutlined className=' !mr-1' />
                {t('counterName')}:
              </div>
            }
          >
            <div className='font-semibold text-base'>
              {dCounterData?.counterName}
            </div>
          </Descriptions.Item>

          <Descriptions.Item
            label={
              <div className='!text-gray-400'>
                <EnvironmentOutlined className='!mr-1' />
                {t('location')}:
              </div>
            }
            className='flex items-center'
          >
            <span> {dCounterData?.location || t('noLocation')} </span>
          </Descriptions.Item>

          <Descriptions.Item
            label={
              <div className='!text-gray-400'>
                <CalendarOutlined className='!mr-1' />
                {t('createTime')}:
              </div>
            }
          >
            <span>{formatDateTime(dCounterData?.createdAt as string)}</span>
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <div className='!text-gray-400'>
                <CalendarOutlined className='!mr-1' />
                <span>{t('updateTime')}:</span>
              </div>
            }
          >
            <span>{formatDateTime(dCounterData?.updatedAt as string)}</span>
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <div className='!text-gray-400'>
                <TagOutlined className='!mr-1' />
                {t('paymentStatus')}:
              </div>
            }
          >
            <span className='text-yellow-500'> {t('unavailableNow')}:</span>
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <div className='!text-gray-400'>
                <ToolOutlined className='!mr-1' />
                {t('actions')}:
              </div>
            }
            className='!text-gray-300 !mr-1 action-custom'
          >
            <Button type='text' className='!text-purple-500' onClick={needEdit}>
              {t('edit')}
            </Button>
          </Descriptions.Item>
        </Descriptions>

        {/* Notes */}
        <div className='mt-4'>
          <div className='flex items-center gap-2 mb-2 !text-gray-400 font-medium'>
            <FileTextOutlined className='!text-gray-300' />
            {t('notes')}
          </div>
          <Paragraph className='text-gray-600 ml-6 mb-0'>
            {dCounterData?.description || t('noDescription')}
          </Paragraph>
        </div>
      </Card>

      {editingD && (
        <Card>
          <Form
            form={dEditCounterForm}
            onFinish={handleEditD}
            layout='vertical'
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name='counterName'
                  label={t('counterNameLabel')}
                  rules={[
                    { required: true, message: t('counterNameRequired') },
                  ]}
                >
                  <Input
                    autoComplete='off'
                    prefix={<TableOutlined />}
                    placeholder={t('counterNamePlaceholder')}
                    size='large'
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name='location'
                  label={t('locationLabel')}
                  rules={[{ required: true, message: t('locationRequired') }]}
                >
                  <Input
                    autoComplete='off'
                    placeholder={t('locationPlaceholder')}
                    size='large'
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name='status'
                  label={t('statusLabel')}
                  rules={[{ required: true, message: t('statusRequired') }]}
                >
                  <Select placeholder={t('statusPlaceholder')} size='large'>
                    {counterStatusEnumsOptions.map((option) => (
                      <Option value={option.value}>
                        {optionsT(option.label)}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name='description' label={t('descriptionLabel')}>
                  <TextArea
                    placeholder={t('descriptionPlaceholder')}
                    rows={2}
                    size='large'
                  />
                </Form.Item>
              </Col>
            </Row>

            <Button
              type='primary'
              htmlType='submit'
              size='large'
              className='bg-blue-600 hover:bg-blue-700 w-full'
              loading={dEditing}
            >
              {t('submit')}
            </Button>
          </Form>
        </Card>
      )}
      <div className='flex justify-end gap-3'>
        <Button
          className='mt-2'
          onClick={() => {
            resetClose();
            onClose();
          }}
          size='large'
        >
          {t('cancel')}
        </Button>
      </div>
    </ModalPageYF>
  );
};

export default CounterDetailsModal;
