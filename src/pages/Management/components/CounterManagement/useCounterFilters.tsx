import { useApiFilters } from '@/hooks';
import type { CounterStatusEnums } from '@/utils';

const useCounterFilter = () => {
  const apiFilters = useApiFilters();

  const filterByCounterName = (counterName: string) => {
    apiFilters.addFilter({
      key: 'counterName',
      operator: 'c',
      value: counterName,
    });
  };
  const clearCounterNameFilter = () => {
    apiFilters.removeFilter('counterName');
  };

  const filterByStatus = (status: CounterStatusEnums) => {
    apiFilters.addFilter({
      key: 'status',
      operator: 'eq',
      value: status,
    });
  };
  const clearStatusFilter = () => {
    apiFilters.removeFilter('status');
  };

  const filterByDateRange = (startDate: string, endDate: string) => {
    apiFilters.addFilter({
      key: 'createdAt',
      operator: 'gte',
      value: startDate,
    });
    apiFilters.addFilter({
      key: 'createdAt',
      operator: 'lte',
      value: endDate,
    });
  };
  const clearDateRangeFilter = () => {
    apiFilters.removeFilter('createdAt');
  };

  const filterByUpdateDateRange = (startDate: string, endDate: string) => {
    apiFilters.addFilter({
      key: 'updatedAt',
      operator: 'gte',
      value: startDate,
    });
    apiFilters.addFilter({
      key: 'updatedAt',
      operator: 'lte',
      value: endDate,
    });
  };
  const clearUpdateDateRangeFilter = () => {
    apiFilters.removeFilter('updatedAt');
  };

  return {
    ...apiFilters,
    filterByStatus,
    clearStatusFilter,
    filterByDateRange,
    filterByCounterName,
    clearCounterNameFilter,
    clearDateRangeFilter,
    filterByUpdateDateRange,
    clearUpdateDateRangeFilter,
  };
};

export default useCounterFilter;
