import { useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/api/apiClient';
import {
  useMasterMutation,
  type UseMasterMutationProps,
} from '@/hooks/useApiMaster';
import { useNotifyStore } from '@/stores';

type AddCounterRes = unknown;

type AddCounterProps = {
  counterName: string;
  location: string;
  description: string;
};

type Other = unknown;

const useAddCounter = (
  useProps: UseMasterMutationProps<AddCounterRes, AddCounterProps, Other>,
) => {
  const { onSuccess, ...config } = useProps;
  const queryClient = useQueryClient();
  const { pushBSQ } = useNotifyStore();

  const testMutation = useMasterMutation<AddCounterRes, AddCounterProps>({
    ...config,
    mutationFn: (props: AddCounterProps) => {
      const request = apiClient
        .post('/admin/counters', props)
        .then(({ data }) => data);
      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
      queryClient.invalidateQueries({ queryKey: ['counter-list'] });
      pushBSQ([{ title: 'YF Page Admin', des: 'Counter Added succesfully!' }]);
    },
  });
  const { mutate, isPending } = testMutation;
  return { ...testMutation, mutate, isPending };
};

export { useAddCounter };
export type { AddCounterProps };
