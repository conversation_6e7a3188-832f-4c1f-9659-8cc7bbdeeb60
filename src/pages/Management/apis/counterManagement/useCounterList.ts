import qs from 'qs';
import { apiClient } from '@/api/apiClient';
import { DEFAULT_STALE_TIME } from '@/constants';
import { useMasterQuery, type UseMasterQueryProps } from '@/hooks/useApiMaster';
import type { ApiFiltersParams } from '@/types';
import { buildApiQueryString, type CounterStatusEnums } from '@/utils';

type CounterListProps = {
  sort?: Array<string>;
  filterParams?: ApiFiltersParams;
};
type CounterOptions = {
  counterId: string;
  counterName: string;
  status: CounterStatusEnums;
  location: string;
  description: string;
  createdAt: string;
  updatedAt: string;
};
type CounterListRes = {
  totalItems: number;
  totalPages: number;
  pageSize: number;
  pageNumber: number;
  items: Array<CounterOptions>;
};

type Other = unknown;

const useCounterList = (
  useProps: UseMasterQueryProps<Other, CounterListProps, CounterListRes>,
) => {
  const { params, ...config } = useProps;
  const testQuery = useMasterQuery<CounterListRes, CounterListProps>({
    ...config,
    queryKey: ['counter-list', ...Object.values(params || {})],
    qf: () => {
      const queryString = buildApiQueryString(params?.filterParams || {});
      const request = apiClient
        .get(`/admin/counters?${queryString}`, {
          params: { ...params, filterParams: undefined },
          paramsSerializer: (params) => {
            return qs.stringify(
              { ...params, filterParams: undefined },
              {
                arrayFormat: 'repeat',
              },
            );
          },
        })
        .then(({ data }) => data);
      return request;
    },
    refetchOnWindowFocus: true,
    staleTime: DEFAULT_STALE_TIME,
  });
  return { ...testQuery };
};

export { useCounterList };
export type { CounterListProps, CounterListRes, CounterOptions };
