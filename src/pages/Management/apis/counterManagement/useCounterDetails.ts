import type { CounterOptions } from './useCounterList';
import { apiClient } from '@/api/apiClient';
import {
  useMasterMutation,
  type UseMasterMutationProps,
} from '@/hooks/useApiMaster';

type CounterDetailsProps = {
  counterId: string;
};

type CounterDetailsRes = CounterOptions;

type Other = unknown;

const useCounterDetails = (
  useProps: UseMasterMutationProps<
    CounterDetailsRes,
    CounterDetailsProps,
    Other
  >,
) => {
  const { ...config } = useProps;

  const testMutation = useMasterMutation<
    CounterDetailsRes,
    CounterDetailsProps
  >({
    ...config,
    mutationFn: (props) => {
      const { counterId } = props;
      const request = apiClient
        .get(`/admin/counters/${counterId}`)
        .then(({ data }) => data);
      return request;
    },
  });

  const { mutate } = testMutation;

  return { ...testMutation, mutate };
};

export { useCounterDetails };
export type { CounterDetailsProps, CounterDetailsRes };
