import { useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/api/apiClient';
import {
  useMasterMutation,
  type UseMasterMutationProps,
} from '@/hooks/useApiMaster';
import type { CounterStatusEnums } from '@/utils';

type EditCounterDetailsRes = unknown;

type EditCounterDetailsProps = {
  counterId: string;
  status: CounterStatusEnums;
  counterName: string;
  location: string;
  description: string;
};

type Other = unknown;

const useEditCounterDetails = (
  useProps: UseMasterMutationProps<
    EditCounterDetailsRes,
    EditCounterDetailsProps,
    Other
  >,
) => {
  const { onSuccess, ...config } = useProps;
  const queryClient = useQueryClient();

  const testMutation = useMasterMutation<
    EditCounterDetailsRes,
    EditCounterDetailsProps
  >({
    ...config,
    mutationFn: (props: EditCounterDetailsProps) => {
      const { counterId } = props;
      const request = apiClient
        .put(`/admin/counters/${counterId}`, props)
        .then(({ data }) => data);
      return request;
    },
    onSuccess: (res, params) => {
      if (onSuccess) onSuccess(res, params);
      queryClient.invalidateQueries({ queryKey: ['counter-list'] });
    },
  });
  const { mutate, isPending } = testMutation;
  return { ...testMutation, mutate, isPending };
};

export { useEditCounterDetails };
export type { EditCounterDetailsProps };
