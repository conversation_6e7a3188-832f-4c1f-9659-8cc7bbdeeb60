import { useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/api/apiClient';
import {
  useMasterMutation,
  type UseMasterMutationProps,
} from '@/hooks/useApiMaster';
import { useNotifyStore } from '@/stores';

type CounterDelRes = unknown;
type CounterDelProps = {
  counterId: string;
};

const useDelCounter = (
  useProps: UseMasterMutationProps<CounterDelRes, CounterDelProps>,
) => {
  const { ...config } = useProps;
  const { pushBSQ } = useNotifyStore();
  const queryClient = useQueryClient();
  const mutation = useMasterMutation<CounterDelRes, CounterDelProps>({
    ...config,
    mutationFn: (props) => {
      const request = apiClient
        .delete(`/admin/counters/${props.counterId}`)
        .then(({ data }) => data);
      return request;
    },
    onSuccess: () => {
      pushBSQ([{ title: 'YF Admin', des: 'Counter Delete Successfully!' }]);
      queryClient.invalidateQueries({ queryKey: ['counter-list'] });
    },
  });
  return mutation;
};

export { useDelCounter };
export type { CounterDelProps, CounterDelRes };
