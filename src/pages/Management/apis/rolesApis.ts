import {
  mockRoles,
  mockPermissions,
  mockPermissionCategories,
  mockUserRoles,
  mockDelay,
} from '@/api/mockData.ts';
import type {
  Role,
  Permission,
  PermissionCategory,
  UserRole,
  CreateRoleRequest,
  UpdateRoleRequest,
  ApiResponse,
  PaginatedResponse,
} from '@/types';

// Role APIs
export const fetchRoles = async (params?: {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: string;
}): Promise<ApiResponse<PaginatedResponse<Role>>> => {
  await mockDelay();

  const { page = 1, pageSize = 10, search = '', status } = params || {};

  // Filter roles based on parameters
  let filteredRoles = mockRoles;

  if (search) {
    filteredRoles = filteredRoles.filter(
      (role) =>
        role.name.toLowerCase().includes(search.toLowerCase()) ||
        role.description.toLowerCase().includes(search.toLowerCase()),
    );
  }

  if (status) {
    filteredRoles = filteredRoles.filter((role) => role.status === status);
  }

  // Sort by creation date (newest first)
  filteredRoles.sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
  );

  // Paginate results
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedRoles = filteredRoles.slice(startIndex, endIndex);

  return {
    data: {
      data: paginatedRoles,
      total: filteredRoles.length,
      page,
      pageSize,
    },
    success: true,
    message: 'Roles fetched successfully',
  };
};

export const fetchRoleById = async (id: string): Promise<ApiResponse<Role>> => {
  await mockDelay();

  const role = mockRoles.find((r) => r.id === id);

  if (!role) {
    throw new Error('Role not found');
  }

  return {
    data: role,
    success: true,
    message: 'Role fetched successfully',
  };
};

export const createRole = async (
  roleData: CreateRoleRequest,
): Promise<ApiResponse<Role>> => {
  await mockDelay();

  // Check if role name already exists
  const existingRole = mockRoles.find(
    (role) => role.name.toLowerCase() === roleData.name.toLowerCase(),
  );

  if (existingRole) {
    throw new Error('Role name already exists');
  }

  const newRole: Role = {
    id: `role_${Date.now()}`,
    name: roleData.name,
    description: roleData.description,
    status: 'active',
    permissions: roleData.permissions,
    userCount: 0,
    isSystem: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'current_user', // In real app, this would be the current user ID
  };

  mockRoles.push(newRole);

  return {
    data: newRole,
    success: true,
    message: 'Role created successfully',
  };
};

export const updateRole = async (
  id: string,
  updateData: UpdateRoleRequest,
): Promise<ApiResponse<Role>> => {
  await mockDelay();

  const roleIndex = mockRoles.findIndex((role) => role.id === id);

  if (roleIndex === -1) {
    throw new Error('Role not found');
  }

  const role = mockRoles[roleIndex];

  // Check if it's a system role and trying to delete
  if (role.isSystem && updateData.status === 'inactive') {
    throw new Error('Cannot deactivate system roles');
  }

  // Check if role name already exists (if changing name)
  if (updateData.name && updateData.name !== role.name) {
    const existingRole = mockRoles.find(
      (r) =>
        r.name.toLowerCase() === updateData.name?.toLowerCase() && r.id !== id,
    );

    if (existingRole) {
      throw new Error('Role name already exists');
    }
  }

  // Update role data
  mockRoles[roleIndex] = {
    ...role,
    ...updateData,
    updatedAt: new Date().toISOString(),
  };

  return {
    data: mockRoles[roleIndex],
    success: true,
    message: 'Role updated successfully',
  };
};

export const deleteRole = async (id: string): Promise<ApiResponse<void>> => {
  await mockDelay();

  const roleIndex = mockRoles.findIndex((role) => role.id === id);

  if (roleIndex === -1) {
    throw new Error('Role not found');
  }

  const role = mockRoles[roleIndex];

  // Check if it's a system role
  if (role.isSystem) {
    throw new Error('Cannot delete system roles');
  }

  // Check if role has assigned users
  if (role.userCount > 0) {
    throw new Error(
      'Cannot delete role with assigned users. Please reassign users first.',
    );
  }

  mockRoles.splice(roleIndex, 1);

  return {
    data: undefined,
    success: true,
    message: 'Role deleted successfully',
  };
};

export const updateRoleStatus = async (
  id: string,
  status: Role['status'],
): Promise<ApiResponse<Role>> => {
  await mockDelay();

  const roleIndex = mockRoles.findIndex((role) => role.id === id);

  if (roleIndex === -1) {
    throw new Error('Role not found');
  }

  const role = mockRoles[roleIndex];

  // Check if it's a system role and trying to deactivate
  if (role.isSystem && status === 'inactive') {
    throw new Error('Cannot deactivate system roles');
  }

  mockRoles[roleIndex] = {
    ...role,
    status,
    updatedAt: new Date().toISOString(),
  };

  return {
    data: mockRoles[roleIndex],
    success: true,
    message: `Role ${status === 'active' ? 'activated' : 'deactivated'} successfully`,
  };
};

// Permission APIs
export const fetchPermissions = async (): Promise<
  ApiResponse<Permission[]>
> => {
  await mockDelay();

  return {
    data: mockPermissions,
    success: true,
    message: 'Permissions fetched successfully',
  };
};

export const fetchPermissionCategories = async (): Promise<
  ApiResponse<PermissionCategory[]>
> => {
  await mockDelay();

  return {
    data: mockPermissionCategories,
    success: true,
    message: 'Permission categories fetched successfully',
  };
};

// User Role Assignment APIs
export const fetchUserRoles = async (params?: {
  page?: number;
  pageSize?: number;
  search?: string;
  roleId?: string;
}): Promise<ApiResponse<PaginatedResponse<UserRole>>> => {
  await mockDelay();

  const { page = 1, pageSize = 10, search = '', roleId } = params || {};

  // Filter user roles based on parameters
  let filteredUserRoles = mockUserRoles;

  if (search) {
    filteredUserRoles = filteredUserRoles.filter(
      (userRole) =>
        userRole.userName.toLowerCase().includes(search.toLowerCase()) ||
        userRole.userEmail.toLowerCase().includes(search.toLowerCase()) ||
        userRole.roleName.toLowerCase().includes(search.toLowerCase()),
    );
  }

  if (roleId) {
    filteredUserRoles = filteredUserRoles.filter(
      (userRole) => userRole.roleId === roleId,
    );
  }

  // Sort by assignment date (newest first)
  filteredUserRoles.sort(
    (a, b) =>
      new Date(b.assignedAt).getTime() - new Date(a.assignedAt).getTime(),
  );

  // Paginate results
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedUserRoles = filteredUserRoles.slice(startIndex, endIndex);

  return {
    data: {
      data: paginatedUserRoles,
      total: filteredUserRoles.length,
      page,
      pageSize,
    },
    success: true,
    message: 'User roles fetched successfully',
  };
};

export const assignUserRole = async (
  userId: string,
  roleId: string,
): Promise<ApiResponse<UserRole>> => {
  await mockDelay();

  // Check if user already has this role
  const existingAssignment = mockUserRoles.find(
    (ur) => ur.userId === userId && ur.roleId === roleId,
  );

  if (existingAssignment) {
    throw new Error('User already has this role assigned');
  }

  // Find role and user info (in real app, you'd fetch from respective APIs)
  const role = mockRoles.find((r) => r.id === roleId);
  if (!role) {
    throw new Error('Role not found');
  }

  // For demo purposes, we'll use mock user data
  const userInfo = {
    name: `User ${userId}`,
    email: `user${userId}@galaxy.com`,
  };

  const newUserRole: UserRole = {
    id: `ur_${Date.now()}`,
    userId,
    userName: userInfo.name,
    userEmail: userInfo.email,
    roleId,
    roleName: role.name,
    assignedAt: new Date().toISOString(),
    assignedBy: 'current_user', // In real app, this would be the current user ID
  };

  mockUserRoles.push(newUserRole);

  // Update role user count
  const roleIndex = mockRoles.findIndex((r) => r.id === roleId);
  if (roleIndex !== -1) {
    mockRoles[roleIndex].userCount += 1;
  }

  return {
    data: newUserRole,
    success: true,
    message: 'Role assigned to user successfully',
  };
};

export const removeUserRole = async (
  userRoleId: string,
): Promise<ApiResponse<void>> => {
  await mockDelay();

  const userRoleIndex = mockUserRoles.findIndex((ur) => ur.id === userRoleId);

  if (userRoleIndex === -1) {
    throw new Error('User role assignment not found');
  }

  const userRole = mockUserRoles[userRoleIndex];

  // Update role user count
  const roleIndex = mockRoles.findIndex((r) => r.id === userRole.roleId);
  if (roleIndex !== -1) {
    mockRoles[roleIndex].userCount = Math.max(
      0,
      mockRoles[roleIndex].userCount - 1,
    );
  }

  mockUserRoles.splice(userRoleIndex, 1);

  return {
    data: undefined,
    success: true,
    message: 'Role removed from user successfully',
  };
};
