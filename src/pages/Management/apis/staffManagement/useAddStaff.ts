import { apiClient } from '@/api';
import { useMasterMutation, type UseMasterMutationProps } from '@/hooks';

type AddStaffProps = {
  username: string;
  password: string;
};
type AddStaffRes = unknown;

type Other = unknown;

const useAddStaff = (
  useProps: UseMasterMutationProps<AddStaffRes, AddStaffProps, Other>,
) => {
  const { ...config } = useProps;
  const mutation = useMasterMutation<AddStaffRes, AddStaffProps>({
    ...config,
    mutationFn: (props) => {
      const request = apiClient
        .post('/admin/staffs', props)
        .then(({ data }) => data);
      return request;
    },
  });
  return { ...mutation };
};

export { useAddStaff };
export type { AddStaffProps };
