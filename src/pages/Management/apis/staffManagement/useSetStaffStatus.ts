import { apiClient } from '@/api';
import { useMasterMutation, type UseMasterMutationProps } from '@/hooks';
import type { StaffStatusEnums } from '@/utils';

type SetStaffStatusProps = {
  staffId: string;
  status: StaffStatusEnums;
};
type SetStaffStatusRes = unknown;

type Other = unknown;

const useSetStaffStatus = (
  useProps: UseMasterMutationProps<
    SetStaffStatusRes,
    SetStaffStatusProps,
    Other
  >,
) => {
  const { ...config } = useProps;
  const mutation = useMasterMutation<SetStaffStatusRes, SetStaffStatusProps>({
    ...config,
    mutationFn: (props) => {
      const request = apiClient
        .patch(`/admin/staffs/${props.staffId}/status`, props)
        .then(({ data }) => data);
      return request;
    },
  });
  return { ...mutation };
};

export { useSetStaffStatus };
