import { apiClient } from '@/api';
import { useMasterMutation, type UseMasterMutationProps } from '@/hooks';

type DeleteStaffProps = {
  userId: string;
};
type DeleteStaffRes = unknown;

type Other = unknown;

const useDeleteStaff = (
  useProps: UseMasterMutationProps<DeleteStaffRes, DeleteStaffProps, Other>,
) => {
  const { ...config } = useProps;
  const mutation = useMasterMutation<DeleteStaffRes, DeleteStaffProps>({
    ...config,
    mutationFn: (props) => {
      const request = apiClient
        .delete(`/admin/staffs/${props.userId}`)
        .then(({ data }) => data);
      return request;
    },
  });
  return { ...mutation };
};

export { useDeleteStaff };
