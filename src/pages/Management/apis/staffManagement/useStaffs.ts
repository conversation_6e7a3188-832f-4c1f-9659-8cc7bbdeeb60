import qs from 'qs';
import { apiClient } from '@/api';
import { DEFAULT_STALE_TIME, queryKeys } from '@/constants';
import { useMasterQuery, type UseMasterQueryProps } from '@/hooks';
import type { ApiFiltersParams, YFRole } from '@/types';
import { type StaffStatusEnums, buildApiQueryString } from '@/utils';

type StaffsProps = {
  sort?: Array<string>;
  filterParams?: ApiFiltersParams;
};
type Staff = {
  id: string;
  userName: string;
  isNew: boolean;
  status: StaffStatusEnums;
  roles: Array<YFRole>;
  createdById: string;
  createdByName: string;
  createdAt: string;
  updatedAt: string;
};
type StaffsRes = {
  totalItems: number;
  totalPages: number;
  pageSize: number;
  pageNumber: number;
  items: Array<Staff>;
};

type Other = unknown;

const useStaffs = (
  useProps: UseMasterQueryProps<Other, StaffsProps, StaffsRes>,
) => {
  const { params, ...config } = useProps;
  const query = useMasterQuery<StaffsRes, StaffsProps>({
    ...config,
    queryKey: queryKeys.management.staffs(params),
    qf: () => {
      const queryString = buildApiQueryString(params?.filterParams || {});

      const request = apiClient
        .get(`/admin/staffs?${queryString}`, {
          params: { ...params, filterParams: undefined },
          paramsSerializer: (params) => {
            return qs.stringify(
              { ...params, filterParams: undefined },
              {
                arrayFormat: 'repeat',
              },
            );
          },
        })
        .then(({ data }) => data);
      return request;
    },
    refetchOnWindowFocus: true,
    staleTime: DEFAULT_STALE_TIME,
  });
  return { ...query };
};

export { useStaffs };
export type { Staff, StaffsProps };
