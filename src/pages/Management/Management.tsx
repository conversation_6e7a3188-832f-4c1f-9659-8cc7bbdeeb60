import {
  ApartmentOutlined,
  ShopOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { Card, Tabs, Typography } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import StaffManagement from './components/StaffManagement/StaffManagement';
import { Txt } from '@/components';
import { useResponsive } from '@/hooks/useResponsive.ts';
import CounterManagement from '@/pages/Management/components/CounterManagement/CounterManagement';
import { useUserStore } from '@/stores';

const { Title } = Typography;

export type TabKey = 'counters' | 'staffs';

const Management: React.FC = () => {
  const { t } = useTranslation('management');
  const { isMobile } = useResponsive();
  const [activeTab, setActiveTab] = useState<TabKey>('counters');
  const { isDark } = useUserStore();
  // const { data: statsResponse, isLoading: statsLoading } = useManagementStats();

  // const statisticFontSize = useResponsiveValue({
  //   xs: '20px',
  //   sm: '24px',
  //   md: '28px',
  //   lg: '28px',
  //   xl: '28px',
  //   '2xl': '28px',
  // });
  // const stats = statsResponse?.data;

  const tabItems = [
    {
      key: 'counters',
      label: (
        <span className='flex items-center gap-2'>
          <ShopOutlined />
          {t('counters')}
        </span>
      ),
      children: <CounterManagement activeTab={activeTab} />,
    },
    {
      key: 'staffs',
      label: (
        <span className='flex items-center gap-2'>
          <TeamOutlined />
          {t('staffs')}
        </span>
      ),
      children: <StaffManagement activeTab={activeTab} />,
    },
  ];

  return (
    <div className='space-y-4 sm:space-y-4 md:space-y-6 lg:space-y-8'>
      {/* Header Section */}
      <div
        className={`rounded-xl p-4 md:rounded-2xl md:p-6 lg:p-8 bg-gradient-to-r ${
          isDark
            ? 'from-purple-700 via-purple-800 to-purple-900'
            : 'from-purple-100 via-purple-200 to-purple-300'
        }`}
      >
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-3 sm:gap-4'>
            <div className='w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-purple-400 to-indigo-600 rounded-xl flex items-center justify-center flex-shrink-0'>
              <ApartmentOutlined className='!text-white text-lg md:text-xl' />
            </div>
            <div>
              <Title level={2}>{t('title')}</Title>
              <Txt>{t('textMana')}</Txt>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Overview - Collapsible */}
      {/* <Card */}
      {/*   styles={{ body: { padding: isMobile ? '0px' : '' } }} */}
      {/*   className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card`} */}
      {/* > */}
      {/*   <Collapse */}
      {/*     ghost */}
      {/*     expandIcon={({ isActive }) => ( */}
      {/*       <DownOutlined */}
      {/*         rotate={isActive ? 180 : 0} */}
      {/*         className={`transition-transform duration-200 ${isMobile ? 'text-sm' : ''}`} */}
      {/*       /> */}
      {/*     )} */}
      {/*     items={[ */}
      {/*       { */}
      {/*         key: 'overview', */}
      {/*         label: ( */}
      {/*           <div */}
      {/*             className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`} */}
      {/*           > */}
      {/*             <div */}
      {/*               className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} bg-blue-100 rounded-lg flex items-center justify-center`} */}
      {/*             > */}
      {/*               <BarChartOutlined */}
      {/*                 className={`!text-black ${isMobile ? 'text-sm' : ''}`} */}
      {/*               /> */}
      {/*             </div> */}
      {/*             <div> */}
      {/*               <Title */}
      {/*                 level={4} */}
      {/*                 className={`text-gray-900 mb-0 font-bold ${isMobile ? 'text-base' : ''}`} */}
      {/*               > */}
      {/*                 {t('manaOvw')} */}
      {/*               </Title> */}
      {/*               <Text */}
      {/*                 className={`text-gray-600 ${isMobile ? 'text-xs' : ''}`} */}
      {/*               > */}
      {/*                 {t('ovwText')} */}
      {/*               </Text> */}
      {/*             </div> */}
      {/*           </div> */}
      {/*         ), */}
      {/*         children: ( */}
      {/*           <div className={`${isMobile ? 'mt-3' : 'mt-4'}`}> */}
      {/*             <Row */}
      {/*               gutter={[ */}
      {/*                 { xs: 12, sm: 16, lg: 24 }, */}
      {/*                 { xs: 12, sm: 16, lg: 24 }, */}
      {/*               ]} */}
      {/*             > */}
      {/* missing API  */}

      {/* <Col xs={12} sm={12} lg={6}>
                      <Card
                        className={`text-center ${
                          isDark
                            ? '!bg-blue-950 !border-blue-800'
                            : '!border-blue-200 !bg-blue-100'
                        } ${isMobile ? 'p-2' : ''}`}
                      >
                        <Statistic
                          title={
                            <span
                              className={`${
                                isDark ? 'text-white' : 'text-blue-700'
                              } font-medium ${isMobile ? 'text-xs' : 'text-lg'}`}
                            >
                              {t('totalEmployees')}
                            </span>
                          }
                          value={stats?.totalEmployees || 0}
                          loading={statsLoading}
                          prefix={<UserOutlined className='text-blue-600' />}
                          valueStyle={{
                            color: '#1d4ed8',
                            fontSize: statisticFontSize,
                          }}
                        />
                        <div className='mt-2'>
                          <Text
                            className={`${isMobile ? 'text-xs' : 'text-xs'} text-blue-600`}
                          >
                            <span className='font-medium'>
                              {stats?.activeEmployees || 0}
                            </span>
                            {t('active')}
                          </Text>
                        </div>
                      </Card>
                    </Col> */}

      {/* <Col xs={12} sm={12} lg={6}> */}
      {/*   <Card */}
      {/*     className={`text-center ${ */}
      {/*       isDark */}
      {/*         ? '!bg-green-950 !border-green-800' */}
      {/*         : '!border-green-200 !bg-green-100' */}
      {/*     } ${isMobile ? 'p-2' : ''}`} */}
      {/*   > */}
      {/*     <Statistic */}
      {/*       title={ */}
      {/*         <span */}
      {/*           className={`${ */}
      {/*             isDark ? 'text-white' : 'text-green-700' */}
      {/*           } font-medium ${isMobile ? 'text-xs' : 'text-lg'}`} */}
      {/*         > */}
      {/*           Total Counter */}
      {/*         </span> */}
      {/*       } */}
      {/*       value={countDt?.items.length || 0} */}
      {/*       loading={statsLoading} */}
      {/*       prefix={<ShopOutlined className='text-green-600' />} */}
      {/*       valueStyle={{ */}
      {/*         color: '#059669', */}
      {/*         fontSize: statisticFontSize, */}
      {/*       }} */}
      {/*     /> */}
      {/*     <div className='mt-2'> */}
      {/*       <Text */}
      {/*         className={`${isMobile ? 'text-xs' : 'text-xs'} text-green-600`} */}
      {/*       > */}
      {/*         <span className='font-medium mr-1'> */}
      {/*           {countDt?.items.length || 0} */}
      {/*         </span> */}
      {/*         {t('active')} */}
      {/*       </Text> */}
      {/*     </div> */}
      {/*   </Card> */}
      {/* </Col> */}
      {/* missing API  */}

      {/* <Col xs={12} sm={12} lg={6}>
                      <Card
                        className={`text-center ${
                          isDark
                            ? '!bg-purple-950 !border-purple-800'
                            : '!border-purple-200 !bg-purple-100'
                        } ${isMobile ? 'p-2' : ''}`}
                      >
                        <Statistic
                          title={
                            <span
                              className={`${
                                isDark ? 'text-white' : 'text-purple-700'
                              } font-medium ${isMobile ? 'text-xs' : 'text-lg'}`}
                            >
                              {t('openRegisters')}
                            </span>
                          }
                          value={stats?.openRegisters || 0}
                          suffix={`/ ${stats?.totalRegisters || 0}`}
                          loading={statsLoading}
                          prefix={<BankOutlined className='text-purple-600' />}
                          valueStyle={{
                            color: '#7c3aed',
                            fontSize: statisticFontSize,
                          }}
                        />
                        <div className='mt-2'>
                          <Text
                            className={`${isMobile ? 'text-xs' : 'text-xs'} text-purple-600`}
                          >
                            {isMobile ? t('register') : t('cashRegis')}
                          </Text>
                        </div>
                      </Card>
                    </Col> */}
      {/* missing API  */}
      {/* <Col xs={12} sm={12} lg={6}>
                      <Card
                        className={`text-center ${
                          isDark
                            ? '!bg-orange-900 !border-orange-800'
                            : '!border-orange-200 !bg-orange-100'
                        } ${isMobile ? 'p-2' : ''}`}
                      >
                        <Statistic
                          title={
                            <span
                              className={`${
                                isDark ? 'text-white' : 'text-orange-700'
                              } font-medium ${isMobile ? 'text-xs' : 'text-lg'}`}
                            >
                              {t('todayRevenue')}
                            </span>
                          }
                          value={stats?.todayRevenue || 0}
                          precision={2}
                          prefix='$'
                          loading={statsLoading}
                          valueStyle={{
                            color: '#ea580c',
                            fontSize: statisticFontSize,
                          }}
                        />
                        <div className='mt-2'>
                          <Text
                            className={`${isMobile ? 'text-xs' : 'text-xs'} text-orange-600`}
                          >
                            {isMobile ? t('today') : t('todayPerform')}
                          </Text>
                        </div>
                      </Card>
                    </Col> */}
      {/* </Row> */}

      {/* Alert Section */}
      {/* FE team use later  */}

      {/* {stats &&
                    (stats.lowStockItems > 0 || stats.pendingShifts > 0) && (
                      <div
                        className={`${isMobile ? 'mt-4' : '!mt-6'} !space-y-3`}
                      >
                        {stats.lowStockItems > 0 && (
                          <Alert
                            message={
                              <span
                                className={`font-medium ${isMobile ? 'text-sm' : ''}`}
                              >
                                {stats.lowStockItems} {t('lowStockItems')}
                              </span>
                            }
                            description={
                              !isMobile ? t('someProduct') : undefined
                            }
                            type='warning'
                            icon={<WarningOutlined />}
                            showIcon
                            className='border-orange-200'
                          />
                        )}

                        {stats.pendingShifts > 0 && (
                          <Alert
                            message={
                              <span
                                className={`font-medium ${isMobile ? 'text-sm' : ''}`}
                              >
                                {stats.pendingShifts} {t('pendingShifts')}
                              </span>
                            }
                            description={!isMobile ? t('someShift') : undefined}
                            type='info'
                            icon={<ClockCircleOutlined />}
                            showIcon
                            className='border-blue-200'
                          />
                        )}
                      </div>
                    )} */}
      {/*           </div> */}
      {/*         ), */}
      {/*       }, */}
      {/*     ]} */}
      {/*   /> */}
      {/* </Card> */}

      {/* Management Tabs */}
      <Card
        className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card`}
      >
        <Tabs
          activeKey={activeTab}
          onChange={(value) => {
            setActiveTab(value as TabKey);
          }}
          items={tabItems}
          size={isMobile ? 'small' : 'large'}
          className='management-tabs'
          tabPosition={isMobile ? 'top' : 'top'}
        />
      </Card>
    </div>
  );
};

export default Management;
