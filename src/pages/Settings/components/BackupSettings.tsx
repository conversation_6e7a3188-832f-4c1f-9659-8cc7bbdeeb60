import {
  SaveOutlined,
  SafetyOutlined,
  CloudDownloadOutlined,
  HistoryOutlined,
  DeleteOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import {
  Card,
  Form,
  Switch,
  Button,
  Row,
  Col,
  Typography,
  Space,
  message,
  Select,
  TimePicker,
  Tag,
  Alert,
  Modal,
} from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import GalaxyTable from '@/components/WeAreTableGalaxy/GalaxyTable';
import { useUserStore } from '@/stores';

const { Text } = Typography;
const { Option } = Select;

interface BackupData {
  autoBackup: boolean;
  backupFrequency: string;
  backupTime: dayjs.Dayjs;
  retentionDays: number;
  includeFiles: boolean;
  includeDatabase: boolean;
  includeSettings: boolean;
  cloudBackup: boolean;
  cloudProvider: string;
}

interface BackupRecord {
  id: string;
  type: 'auto' | 'manual';
  status: 'completed' | 'failed' | 'in_progress';
  size: string;
  date: string;
  duration: string;
  includes: string[];
}

const BackupSettings: React.FC = () => {
  const { isDark } = useUserStore();
  const { t } = useTranslation('backupSettings');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [backupInProgress, setBackupInProgress] = useState(false);

  const initialData: BackupData = {
    autoBackup: true,
    backupFrequency: 'daily',
    backupTime: dayjs('02:00'),
    retentionDays: 30,
    includeFiles: true,
    includeDatabase: true,
    includeSettings: true,
    cloudBackup: false,
    cloudProvider: 'aws',
  };

  const mockBackups: BackupRecord[] = [
    {
      id: '1',
      type: 'auto',
      status: 'completed',
      size: '2.4 GB',
      date: '2024-02-24T02:00:00Z',
      duration: '12 min',
      includes: ['database', 'files', 'settings'],
    },
    {
      id: '2',
      type: 'manual',
      status: 'completed',
      size: '2.3 GB',
      date: '2024-02-23T15:30:00Z',
      duration: '10 min',
      includes: ['database', 'settings'],
    },
    {
      id: '3',
      type: 'auto',
      status: 'failed',
      size: '-',
      date: '2024-02-23T02:00:00Z',
      duration: '2 min',
      includes: ['database', 'files', 'settings'],
    },
  ];
  // not use by Shuu
  // values: BackupData
  const handleSave = async () => {
    setLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      message.success(t('saveSuccess'));
    } catch (error) {
      message.error(t('saveError'));
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleManualBackup = async () => {
    setBackupInProgress(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 3000));
      message.success(t('backupSuccess'));
    } catch (error) {
      message.error(t('backupError'));
      console.log(error);
    } finally {
      setBackupInProgress(false);
    }
  };
  // not use by Shuu
  // backupId: string
  const handleDeleteBackup = () => {
    Modal.confirm({
      title: t('deleteBackup'),
      content: t('deleteBackupConfirm'),
      icon: <ExclamationCircleOutlined />,
      onOk() {
        message.success(t('backupDeleted'));
      },
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined className='text-green-600' />;
      case 'failed':
        return <ExclamationCircleOutlined className='text-red-600' />;
      case 'in_progress':
        return <ClockCircleOutlined className='text-blue-600' />;
      default:
        return null;
    }
  };

  const backupColumns = [
    {
      title: t('type'),
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={type === 'auto' ? 'blue' : 'green'}>{t(type)}</Tag>
      ),
    },
    {
      title: t('status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <div className='flex items-center gap-2'>
          {getStatusIcon(status)}
          <span>{t(status)}</span>
        </div>
      ),
    },
    {
      title: t('date'),
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => dayjs(date).format('MMM DD, YYYY HH:mm'),
    },
    {
      title: t('size'),
      dataIndex: 'size',
      key: 'size',
    },
    {
      title: t('duration'),
      dataIndex: 'duration',
      key: 'duration',
    },
    {
      title: t('actions'),
      key: 'actions',
      render: (_: any, record: BackupRecord) => (
        <Space>
          {record.status === 'completed' && (
            <Button
              type='text'
              icon={<DownloadOutlined />}
              size='small'
              onClick={() => message.info('Download backup')}
            />
          )}
          <Button
            type='text'
            icon={<DeleteOutlined />}
            size='small'
            danger
            onClick={handleDeleteBackup}
          />
        </Space>
      ),
    },
  ];

  return (
    <div className='space-y-6'>
      {/* Backup Status */}
      <Card
        title={
          <span className='flex items-center gap-2'>
            <SafetyOutlined className='text-blue-600' />
            {t('backupStatus')}
          </span>
        }
        className='rounded-lg'
      >
        <Row gutter={[24, 16]}>
          <Col xs={24} sm={8}>
            <div
              className={`text-center p-4 rounded-lg ${
                isDark ? 'bg-green-900 border border-green-700' : 'bg-green-200'
              }`}
            >
              <div
                className={`text-2xl font-bold ${
                  isDark ? 'text-white' : 'text-green-600'
                }`}
              >
                15
              </div>
              <div
                className={`text-sm ${
                  isDark ? 'text-green-300' : 'text-green-700'
                }`}
              >
                {t('totalBackups')}
              </div>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <div
              className={`text-center p-4 rounded-lg ${
                isDark ? 'bg-blue-900 border border-blue-700' : 'bg-blue-200'
              }`}
            >
              <div
                className={`text-2xl font-bold ${
                  isDark ? 'text-white' : 'text-blue-600'
                }`}
              >
                2.4 GB
              </div>
              <div
                className={`text-sm ${
                  isDark ? 'text-blue-300' : 'text-blue-700'
                }`}
              >
                {t('lastBackupSize')}
              </div>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <div
              className={`text-center p-4 rounded-lg ${
                isDark
                  ? 'bg-purple-900 border border-purple-700'
                  : 'bg-purple-200'
              }`}
            >
              <div
                className={`text-2xl font-bold ${
                  isDark ? 'text-white' : 'text-purple-600'
                }`}
              >
                98.5%
              </div>
              <div
                className={`text-sm ${
                  isDark ? 'text-purple-300' : 'text-purple-700'
                }`}
              >
                {t('successRate')}
              </div>
            </div>
          </Col>
        </Row>

        <div className='mt-4 flex justify-center'>
          <Button
            type='primary'
            icon={<CloudDownloadOutlined />}
            onClick={handleManualBackup}
            loading={backupInProgress}
            size='large'
            className='bg-blue-600 hover:bg-blue-700'
          >
            {t('createBackup')}
          </Button>
        </div>
      </Card>

      <Form
        form={form}
        layout='vertical'
        initialValues={initialData}
        onFinish={handleSave}
        className='space-y-6'
      >
        {/* Automatic Backup */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <HistoryOutlined className='text-blue-600' />
              {t('automaticBackup')}
            </span>
          }
          className='rounded-lg'
        >
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <div>
                <Text className='font-medium'>{t('enableAutoBackup')}</Text>
                <div className='text-sm text-gray-500'>
                  {t('enableAutoBackupDesc')}
                </div>
              </div>
              <Form.Item
                name='autoBackup'
                valuePropName='checked'
                className='mb-0'
              >
                <Switch />
              </Form.Item>
            </div>

            <Row gutter={[24, 16]}>
              <Col xs={24} lg={8}>
                <Form.Item
                  name='backupFrequency'
                  label={t('frequencyLabel')}
                  dependencies={['autoBackup']}
                >
                  <Select
                    size='large'
                    disabled={!form.getFieldValue('autoBackup')}
                  >
                    <Option value='hourly'>{t('hourly')}</Option>
                    <Option value='daily'>{t('daily')}</Option>
                    <Option value='weekly'>{t('weekly')}</Option>
                    <Option value='monthly'>{t('monthly')}</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} lg={8}>
                <Form.Item
                  name='backupTime'
                  label={t('backupTimeLabel')}
                  dependencies={['autoBackup']}
                >
                  <TimePicker
                    size='large'
                    format='HH:mm'
                    className='w-full'
                    disabled={!form.getFieldValue('autoBackup')}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} lg={8}>
                <Form.Item
                  name='retentionDays'
                  label={t('retentionDaysLabel')}
                  dependencies={['autoBackup']}
                >
                  <Select
                    size='large'
                    disabled={!form.getFieldValue('autoBackup')}
                  >
                    <Option value={7}>7 {t('days')}</Option>
                    <Option value={14}>14 {t('days')}</Option>
                    <Option value={30}>30 {t('days')}</Option>
                    <Option value={90}>90 {t('days')}</Option>
                    <Option value={365}>1 {t('year')}</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </div>
        </Card>

        {/* Backup Content */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <SafetyOutlined className='text-blue-600' />
              {t('backupContent')}
            </span>
          }
          className='rounded-lg'
        >
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <div>
                <Text className='font-medium'>{t('includeDatabase')}</Text>
                <div className='text-sm text-gray-500'>
                  {t('includeDatabaseDesc')}
                </div>
              </div>
              <Form.Item
                name='includeDatabase'
                valuePropName='checked'
                className='mb-0'
              >
                <Switch />
              </Form.Item>
            </div>

            <div className='flex items-center justify-between'>
              <div>
                <Text className='font-medium'>{t('includeFiles')}</Text>
                <div className='text-sm text-gray-500'>
                  {t('includeFilesDesc')}
                </div>
              </div>
              <Form.Item
                name='includeFiles'
                valuePropName='checked'
                className='mb-0'
              >
                <Switch />
              </Form.Item>
            </div>

            <div className='flex items-center justify-between'>
              <div>
                <Text className='font-medium'>{t('includeSettings')}</Text>
                <div className='text-sm text-gray-500'>
                  {t('includeSettingsDesc')}
                </div>
              </div>
              <Form.Item
                name='includeSettings'
                valuePropName='checked'
                className='mb-0'
              >
                <Switch />
              </Form.Item>
            </div>
          </div>
        </Card>

        {/* Cloud Backup */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <CloudDownloadOutlined className='text-blue-600' />
              {t('cloudBackup')}
            </span>
          }
          className='rounded-lg'
        >
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <div>
                <Text className='font-medium'>{t('enableCloudBackup')}</Text>
                <div className='text-sm text-gray-500'>
                  {t('enableCloudBackupDesc')}
                </div>
              </div>
              <Form.Item
                name='cloudBackup'
                valuePropName='checked'
                className='mb-0'
              >
                <Switch />
              </Form.Item>
            </div>

            <Form.Item
              name='cloudProvider'
              label={t('cloudProvider')}
              dependencies={['cloudBackup']}
            >
              <Select
                size='large'
                disabled={!form.getFieldValue('cloudBackup')}
              >
                <Option value='aws'>Amazon S3</Option>
                <Option value='gcp'>Google Cloud Storage</Option>
                <Option value='azure'>Azure Blob Storage</Option>
                <Option value='dropbox'>Dropbox</Option>
              </Select>
            </Form.Item>

            <Alert
              message={t('cloudWarning')}
              description={t('cloudWarningDesc')}
              type='info'
              showIcon
            />
          </div>
        </Card>

        {/* Action Buttons */}
        <Card className='rounded-lg'>
          <div className='flex justify-end'>
            <Space>
              <Button size='large'>{t('cancel')}</Button>
              <Button
                type='primary'
                icon={<SaveOutlined />}
                htmlType='submit'
                loading={loading}
                size='large'
                className='bg-blue-600 hover:bg-blue-700'
              >
                {t('saveChanges')}
              </Button>
            </Space>
          </div>
        </Card>
      </Form>

      {/* Backup History */}
      <Card
        title={
          <span className='flex items-center gap-2'>
            <HistoryOutlined className='text-blue-600' />
            {t('backupHistory')}
          </span>
        }
        className='rounded-lg'
      >
        <GalaxyTable
          data={mockBackups}
          columns={backupColumns}
          rowKey='id'
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} backups`,
            current: 1,
            total: 10,
            onChange: (page, pageSize) => {
              console.log('Page:', page, 'PageSize:', pageSize);
            },
          }}
          size='small'
        />
      </Card>
    </div>
  );
};

export default BackupSettings;
