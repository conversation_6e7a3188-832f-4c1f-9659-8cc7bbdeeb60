import {
  SaveOutlined,
  ReloadOutlined,
  SettingOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import {
  Card,
  Form,
  Input,
  Select,
  Switch,
  Button,
  Row,
  Col,
  Typography,
  Divider,
  TimePicker,
  InputNumber,
  Space,
  message,
} from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

const { Text } = Typography;
const { Option } = Select;

interface GeneralSettingsData {
  siteName: string;
  siteDescription: string;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
  currency: string;
  currencySymbol: string;
  decimalPlaces: number;
  businessHours: {
    start: dayjs.Dayjs;
    end: dayjs.Dayjs;
  };
  maintenanceMode: boolean;
  debugMode: boolean;
  autoSave: boolean;
  autoSaveInterval: number;
}

const GeneralSettings: React.FC = () => {
  const { t } = useTranslation('generalSettings');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // Mock initial data - in real app this would come from API
  const initialData: GeneralSettingsData = {
    siteName: 'YF Pay Admin',
    siteDescription: 'Casino Management System',
    timezone: 'Asia/Taipei',
    dateFormat: 'YYYY-MM-DD',
    timeFormat: '24h',
    currency: 'USD',
    currencySymbol: '$',
    decimalPlaces: 2,
    businessHours: {
      start: dayjs('09:00', 'HH:mm'),
      end: dayjs('23:00', 'HH:mm'),
    },
    maintenanceMode: false,
    debugMode: false,
    autoSave: true,
    autoSaveInterval: 5,
  };
  // 2 param not use by Shuu :
  // values: GeneralSettingsData
  // error
  const handleSave = async () => {
    setLoading(true);
    try {
      // Mock API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      message.success(t('saveSuccess'));
    } catch (error) {
      message.error(t('saveError'));
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.setFieldsValue(initialData);
    message.info(t('resetSuccess'));
  };

  return (
    <div className='space-y-6'>
      <Form
        form={form}
        layout='vertical'
        initialValues={initialData}
        onFinish={handleSave}
        className='space-y-6'
      >
        {/* Site Information */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <SettingOutlined className='text-blue-600' />
              {t('siteInfo')}
            </span>
          }
          className='rounded-lg'
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} lg={12}>
              <Form.Item
                name='siteName'
                label={t('siteNameLabel')}
                rules={[
                  {
                    required: true,
                    message: t('siteNameError'),
                  },
                ]}
              >
                <Input size='large' />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name='siteDescription'
                label={t('siteDescriptionLabel')}
              >
                <Input size='large' />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Regional Settings */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <GlobalOutlined className='text-blue-600' />
              {t('regionalSettings')}
            </span>
          }
          className='rounded-lg'
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} lg={8}>
              <Form.Item
                name='timezone'
                label={t('timezoneLabel')}
                rules={[
                  {
                    required: true,
                    message: t('timezoneError'),
                  },
                ]}
              >
                <Select size='large' showSearch>
                  <Option value='Asia/Taipei'>Asia/Taipei (GMT+8)</Option>
                  <Option value='Asia/Hong_Kong'>Asia/Hong Kong (GMT+8)</Option>
                  <Option value='Asia/Shanghai'>Asia/Shanghai (GMT+8)</Option>
                  <Option value='Asia/Tokyo'>Asia/Tokyo (GMT+9)</Option>
                  <Option value='America/New_York'>
                    America/New York (GMT-5)
                  </Option>
                  <Option value='Europe/London'>Europe/London (GMT+0)</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} lg={8}>
              <Form.Item
                name='dateFormat'
                label={t('dateFormatLabel')}
                rules={[
                  {
                    required: true,
                    message: t('dateFormatError'),
                  },
                ]}
              >
                <Select size='large'>
                  <Option value='YYYY-MM-DD'>YYYY-MM-DD</Option>
                  <Option value='DD/MM/YYYY'>DD/MM/YYYY</Option>
                  <Option value='MM/DD/YYYY'>MM/DD/YYYY</Option>
                  <Option value='DD-MM-YYYY'>DD-MM-YYYY</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} lg={8}>
              <Form.Item
                name='timeFormat'
                label={t('timeFormatLabel')}
                rules={[
                  {
                    required: true,
                    message: t('timeFormatError'),
                  },
                ]}
              >
                <Select size='large'>
                  <Option value='24h'>24 {t('hour')} (23:59)</Option>
                  <Option value='12h'>12 {t('hour')} (11:59 PM)</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Currency Settings */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <DollarOutlined className='text-blue-600' />
              {t('currencySettings')}
            </span>
          }
          className='rounded-lg'
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} lg={8}>
              <Form.Item
                name='currency'
                label={t('currencyLabel')}
                rules={[
                  {
                    required: true,
                    message: t('currencyError'),
                  },
                ]}
              >
                <Select size='large' showSearch>
                  <Option value='USD'>USD - US Dollar</Option>
                  <Option value='EUR'>EUR - Euro</Option>
                  <Option value='GBP'>GBP - British Pound</Option>
                  <Option value='JPY'>JPY - Japanese Yen</Option>
                  <Option value='CNY'>CNY - Chinese Yuan</Option>
                  <Option value='HKD'>HKD - Hong Kong Dollar</Option>
                  <Option value='TWD'>TWD - Taiwan Dollar</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} lg={8}>
              <Form.Item
                name='currencySymbol'
                label={t('currencySymbolLabel')}
                rules={[
                  {
                    required: true,
                    message: t('currencySymbolError'),
                  },
                ]}
              >
                <Input size='large' maxLength={3} />
              </Form.Item>
            </Col>
            <Col xs={24} lg={8}>
              <Form.Item
                name='decimalPlaces'
                label={t('decimalPlacesLabel')}
                rules={[
                  {
                    required: true,
                    message: t('decimalPlacesError'),
                  },
                ]}
              >
                <InputNumber size='large' min={0} max={4} className='w-full' />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Business Hours */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <ClockCircleOutlined className='text-blue-600' />
              {t('businessHours')}
            </span>
          }
          className='rounded-lg'
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} lg={12}>
              <Form.Item
                name={['businessHours', 'start']}
                label={t('openingTimeLabel')}
                rules={[
                  {
                    required: true,
                    message: t('openingTimeError'),
                  },
                ]}
              >
                <TimePicker
                  size='large'
                  format='HH:mm'
                  className='w-full'
                  defaultValue={dayjs('09:00', 'HH:mm')}
                />
              </Form.Item>
            </Col>
            <Col xs={24} lg={12}>
              <Form.Item
                name={['businessHours', 'end']}
                label={t('closingTimeLabel')}
                rules={[
                  {
                    required: true,
                    message: t('closingTimeError'),
                  },
                ]}
              >
                <TimePicker
                  size='large'
                  format='HH:mm'
                  className='w-full'
                  defaultValue={dayjs('23:00', 'HH:mm')}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* System Options */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <SettingOutlined className='text-blue-600' />
              {t('systemOptions')}
            </span>
          }
          className='rounded-lg'
        >
          <div className='space-y-4'>
            <Row gutter={[24, 16]}>
              <Col xs={24} lg={12}>
                <div className='flex items-center justify-between'>
                  <div>
                    <Text className='font-medium'>{t('maintenanceMode')}</Text>
                    <div className='text-sm text-gray-500'>
                      {t('maintenanceModeDesc')}
                    </div>
                  </div>
                  <Form.Item
                    name='maintenanceMode'
                    valuePropName='checked'
                    className='mb-0'
                  >
                    <Switch />
                  </Form.Item>
                </div>
              </Col>
              <Col xs={24} lg={12}>
                <div className='flex items-center justify-between'>
                  <div>
                    <Text className='font-medium'>{t('debugMode')}</Text>
                    <div className='text-sm text-gray-500'>
                      {t('debugModeDesc')}
                    </div>
                  </div>
                  <Form.Item
                    name='debugMode'
                    valuePropName='checked'
                    className='mb-0'
                  >
                    <Switch />
                  </Form.Item>
                </div>
              </Col>
            </Row>

            <Divider />

            <Row gutter={[24, 16]}>
              <Col xs={24} lg={12}>
                <div className='flex items-center justify-between'>
                  <div>
                    <Text className='font-medium'>{t('autoSave')}</Text>
                    <div className='text-sm text-gray-500'>
                      {t('autoSaveDesc')}
                    </div>
                  </div>
                  <Form.Item
                    name='autoSave'
                    valuePropName='checked'
                    className='mb-0'
                  >
                    <Switch />
                  </Form.Item>
                </div>
              </Col>
              <Col xs={24} lg={12}>
                <Form.Item
                  name='autoSaveInterval'
                  label={t('autoSaveIntervalLabel')}
                  dependencies={['autoSave']}
                >
                  <InputNumber
                    size='large'
                    min={1}
                    max={60}
                    className='w-full'
                    disabled={!form.getFieldValue('autoSave')}
                    addonAfter={t('minutes')}
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>
        </Card>

        {/* Action Buttons */}
        <Card className='rounded-lg'>
          <div className='flex justify-between'>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReset}
              size='large'
            >
              {t('reset')}
            </Button>

            <Space>
              <Button size='large'>{t('cancel')}</Button>
              <Button
                type='primary'
                icon={<SaveOutlined />}
                htmlType='submit'
                loading={loading}
                size='large'
                className='bg-blue-600 hover:bg-blue-700'
              >
                {t('saveChanges')}
              </Button>
            </Space>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default GeneralSettings;
