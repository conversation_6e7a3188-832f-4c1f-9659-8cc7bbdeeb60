import {
  SaveOutlined,
  BellOutlined,
  MailOutlined,
  MessageOutlined,
  MobileOutlined,
  SoundOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import {
  Card,
  Form,
  Switch,
  Button,
  Row,
  Col,
  Typography,
  Space,
  message,
  TimePicker,
  Divider,
  Checkbox,
} from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;

interface NotificationData {
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  soundEnabled: boolean;
  quietHours: {
    enabled: boolean;
    start: dayjs.Dayjs;
    end: dayjs.Dayjs;
  };
  notifications: {
    newOrders: boolean;
    memberRegistrations: boolean;
    systemAlerts: boolean;
    securityAlerts: boolean;
    paymentAlerts: boolean;
    employeeUpdates: boolean;
    maintenanceAlerts: boolean;
    reportGeneration: boolean;
  };
  frequency: {
    immediate: string[];
    daily: string[];
    weekly: string[];
  };
}

const NotificationSettings: React.FC = () => {
  const { t } = useTranslation('notificationSettings');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // Mock initial data
  const initialData: NotificationData = {
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    soundEnabled: true,
    quietHours: {
      enabled: true,
      start: dayjs('22:00'),
      end: dayjs('08:00'),
    },
    notifications: {
      newOrders: true,
      memberRegistrations: true,
      systemAlerts: true,
      securityAlerts: true,
      paymentAlerts: true,
      employeeUpdates: false,
      maintenanceAlerts: true,
      reportGeneration: false,
    },
    frequency: {
      immediate: ['systemAlerts', 'securityAlerts', 'paymentAlerts'],
      daily: ['newOrders', 'memberRegistrations'],
      weekly: ['reportGeneration'],
    },
  };
  const handleSave = async () => {
    setLoading(true);
    try {
      // Mock API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      message.success(t('saveSuccess'));
    } catch {
      message.error(t('saveError'));
    } finally {
      setLoading(false);
    }
  };

  const notificationTypes = [
    {
      key: 'newOrders',
      label: t('newOrders'),
      description: t('newOrdersDesc'),
      icon: <BellOutlined className='text-blue-500' />,
    },
    {
      key: 'memberRegistrations',
      label: t('memberRegistrations'),
      description: t('memberRegistrationsDesc'),
      icon: <BellOutlined className='text-green-500' />,
    },
    {
      key: 'systemAlerts',
      label: t('systemAlerts'),
      description: t('systemAlertsDesc'),
      icon: <BellOutlined className='text-orange-500' />,
    },
    {
      key: 'securityAlerts',
      label: t('securityAlerts'),
      description: t('securityAlertsDesc'),
      icon: <BellOutlined className='text-red-500' />,
    },
    {
      key: 'paymentAlerts',
      label: t('paymentAlerts'),
      description: t('paymentAlertsDesc'),
      icon: <BellOutlined className='text-purple-500' />,
    },
    {
      key: 'employeeUpdates',
      label: t('employeeUpdates'),
      description: t('employeeUpdatesDesc'),
      icon: <BellOutlined className='text-cyan-500' />,
    },
    {
      key: 'maintenanceAlerts',
      label: t('maintenanceAlerts'),
      description: t('maintenanceAlertsDesc'),
      icon: <BellOutlined className='text-yellow-500' />,
    },
    {
      key: 'reportGeneration',
      label: t('reportGeneration'),
      description: t('reportGenerationDesc'),
      icon: <BellOutlined className='text-indigo-500' />,
    },
  ];

  return (
    <div className='space-y-6'>
      <Form
        form={form}
        layout='vertical'
        initialValues={initialData}
        onFinish={handleSave}
        className='space-y-6'
      >
        {/* Delivery Methods */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <MessageOutlined className='text-blue-600' />
              {t('deliveryMethods')}
            </span>
          }
          className='rounded-lg'
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={8}>
              <div className='flex items-center justify-between p-4 border rounded-lg'>
                <div className='flex items-center gap-3'>
                  <MailOutlined className='text-blue-500 text-xl' />
                  <div>
                    <Text className='font-medium'>{t('email')}</Text>
                    <div className='text-sm text-gray-500'>
                      {t('emailDesc')}
                    </div>
                  </div>
                </div>
                <Form.Item
                  name='emailNotifications'
                  valuePropName='checked'
                  className='mb-0'
                >
                  <Switch />
                </Form.Item>
              </div>
            </Col>
            <Col xs={24} sm={8}>
              <div className='flex items-center justify-between p-4 border rounded-lg'>
                <div className='flex items-center gap-3'>
                  <MobileOutlined className='text-green-500 text-xl' />
                  <div>
                    <Text className='font-medium'>{t('sms')}</Text>
                    <div className='text-sm text-gray-500'>{t('smsDesc')}</div>
                  </div>
                </div>
                <Form.Item
                  name='smsNotifications'
                  valuePropName='checked'
                  className='mb-0'
                >
                  <Switch />
                </Form.Item>
              </div>
            </Col>
            <Col xs={24} sm={8}>
              <div className='flex items-center justify-between p-4 border rounded-lg'>
                <div className='flex items-center gap-3'>
                  <BellOutlined className='text-purple-500 text-xl' />
                  <div>
                    <Text className='font-medium'>{t('push')}</Text>
                    <div className='text-sm text-gray-500'>{t('pushDesc')}</div>
                  </div>
                </div>
                <Form.Item
                  name='pushNotifications'
                  valuePropName='checked'
                  className='mb-0'
                >
                  <Switch />
                </Form.Item>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Sound & Quiet Hours */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <SoundOutlined className='text-blue-600' />
              {t('soundSettings')}
            </span>
          }
          className='rounded-lg'
        >
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <div>
                <Text className='font-medium'>{t('soundEnabled')}</Text>
                <div className='text-sm text-gray-500'>
                  {t('soundEnabledDesc')}
                </div>
              </div>
              <Form.Item
                name='soundEnabled'
                valuePropName='checked'
                className='mb-0'
              >
                <Switch />
              </Form.Item>
            </div>

            <Divider />

            <div className='flex items-center justify-between'>
              <div>
                <Text className='font-medium'>{t('quietHours')}</Text>
                <div className='text-sm text-gray-500'>
                  {t('quietHoursDesc')}
                </div>
              </div>
              <Form.Item
                name={['quietHours', 'enabled']}
                valuePropName='checked'
                className='mb-0'
              >
                <Switch />
              </Form.Item>
            </div>

            <Row gutter={[16, 16]} className='mt-4'>
              <Col xs={24} sm={12}>
                <Form.Item
                  name={['quietHours', 'start']}
                  label={t('quietStart')}
                  dependencies={[['quietHours', 'enabled']]}
                >
                  <TimePicker
                    size='large'
                    format='HH:mm'
                    className='w-full'
                    disabled={!form.getFieldValue(['quietHours', 'enabled'])}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  name={['quietHours', 'end']}
                  label={t('quietEnd')}
                  dependencies={[['quietHours', 'enabled']]}
                >
                  <TimePicker
                    size='large'
                    format='HH:mm'
                    className='w-full'
                    disabled={!form.getFieldValue(['quietHours', 'enabled'])}
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>
        </Card>

        {/* Notification Types */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <BellOutlined className='text-blue-600' />
              {t('notificationTypes')}
            </span>
          }
          className='rounded-lg'
        >
          <div className='space-y-4'>
            {notificationTypes.map((type) => (
              <div
                key={type.key}
                className='flex items-center justify-between p-4 border rounded-lg'
              >
                <div className='flex items-center gap-3'>
                  {type.icon}
                  <div>
                    <Text className='font-medium'>{type.label}</Text>
                    <div className='text-sm text-gray-500'>
                      {type.description}
                    </div>
                  </div>
                </div>
                <Form.Item
                  name={['notifications', type.key]}
                  valuePropName='checked'
                  className='mb-0'
                >
                  <Switch />
                </Form.Item>
              </div>
            ))}
          </div>
        </Card>

        {/* Frequency Settings */}
        <Card
          title={
            <span className='flex items-center gap-2'>
              <ClockCircleOutlined className='text-blue-600' />
              {t('frequency')}
            </span>
          }
          className='rounded-lg'
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} lg={8}>
              <div className='border rounded-lg p-4'>
                <Title level={5} className='text-green-600 mb-3'>
                  {t('immediate')}
                </Title>
                <Text className='text-sm text-gray-500 block mb-3'>
                  {t('immediateDesc')}
                </Text>
                <Form.Item name={['frequency', 'immediate']} className='mb-0'>
                  <Checkbox.Group className='flex flex-col gap-2'>
                    {notificationTypes.map((type) => (
                      <Checkbox key={type.key} value={type.key}>
                        {type.label}
                      </Checkbox>
                    ))}
                  </Checkbox.Group>
                </Form.Item>
              </div>
            </Col>
            <Col xs={24} lg={8}>
              <div className='border rounded-lg p-4'>
                <Title level={5} className='text-blue-600 mb-3'>
                  {t('daily')}
                </Title>
                <Text className='text-sm text-gray-500 block mb-3'>
                  {t('dailyDesc')}
                </Text>
                <Form.Item name={['frequency', 'daily']} className='mb-0'>
                  <Checkbox.Group className='flex flex-col gap-2'>
                    {notificationTypes.map((type) => (
                      <Checkbox key={type.key} value={type.key}>
                        {type.label}
                      </Checkbox>
                    ))}
                  </Checkbox.Group>
                </Form.Item>
              </div>
            </Col>
            <Col xs={24} lg={8}>
              <div className='border rounded-lg p-4'>
                <Title level={5} className='text-purple-600 mb-3'>
                  {t('weekly')}
                </Title>
                <Text className='text-sm text-gray-500 block mb-3'>
                  {t('weeklyDesc')}
                </Text>
                <Form.Item name={['frequency', 'weekly']} className='mb-0'>
                  <Checkbox.Group className='flex flex-col gap-2'>
                    {notificationTypes.map((type) => (
                      <Checkbox key={type.key} value={type.key}>
                        {type.label}
                      </Checkbox>
                    ))}
                  </Checkbox.Group>
                </Form.Item>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Action Buttons */}
        <Card className='rounded-lg'>
          <div className='flex justify-end'>
            <Space>
              <Button size='large'>{t('cancel')}</Button>
              <Button
                type='primary'
                icon={<SaveOutlined />}
                htmlType='submit'
                loading={loading}
                size='large'
                className='bg-blue-600 hover:bg-blue-700'
              >
                {t('saveChanges')}
              </Button>
            </Space>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default NotificationSettings;
