import {
  SettingOutlined,
  BellOutlined,
  DatabaseOutlined,
  SafetyOutlined,
} from '@ant-design/icons';
import {Tabs, Card, Typography, Alert, Tag} from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import BackupSettings from './components/BackupSettings';
import GeneralSettings from './components/GeneralSettings';
import NotificationSettings from './components/NotificationSettings';
import SystemSettings from './components/SystemSettings';
import { Txt } from '@/components';
import { useUserStore } from '@/stores';
// import PerformanceSettings from './components/PerformanceSettings';

const { Title } = Typography;

const Settings: React.FC = () => {
  const { t } = useTranslation('settings');
  const { isDark } = useUserStore();

  const [activeTab, setActiveTab] = useState('general');

  const tabItems = [
    {
      key: 'general',
      label: (
        <span className='flex items-center gap-2'>
          <SettingOutlined />
          {t('general')}
          <Tag color='blue'>Fake</Tag>
        </span>
      ),
      children: <GeneralSettings />,
    },
    {
      key: 'notifications',
      label: (
        <span className='flex items-center gap-2'>
          <BellOutlined />
          {t('notifications')}
          <Tag color='blue'>Fake</Tag>
        </span>
      ),
      children: <NotificationSettings />,
    },
    {
      key: 'system',
      label: (
        <span className='flex items-center gap-2'>
          <DatabaseOutlined />
          {t('system')}
          <Tag color='blue'>Fake</Tag>
        </span>
      ),
      children: <SystemSettings />,
    },
    {
      key: 'backup',
      label: (
        <span className='flex items-center gap-2'>
          <SafetyOutlined />
          {t('backup')}
          <Tag color='blue'>Fake</Tag>
        </span>
      ),
      children: <BackupSettings />,
    },
    // {
    //   key: 'performance',
    //   label: (
    //     <span className="flex items-center gap-2">
    //       <ThunderboltOutlined />
    //       {t('settings.tabs.performance')}
    //     </span>
    //   ),
    //   children: <PerformanceSettings />,
    // },
  ];

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div
        className={`rounded-xl p-4 md:rounded-2xl md:p-6 lg:p-8 bg-gradient-to-r ${
          isDark
            ? 'from-gray-700 via-gray-800 to-gray-900'
            : 'from-gray-100 via-gray-200 to-gray-300'
        }`}
      >
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-3 sm:gap-4'>
            <div className='w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-gray-400 to-gray-600 rounded-xl flex items-center justify-center flex-shrink-0'>
              <SettingOutlined className='!text-white text-lg md:text-xl' />
            </div>
            <div>
              <Title level={2}>{t('title')}</Title>
              <Txt>{t('subtitle')}</Txt>
            </div>
          </div>
        </div>
      </div>

      {/* System Status Alert */}
      <Alert
        message={t('systemStatusTitle')}
        description={t('systemStatusDesc')}
        type='success'
        showIcon
        className='rounded-lg'
      />

      {/* Settings Tabs */}
      <Card className='rounded-2xl border-0 shadow-card'>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size='large'
          className='settings-tabs'
          tabPosition='top'
        />
      </Card>
    </div>
  );
};

export default Settings;
