import {
  BellOutlined,
  CheckOutlined,
  ClearOutlined,
  SettingOutlined,
  FilterOutlined,
  SoundOutlined,
  DesktopOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import {
  Drawer,
  Button,
  Typography,
  Space,
  Empty,
  Tabs,
  Badge,
  Dropdown,
  Switch,
  Tooltip,
} from 'antd';
import React, { useState, useMemo } from 'react';
import ConnectionStatus from './ConnectionStatus';
import NotificationItem from './NotificationItem';
import { useSignalR } from '@/hooks/useSignalR';
import { useSignalRNotificationStore } from '@/stores/useSignalRNotificationStore';
import type {
  NotificationType,
  NotificationSeverity,
} from '@/types/notifications';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface NotificationPanelProps {
  open: boolean;
  onClose: () => void;
  placement?: 'left' | 'right';
  width?: number;
}

const NotificationPanel: React.FC<NotificationPanelProps> = ({
  open,
  onClose,
  placement = 'right',
  width = 400,
}) => {
  const {
    notifications,
    unreadCount,
    markAllAsRead,
    clearNotifications,
    removeNotification,
    soundEnabled,
    setSoundEnabled,
    showDesktopNotifications,
    setShowDesktopNotifications,
    autoMarkAsRead,
    setAutoMarkAsRead,
  } = useSignalRNotificationStore();

  const { markNotificationAsRead } = useSignalR();

  const [activeTab, setActiveTab] = useState<string>('all');
  const [filterSeverity, _setFilterSeverity] = useState<
    NotificationSeverity | 'all'
  >('all');
  const [filterType, setFilterType] = useState<NotificationType | 'all'>('all');

  // Filter notifications based on active tab and filters
  const filteredNotifications = useMemo(() => {
    let filtered = notifications;

    // Filter by read status
    if (activeTab === 'unread') {
      filtered = filtered.filter((n) => !n.isRead);
    } else if (activeTab === 'read') {
      filtered = filtered.filter((n) => n.isRead);
    }

    // Filter by severity
    if (filterSeverity !== 'all') {
      filtered = filtered.filter((n) => n.severity === filterSeverity);
    }

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter((n) => n.type === filterType);
    }

    return filtered;
  }, [notifications, activeTab, filterSeverity, filterType]);

  const handleMarkAsRead = async (notificationId: string) => {
    await markNotificationAsRead(notificationId);
  };

  const handleMarkAllAsRead = async () => {
    const unreadNotifications = notifications.filter((n) => !n.isRead);

    // Mark all as read in the store
    markAllAsRead();

    // Send requests to server for each unread notification
    for (const notification of unreadNotifications) {
      try {
        await markNotificationAsRead(notification.notificationId);
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    }
  };

  const handleRemove = (notificationId: string) => {
    removeNotification(notificationId);
  };

  const settingsMenu = (
    <div className='p-4 w-64 bg-white'>
      <Title level={5} className='!mb-3'>
        Notification Settings
      </Title>

      <Space direction='vertical' className='w-full'>
        <div className='flex items-center justify-between'>
          <Space>
            <SoundOutlined />
            <Text>Sound</Text>
          </Space>
          <Switch
            size='small'
            checked={soundEnabled}
            onChange={setSoundEnabled}
          />
        </div>

        <div className='flex items-center justify-between'>
          <Space>
            <DesktopOutlined />
            <Text>Desktop</Text>
          </Space>
          <Switch
            size='small'
            checked={showDesktopNotifications}
            onChange={setShowDesktopNotifications}
          />
        </div>

        <div className='flex items-center justify-between'>
          <Space>
            <CheckOutlined />
            <Text>Auto Read</Text>
          </Space>
          <Switch
            size='small'
            checked={autoMarkAsRead}
            onChange={setAutoMarkAsRead}
          />
        </div>
      </Space>
    </div>
  );

  const filterMenu = (
    <div className='p-4 w-48 bg-white'>
      <Title level={5} className='!mb-3'>
        Filters
      </Title>

      <Space direction='vertical' className='w-full'>
        <div>
          <Text strong className='text-xs'>
            Type
          </Text>
          <div className='mt-1'>
            <Button.Group size='small' className='w-full flex flex-col gap-1'>
              <Button
                type={filterType === 'all' ? 'primary' : 'default'}
                onClick={() => setFilterType('all')}
                className='w-full'
              >
                All Types
              </Button>
              <Button
                type={filterType === 4 ? 'primary' : 'default'}
                onClick={() => setFilterType(4)}
                className='w-full'
              >
                Orders
              </Button>
              <Button
                type={filterType === 5 ? 'primary' : 'default'}
                onClick={() => setFilterType(5)}
                className='w-full'
              >
                Users
              </Button>
              <Button
                type={filterType === 6 ? 'primary' : 'default'}
                onClick={() => setFilterType(6)}
                className='w-full'
              >
                Staff
              </Button>
              <Button
                type={filterType === 7 ? 'primary' : 'default'}
                onClick={() => setFilterType(7)}
                className='w-full'
              >
                Counters
              </Button>
              <Button
                type={filterType === 0 ? 'primary' : 'default'}
                onClick={() => setFilterType(0)}
                className='w-full'
              >
                System
              </Button>
            </Button.Group>
          </div>
        </div>
      </Space>
    </div>
  );

  return (
    <Drawer
      title={
        <div className='flex items-center justify-between'>
          <Space>
            <BellOutlined />
            <span>Notifications</span>
            {unreadCount > 0 && <Badge count={unreadCount} size='small' />}
          </Space>
          <Button
            type='text'
            icon={<CloseOutlined />}
            onClick={onClose}
            size='small'
          />
        </div>
      }
      placement={placement}
      width={width}
      open={open}
      onClose={onClose}
      closable={false}
      bodyStyle={{ padding: 0 }}
      headerStyle={{ borderBottom: '1px solid #f0f0f0' }}
    >
      <div className='flex flex-col h-full'>
        {/* Connection Status */}
        <div className='p-4 border-b border-gray-100'>
          <ConnectionStatus />
        </div>

        {/* Actions */}
        <div className='p-4 border-b border-gray-100'>
          <div className='flex items-center justify-between mb-3'>
            <Space>
              <Tooltip title='Mark all as read'>
                <Button
                  type='text'
                  icon={<CheckOutlined />}
                  onClick={handleMarkAllAsRead}
                  disabled={unreadCount === 0}
                  size='small'
                />
              </Tooltip>

              <Tooltip title='Clear all'>
                <Button
                  type='text'
                  icon={<ClearOutlined />}
                  onClick={clearNotifications}
                  disabled={notifications.length === 0}
                  size='small'
                />
              </Tooltip>
            </Space>

            <Space>
              <Dropdown
                overlay={filterMenu}
                trigger={['click']}
                placement='bottomRight'
              >
                <Button type='text' icon={<FilterOutlined />} size='small' />
              </Dropdown>

              <Dropdown
                overlay={settingsMenu}
                trigger={['click']}
                placement='bottomRight'
              >
                <Button type='text' icon={<SettingOutlined />} size='small' />
              </Dropdown>
            </Space>
          </div>

          {/* Tabs */}
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            size='small'
            className='notification-tabs'
          >
            <TabPane
              tab={
                <Badge
                  count={notifications.length}
                  size='small'
                  offset={[10, 0]}
                >
                  All
                </Badge>
              }
              key='all'
            />
            <TabPane
              tab={
                <Badge count={unreadCount} size='small' offset={[10, 0]}>
                  Unread
                </Badge>
              }
              key='unread'
            />
            <TabPane
              tab={
                <Badge
                  count={notifications.length - unreadCount}
                  size='small'
                  offset={[10, 0]}
                >
                  Read
                </Badge>
              }
              key='read'
            />
          </Tabs>
        </div>

        {/* Notifications List */}
        <div className='flex-1 overflow-y-auto'>
          {filteredNotifications.length === 0 ? (
            <div className='p-8'>
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={
                  activeTab === 'unread'
                    ? 'No unread notifications'
                    : activeTab === 'read'
                      ? 'No read notifications'
                      : 'No notifications'
                }
              />
            </div>
          ) : (
            <div className='p-4 space-y-3'>
              {filteredNotifications.map((notification) => (
                <NotificationItem
                  key={notification.notificationId}
                  notification={notification}
                  onMarkAsRead={handleMarkAsRead}
                  onRemove={handleRemove}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </Drawer>
  );
};

export default NotificationPanel;
