import { BellOutlined, EyeOutlined } from '@ant-design/icons';
import {
  Badge,
  Button,
  Dropdown,
  Typography,
  Space,
  Divider,
  Empty,
} from 'antd';
import React from 'react';
import ConnectionStatus from './ConnectionStatus';
import NotificationItem from './NotificationItem';
import { useSignalR } from '@/hooks/useSignalR';
import { useSignalRNotificationStore } from '@/stores/useSignalRNotificationStore';

const { Title } = Typography;

interface NotificationDropdownProps {
  onOpenPanel?: () => void;
  maxDisplayItems?: number;
  isMobile?: boolean;
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({
  onOpenPanel,
  maxDisplayItems = 5,
  isMobile = false,
}) => {
  const [dropdownOpen, setDropdownOpen] = React.useState(false);

  const { notifications, unreadCount, removeNotification } =
    useSignalRNotificationStore();
  const { markNotificationAsRead } = useSignalR();

  // Get recent notifications for dropdown
  const recentNotifications = notifications.slice(0, maxDisplayItems);
  const hasMoreNotifications = notifications.length > maxDisplayItems;

  const handleMarkAsRead = async (notificationId: string) => {
    await markNotificationAsRead(notificationId);
  };

  const handleRemove = (notificationId: string) => {
    removeNotification(notificationId);
  };

  const handleOpenPanel = () => {
    setDropdownOpen(false); // Close dropdown first
    if (onOpenPanel) {
      onOpenPanel();
    }
  };

  const dropdownContent = (
    <div className='w-96 max-w-[90vw] bg-white rounded-lg shadow-lg border border-gray-200'>
      {/* Header */}
      <div className='p-4 border-b border-gray-100'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <BellOutlined className='text-blue-600' />
            <Title level={5} className='!mb-0'>
              Notifications
            </Title>
            {unreadCount > 0 && <Badge count={unreadCount} size='small' />}
          </div>

          <Space>
            {onOpenPanel && (
              <Button
                type='text'
                size='small'
                icon={<EyeOutlined />}
                onClick={handleOpenPanel}
                className='text-blue-600 hover:bg-blue-50'
              >
                View All
              </Button>
            )}
          </Space>
        </div>
      </div>

      {/* Connection Status */}
      <div className='p-3 border-b border-gray-100'>
        <ConnectionStatus />
      </div>

      {/* Notifications List */}
      <div className='max-h-96 overflow-y-auto'>
        {recentNotifications.length === 0 ? (
          <div className='p-6'>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description='No notifications'
              className='!my-0'
            />
          </div>
        ) : (
          <div className='p-3 space-y-2'>
            {recentNotifications.map((notification) => (
              <NotificationItem
                key={notification.notificationId}
                notification={notification}
                onMarkAsRead={handleMarkAsRead}
                onRemove={handleRemove}
                compact
                showActions={!isMobile}
              />
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {hasMoreNotifications && onOpenPanel && (
        <>
          <Divider className='!my-0' />
          <div className='p-3 text-center'>
            <Button
              type='link'
              onClick={handleOpenPanel}
              className='text-blue-600 hover:text-blue-800'
            >
              View {notifications.length - maxDisplayItems} more notifications
            </Button>
          </div>
        </>
      )}
    </div>
  );

  return (
    <Dropdown
      overlay={dropdownContent}
      trigger={['click']}
      placement={isMobile ? 'bottomCenter' : 'bottomRight'}
      overlayClassName='notification-dropdown'
      open={dropdownOpen}
      onOpenChange={setDropdownOpen}
    >
      <Badge count={unreadCount} size='small' offset={[0, 0]}>
        <Button
          type='text'
          icon={<BellOutlined />}
          size={isMobile ? 'middle' : 'large'}
          className={`
            relative transition-all duration-200
            ${
              unreadCount > 0
                ? 'text-blue-600 hover:bg-blue-50'
                : 'text-gray-600 hover:bg-gray-50'
            }
            ${unreadCount > 0 ? 'animate-pulse' : ''}
          `}
        />
      </Badge>
    </Dropdown>
  );
};

export default NotificationDropdown;
