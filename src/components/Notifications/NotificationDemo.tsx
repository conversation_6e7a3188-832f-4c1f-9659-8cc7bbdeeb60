import { BellOutlined, SendOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>, Space, Typography, Divider, Select } from 'antd';
import React from 'react';
import { useSignalRConnectionState } from '@/hooks/useSignalR';
import { useSignalRNotificationStore } from '@/stores/useSignalRNotificationStore';
import type {
  NotificationMessageKey,
  NotificationType,
  NotificationSeverity,
} from '@/types/notifications';

const { Text } = Typography;
const { Option } = Select;

const NotificationDemo: React.FC = () => {
  const { addNotification } = useSignalRNotificationStore();
  const { isConnected } = useSignalRConnectionState();

  const [demoType, setDemoType] = React.useState<NotificationType>(4); // Order
  const [demoSeverity, setDemoSeverity] =
    React.useState<NotificationSeverity>(1); // Success

  const generateDemoNotification = () => {
    const demoNotifications = [
      {
        messageKey: 400 as NotificationMessageKey, // Order_CreatedComplete
        type: 4 as NotificationType, // Order
        severity: 1 as NotificationSeverity, // Success
        metadata: {
          orderId:
            'ORD-' + Math.random().toString(36).substr(2, 9).toUpperCase(),
        },
        title: 'Order Created Successfully',
        description:
          'A new order has been created and is ready for processing.',
      },
      {
        messageKey: 401 as NotificationMessageKey, // Order_CreatedFail
        type: 4 as NotificationType, // Order
        severity: 3 as NotificationSeverity, // Error
        metadata: { error: 'Insufficient inventory' },
        title: 'Order Creation Failed',
        description: 'Failed to create order due to insufficient inventory.',
      },
      {
        messageKey: 500 as NotificationMessageKey, // User_Registered
        type: 5 as NotificationType, // User
        severity: 0 as NotificationSeverity, // Info
        metadata: {
          userId:
            'USR-' + Math.random().toString(36).substr(2, 9).toUpperCase(),
        },
        title: 'New User Registration',
        description: 'A new user has registered and requires verification.',
      },
      {
        messageKey: 600 as NotificationMessageKey, // Staff_Added
        type: 6 as NotificationType, // Staff
        severity: 1 as NotificationSeverity, // Success
        metadata: {
          staffId:
            'STF-' + Math.random().toString(36).substr(2, 9).toUpperCase(),
        },
        title: 'Staff Member Added',
        description: 'A new staff member has been added to the system.',
      },
      {
        messageKey: 200 as NotificationMessageKey, // System_Error
        type: 0 as NotificationType, // System
        severity: 3 as NotificationSeverity, // Error
        metadata: { errorCode: 'SYS_ERR_001' },
        title: 'System Error',
        description:
          'A critical system error has occurred and requires immediate attention.',
      },
    ];

    const randomNotification =
      demoNotifications[Math.floor(Math.random() * demoNotifications.length)];

    const notification = {
      notificationId:
        'demo-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
      messageKey: randomNotification.messageKey,
      type: demoType,
      severity: demoSeverity,
      metadata: randomNotification.metadata,
      isRead: false,
      isBroadcast: Math.random() > 0.7, // 30% chance of being broadcast
      createdAt: new Date().toISOString(),
      title: randomNotification.title,
      description: randomNotification.description,
      actionUrl: getActionUrl(demoType),
      actionText: getActionText(demoType),
      autoClose: demoSeverity !== 3, // Don't auto-close errors
      duration: demoSeverity === 3 ? 0 : 5000,
    };

    addNotification(notification);
  };

  const getActionUrl = (type: NotificationType): string => {
    switch (type) {
      case 4:
        return '/orders';
      case 5:
        return '/user-management';
      case 6:
        return '/management';
      case 7:
        return '/management';
      default:
        return '/dashboard';
    }
  };

  const getActionText = (type: NotificationType): string => {
    switch (type) {
      case 4:
        return 'View Orders';
      case 5:
        return 'View Users';
      case 6:
        return 'View Staff';
      case 7:
        return 'View Counters';
      default:
        return 'View Dashboard';
    }
  };

  const generateMultipleNotifications = () => {
    const count = Math.floor(Math.random() * 5) + 3; // 3-7 notifications
    for (let i = 0; i < count; i++) {
      setTimeout(() => {
        generateDemoNotification();
      }, i * 500); // Stagger notifications by 500ms
    }
  };

  return (
    <Card
      title={
        <Space>
          <BellOutlined />
          <span>SignalR Notification Demo</span>
        </Space>
      }
      className='w-full max-w-md'
    >
      <Space direction='vertical' className='w-full'>
        <div>
          <Text strong>Connection Status: </Text>
          <Text
            type={isConnected ? 'success' : 'danger'}
            className='font-medium'
          >
            {isConnected ? 'Connected' : 'Disconnected'}
          </Text>
        </div>

        <Divider />

        <div>
          <Text strong className='block mb-2'>
            Demo Settings
          </Text>

          <Space direction='vertical' className='w-full'>
            <div>
              <Text className='block mb-1'>Notification Type:</Text>
              <Select
                value={demoType}
                onChange={setDemoType}
                className='w-full'
                size='small'
              >
                <Option value={0}>System</Option>
                <Option value={1}>Warning</Option>
                <Option value={2}>Info</Option>
                <Option value={3}>Announcement</Option>
                <Option value={4}>Order</Option>
                <Option value={5}>User</Option>
                <Option value={6}>Staff</Option>
                <Option value={7}>Counter</Option>
              </Select>
            </div>

            <div>
              <Text className='block mb-1'>Severity:</Text>
              <Select
                value={demoSeverity}
                onChange={setDemoSeverity}
                className='w-full'
                size='small'
              >
                <Option value={0}>Info</Option>
                <Option value={1}>Success</Option>
                <Option value={2}>Warning</Option>
                <Option value={3}>Error</Option>
              </Select>
            </div>
          </Space>
        </div>

        <Divider />

        <Space direction='vertical' className='w-full'>
          <Button
            type='primary'
            icon={<SendOutlined />}
            onClick={generateDemoNotification}
            className='w-full'
          >
            Send Demo Notification
          </Button>

          <Button
            type='default'
            onClick={generateMultipleNotifications}
            className='w-full'
          >
            Send Multiple Notifications
          </Button>
        </Space>

        {!isConnected && (
          <Text type='secondary' className='text-xs text-center block'>
            Connect to SignalR to send demo notifications
          </Text>
        )}
      </Space>
    </Card>
  );
};

export default NotificationDemo;
