import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { notification } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { useSignalR } from '@/hooks/useSignalR';
import { useSignalRNotificationStore } from '@/stores/useSignalRNotificationStore';
import type {
  UINotification,
  NotificationSeverity,
} from '@/types/notifications';

const NotificationToast: React.FC = () => {
  const { notifications } = useSignalRNotificationStore();
  const { markNotificationAsRead } = useSignalR();
  const [processedNotifications, setProcessedNotifications] = useState<
    Set<string>
  >(new Set());

  // Get severity config for toast styling
  const getSeverityConfig = (severity: NotificationSeverity) => {
    switch (severity) {
      case 0: // Info
        return {
          type: 'info' as const,
          icon: <InfoCircleOutlined className='text-blue-500' />,
          duration: 4.5,
        };
      case 1: // Success
        return {
          type: 'success' as const,
          icon: <CheckCircleOutlined className='text-green-500' />,
          duration: 4.5,
        };
      case 2: // Warning
        return {
          type: 'warning' as const,
          icon: <WarningOutlined className='text-orange-500' />,
          duration: 6,
        };
      case 3: // Error
        return {
          type: 'error' as const,
          icon: <ExclamationCircleOutlined className='text-red-500' />,
          duration: 0, // Don't auto close errors
        };
      default:
        return {
          type: 'info' as const,
          icon: <InfoCircleOutlined className='text-blue-500' />,
          duration: 4.5,
        };
    }
  };

  const getDefaultTitle = useCallback((notif: UINotification) => {
    switch (notif.type) {
      case 4:
        return 'Order Update';
      case 5:
        return 'User Update';
      case 6:
        return 'Staff Update';
      case 7:
        return 'Counter Update';
      case 0:
        return 'System Notification';
      case 1:
        return 'Warning';
      case 2:
        return 'Information';
      case 3:
        return 'Announcement';
      default:
        return 'Notification';
    }
  }, []);

  const getDefaultDescription = useCallback((notif: UINotification): string => {
    switch (notif.messageKey) {
      case 400:
        return 'Order has been created successfully';
      case 401:
        return 'Failed to create order';
      case 402:
        return 'Order status has been updated';
      case 500:
        return 'New user has been registered';
      case 600:
        return 'New staff member has been added';
      case 700:
        return 'New counter has been created';
      case 200:
        return 'System error occurred';
      default:
        return notif.metadata?.description || 'You have a new notification';
    }
  }, []);

  // Get notification title and description
  const getNotificationContent = useCallback(
    (notif: UINotification) => {
      const title = notif.title || getDefaultTitle(notif);
      const description = notif.description || getDefaultDescription(notif);
      return { title, description };
    },
    [getDefaultTitle, getDefaultDescription],
  );

  // Handle marking notification as read
  const handleMarkAsRead = useCallback(
    async (notificationId: string) => {
      try {
        await markNotificationAsRead(notificationId);
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    },
    [markNotificationAsRead],
  );

  // Show toast notifications for new unread notifications
  useEffect(() => {
    const newNotifications = notifications.filter(
      (notif) =>
        !notif.isRead && !processedNotifications.has(notif.notificationId),
    );

    newNotifications.forEach((notif) => {
      const severityConfig = getSeverityConfig(notif.severity);
      const { title, description } = getNotificationContent(notif);

      // Show toast notification
      notification[severityConfig.type]({
        message: title,
        description: description,
        icon: severityConfig.icon,
        duration: severityConfig.duration,
        placement: 'topRight',
        className: 'notification-toast',
        style: {
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        },
        btn: notif.actionUrl ? (
          <div className='flex gap-2 mt-2'>
            {notif.actionText && (
              <button
                className='px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors'
                onClick={() => {
                  window.location.href = notif.actionUrl!;
                  handleMarkAsRead(notif.notificationId);
                }}
              >
                {notif.actionText}
              </button>
            )}
            <button
              className='px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors'
              onClick={() => handleMarkAsRead(notif.notificationId)}
            >
              Mark as Read
            </button>
          </div>
        ) : (
          <button
            className='px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors mt-2'
            onClick={() => handleMarkAsRead(notif.notificationId)}
          >
            Mark as Read
          </button>
        ),
        onClose: () => {
          // Auto mark as read when toast is closed (if not error)
          if (notif.severity < 3) {
            handleMarkAsRead(notif.notificationId);
          }
        },
      });

      // Mark as processed
      setProcessedNotifications(
        (prev) => new Set([...prev, notif.notificationId]),
      );
    });
  }, [
    notifications,
    processedNotifications,
    markNotificationAsRead,
    getNotificationContent,
    handleMarkAsRead,
  ]);

  // Clean up processed notifications that are no longer in the store
  useEffect(() => {
    const currentNotificationIds = new Set(
      notifications.map((n) => n.notificationId),
    );
    setProcessedNotifications(
      (prev) =>
        new Set([...prev].filter((id) => currentNotificationIds.has(id))),
    );
  }, [notifications]);

  return null; // This component doesn't render anything visible
};

export default NotificationToast;
