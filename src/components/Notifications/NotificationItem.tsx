import {
  CheckOutlined,
  CloseOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  BellOutlined,
  UserOutlined,
  ShoppingCartOutlined,
  TeamOutlined,
  DesktopOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { Avatar, Button, Card, Typography, Tag, Space, Descriptions, Divider } from 'antd';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import {NotificationSeverity, NotificationType, type UINotification} from "@/types";


dayjs.extend(relativeTime);

const { Text, Title } = Typography;

interface NotificationItemProps {
  notification: UINotification;
  onMarkAsRead?: (notificationId: string) => void;
  onRemove?: (notificationId: string) => void;
  onAction?: (notification: UINotification) => void;
  compact?: boolean;
  showActions?: boolean;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
  onRemove,
  onAction,
  compact = false,
  showActions = true,
}) => {
  const navigate = useNavigate();
  console.log(notification)
  // Get severity color and icon
  const getSeverityConfig = (severity: NotificationSeverity) => {
    switch (severity) {
      case NotificationSeverity.Info:
        return {
          color: 'blue',
          icon: <InfoCircleOutlined className='text-blue-500' />,
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
        };
      case NotificationSeverity.Success:
        return {
          color: 'green',
          icon: <CheckOutlined className='text-green-500' />,
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
        };
      case NotificationSeverity.Warning:
        return {
          color: 'orange',
          icon: <WarningOutlined className='text-orange-500' />,
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
        };
      case NotificationSeverity.Error:
        return {
          color: 'red',
          icon: <ExclamationCircleOutlined className='text-red-500' />,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
        };
      default:
        return {
          color: 'gray',
          icon: <BellOutlined className='text-gray-500' />,
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
        };
    }
  };

  // Get type icon and color
  const getTypeConfig = (type: NotificationType) => {
    switch (type) {
      case NotificationType.Order:
        return {
          icon: <ShoppingCartOutlined className='text-blue-600' />,
          color: 'blue',
          bgColor: 'bg-blue-100',
        };
      case NotificationType.User:
        return {
          icon: <UserOutlined className='text-green-600' />,
          color: 'green',
          bgColor: 'bg-green-100',
        };
      case NotificationType.Staff:
        return {
          icon: <TeamOutlined className='text-purple-600' />,
          color: 'purple',
          bgColor: 'bg-purple-100',
        };
      case NotificationType.Counter:
        return {
          icon: <DesktopOutlined className='text-orange-600' />,
          color: 'orange',
          bgColor: 'bg-orange-100',
        };
      case NotificationType.System:
        return {
          icon: <SettingOutlined className='text-gray-600' />,
          color: 'gray',
          bgColor: 'bg-gray-100',
        };
      case NotificationType.Warning:
        return {
          icon: <WarningOutlined className='text-yellow-600' />,
          color: 'yellow',
          bgColor: 'bg-yellow-100',
        };
      case NotificationType.Info:
        return {
          icon: <InfoCircleOutlined className='text-blue-600' />,
          color: 'blue',
          bgColor: 'bg-blue-100',
        };
      case NotificationType.Announcement:
        return {
          icon: <BellOutlined className='text-indigo-600' />,
          color: 'indigo',
          bgColor: 'bg-indigo-100',
        };
      default:
        return {
          icon: <BellOutlined className='text-gray-600' />,
          color: 'gray',
          bgColor: 'bg-gray-100',
        };
    }
  };

  // Get type label
  const getTypeLabel = (type: NotificationType) => {
    switch (type) {
      case NotificationType.Order:
        return 'Order';
      case NotificationType.User:
        return 'User';
      case NotificationType.Staff:
        return 'Staff';
      case NotificationType.Counter:
        return 'Counter';
      case NotificationType.System:
        return 'System';
      case NotificationType.Warning:
        return 'Warning';
      case NotificationType.Info:
        return 'Info';
      case NotificationType.Announcement:
        return 'Announcement';
      default:
        return 'Notification';
    }
  };

  const severityConfig = getSeverityConfig(notification.severity);
  const typeConfig = getTypeConfig(notification.type);
  const typeLabel = getTypeLabel(notification.type);

  const handleAction = () => {
    if (onAction) {
      onAction(notification);
    } else if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }

    if (!notification.isRead && onMarkAsRead) {
      onMarkAsRead(notification.notificationId);
    }
  };

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMarkAsRead) {
      onMarkAsRead(notification.notificationId);
    }
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onRemove) {
      onRemove(notification.notificationId);
    }
  };

  if (compact) {
    return (
      <div
        className={`
          flex items-center gap-3 p-3 rounded-lg border transition-all duration-200 cursor-pointer
          ${notification.isRead ? 'bg-gray-50 border-gray-200' : `${severityConfig.bgColor} ${severityConfig.borderColor}`}
          hover:shadow-md hover:scale-[1.02]
        `}
        onClick={handleAction}
      >
        <Avatar
          size='small'
          icon={typeConfig.icon}
          className={`flex-shrink-0 ${typeConfig.bgColor}`}
        />

        <div className='flex-1 min-w-0'>
          <div className='flex items-center gap-2 mb-1'>
            <Text strong className='text-sm truncate'>
              {notification.title}
            </Text>
            {!notification.isRead && (
              <div className='w-2 h-2 bg-blue-500 rounded-full flex-shrink-0' />
            )}
          </div>
          <Text type='secondary' className='text-xs'>
            {dayjs(notification.createdAt).fromNow()}
          </Text>
        </div>

        {showActions && (
          <div className='flex items-center gap-1'>
            {!notification.isRead && (
              <Button
                type='text'
                size='small'
                icon={<CheckOutlined />}
                onClick={handleMarkAsRead}
                className='text-green-600 hover:bg-green-100'
              />
            )}
            <Button
              type='text'
              size='small'
              icon={<CloseOutlined />}
              onClick={handleRemove}
              className='text-gray-400 hover:bg-gray-100'
            />
          </div>
        )}
      </div>
    );
  }

  return (
    <Card
      className={`
        transition-all duration-200 cursor-pointer border-l-4
        ${notification.isRead ? 'bg-gray-50 border-l-gray-300' : `${severityConfig.bgColor} border-l-${severityConfig.color}-500`}
        hover:shadow-lg hover:scale-[1.02]
      `}
      styles={{ body: { padding: '16px' } }}
      onClick={handleAction}
    >
      <div className='flex items-start gap-3'>
        <Avatar
          size='large'
          icon={typeConfig.icon}
          className={`flex-shrink-0 mt-1 ${typeConfig.bgColor}`}
        />

        <div className='flex-1 min-w-0'>
          <div className='flex items-center justify-between mb-2'>
            <div className='flex items-center gap-2'>
              <Title level={5} className='!mb-0 !text-sm'>
                {notification.title}
              </Title>
              {!notification.isRead && (
                <div className='w-2 h-2 bg-blue-500 rounded-full' />
              )}
            </div>

            <div className='flex items-center gap-2'>
              <Tag color={severityConfig.color} className='text-xs'>
                {typeLabel}
              </Tag>
              {showActions && (
                <Space size='small'>
                  {!notification.isRead && (
                    <Button
                      type='text'
                      size='small'
                      icon={<CheckOutlined />}
                      onClick={handleMarkAsRead}
                      className='text-green-600 hover:bg-green-100'
                      title='Mark as read'
                    />
                  )}
                  <Button
                    type='text'
                    size='small'
                    icon={<CloseOutlined />}
                    onClick={handleRemove}
                    className='text-gray-400 hover:bg-gray-100'
                    title='Remove'
                  />
                </Space>
              )}
            </div>
          </div>

          <Text className='text-sm text-gray-700 block mb-2'>
            {notification.description || 'You have a new notification'}
          </Text>

          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              {severityConfig.icon}
              <Text type='secondary' className='text-xs'>
                {dayjs(notification.createdAt).format('MMM DD, YYYY HH:mm')}
              </Text>
            </div>

            {notification.actionText && notification.actionUrl && (
              <Button
                type='link'
                size='small'
                className='p-0 h-auto text-xs'
                onClick={(e) => {
                  e.stopPropagation();
                  handleAction();
                }}
              >
                {notification.actionText}
              </Button>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};



export default NotificationItem;
