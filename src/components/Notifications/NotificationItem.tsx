import {
  CheckOutlined,
  CloseOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  BellOutlined,
  UserOutlined,
  ShoppingCartOutlined,
  TeamOutlined,
  DesktopOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { Avatar, Button, Card, Typography, Tag, Space } from 'antd';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import type {
  UINotification,
  NotificationSeverity,
  NotificationType,
} from '@/types/notifications';

dayjs.extend(relativeTime);

const { Text, Title } = Typography;

interface NotificationItemProps {
  notification: UINotification;
  onMarkAsRead?: (notificationId: string) => void;
  onRemove?: (notificationId: string) => void;
  onAction?: (notification: UINotification) => void;
  compact?: boolean;
  showActions?: boolean;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
  onRemove,
  onAction,
  compact = false,
  showActions = true,
}) => {
  const navigate = useNavigate();

  // Get severity color and icon
  const getSeverityConfig = (severity: NotificationSeverity) => {
    switch (severity) {
      case 0: // Info
        return {
          color: 'blue',
          icon: <InfoCircleOutlined className='text-blue-500' />,
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
        };
      case 1: // Success
        return {
          color: 'green',
          icon: <CheckOutlined className='text-green-500' />,
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
        };
      case 2: // Warning
        return {
          color: 'orange',
          icon: <WarningOutlined className='text-orange-500' />,
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
        };
      case 3: // Error
        return {
          color: 'red',
          icon: <ExclamationCircleOutlined className='text-red-500' />,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
        };
      default:
        return {
          color: 'gray',
          icon: <BellOutlined className='text-gray-500' />,
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
        };
    }
  };

  // Get type icon
  const getTypeIcon = (type: NotificationType) => {
    switch (type) {
      case 4: // Order
        return <ShoppingCartOutlined className='text-blue-600' />;
      case 5: // User
        return <UserOutlined className='text-green-600' />;
      case 6: // Staff
        return <TeamOutlined className='text-purple-600' />;
      case 7: // Counter
        return <DesktopOutlined className='text-orange-600' />;
      case 0: // System
        return <SettingOutlined className='text-gray-600' />;
      default:
        return <BellOutlined className='text-blue-600' />;
    }
  };

  // Get type label
  const getTypeLabel = (type: NotificationType) => {
    switch (type) {
      case 4:
        return 'Order';
      case 5:
        return 'User';
      case 6:
        return 'Staff';
      case 7:
        return 'Counter';
      case 0:
        return 'System';
      case 1:
        return 'Warning';
      case 2:
        return 'Info';
      case 3:
        return 'Announcement';
      default:
        return 'Notification';
    }
  };

  const severityConfig = getSeverityConfig(notification.severity);
  const typeIcon = getTypeIcon(notification.type);
  const typeLabel = getTypeLabel(notification.type);

  const handleAction = () => {
    if (onAction) {
      onAction(notification);
    } else if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }

    if (!notification.isRead && onMarkAsRead) {
      onMarkAsRead(notification.notificationId);
    }
  };

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMarkAsRead) {
      onMarkAsRead(notification.notificationId);
    }
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onRemove) {
      onRemove(notification.notificationId);
    }
  };

  if (compact) {
    return (
      <div
        className={`
          flex items-center gap-3 p-3 rounded-lg border transition-all duration-200 cursor-pointer
          ${notification.isRead ? 'bg-gray-50 border-gray-200' : `${severityConfig.bgColor} ${severityConfig.borderColor}`}
          hover:shadow-md hover:scale-[1.02]
        `}
        onClick={handleAction}
      >
        <Avatar size='small' icon={typeIcon} className='flex-shrink-0' />

        <div className='flex-1 min-w-0'>
          <div className='flex items-center gap-2 mb-1'>
            <Text strong className='text-sm truncate'>
              {notification.title}
            </Text>
            {!notification.isRead && (
              <div className='w-2 h-2 bg-blue-500 rounded-full flex-shrink-0' />
            )}
          </div>
          <Text type='secondary' className='text-xs'>
            {dayjs(notification.createdAt).fromNow()}
          </Text>
        </div>

        {showActions && (
          <div className='flex items-center gap-1'>
            {!notification.isRead && (
              <Button
                type='text'
                size='small'
                icon={<CheckOutlined />}
                onClick={handleMarkAsRead}
                className='text-green-600 hover:bg-green-100'
              />
            )}
            <Button
              type='text'
              size='small'
              icon={<CloseOutlined />}
              onClick={handleRemove}
              className='text-gray-400 hover:bg-gray-100'
            />
          </div>
        )}
      </div>
    );
  }

  return (
    <Card
      className={`
        transition-all duration-200 cursor-pointer border-l-4
        ${notification.isRead ? 'bg-gray-50 border-l-gray-300' : `${severityConfig.bgColor} border-l-${severityConfig.color}-500`}
        hover:shadow-lg hover:scale-[1.02]
      `}
      styles={{ body: { padding: '16px' } }}
      onClick={handleAction}
    >
      <div className='flex items-start gap-3'>
        <Avatar size='large' icon={typeIcon} className='flex-shrink-0 mt-1' />

        <div className='flex-1 min-w-0'>
          <div className='flex items-center justify-between mb-2'>
            <div className='flex items-center gap-2'>
              <Title level={5} className='!mb-0 !text-sm'>
                {notification.title}
              </Title>
              {!notification.isRead && (
                <div className='w-2 h-2 bg-blue-500 rounded-full' />
              )}
            </div>

            <div className='flex items-center gap-2'>
              <Tag color={severityConfig.color} className='text-xs'>
                {typeLabel}
              </Tag>
              {showActions && (
                <Space size='small'>
                  {!notification.isRead && (
                    <Button
                      type='text'
                      size='small'
                      icon={<CheckOutlined />}
                      onClick={handleMarkAsRead}
                      className='text-green-600 hover:bg-green-100'
                      title='Mark as read'
                    />
                  )}
                  <Button
                    type='text'
                    size='small'
                    icon={<CloseOutlined />}
                    onClick={handleRemove}
                    className='text-gray-400 hover:bg-gray-100'
                    title='Remove'
                  />
                </Space>
              )}
            </div>
          </div>

          <Text className='text-sm text-gray-700 block mb-2'>
            {notification.description || getDefaultDescription(notification)}
          </Text>

          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              {severityConfig.icon}
              <Text type='secondary' className='text-xs'>
                {dayjs(notification.createdAt).format('MMM DD, YYYY HH:mm')}
              </Text>
            </div>

            {notification.actionText && notification.actionUrl && (
              <Button
                type='link'
                size='small'
                className='p-0 h-auto text-xs'
                onClick={(e) => {
                  e.stopPropagation();
                  handleAction();
                }}
              >
                {notification.actionText}
              </Button>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

// Helper function to get default description based on message key
const getDefaultDescription = (notification: UINotification): string => {
  switch (notification.messageKey) {
    case 400:
      return 'Order has been created successfully';
    case 401:
      return 'Failed to create order';
    case 402:
      return 'Order status has been updated';
    case 500:
      return 'New user has been registered';
    case 600:
      return 'New staff member has been added';
    case 700:
      return 'New counter has been created';
    case 200:
      return 'System error occurred';
    default:
      return (
        notification.metadata?.description || 'You have a new notification'
      );
  }
};

export default NotificationItem;
