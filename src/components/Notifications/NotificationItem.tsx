import {
  CheckOutlined,
  CloseOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  BellOutlined,
  UserOutlined,
  ShoppingCartOutlined,
  TeamOutlined,
  DesktopOutlined,
  SettingOutlined,
  LinkOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  Card,
  Typography,
  Tag,
} from 'antd';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  NotificationSeverity,
  NotificationType,
  type UINotification,
} from '@/types';

dayjs.extend(relativeTime);

const { Text, Title } = Typography;

interface NotificationItemProps {
  notification: UINotification;
  onMarkAsRead?: (notificationId: string) => void;
  onRemove?: (notificationId: string) => void;
  onAction?: (notification: UINotification) => void;
  compact?: boolean;
  showActions?: boolean;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
  onRemove,
  onAction,
  compact = false,
  showActions = true,
}) => {
  const navigate = useNavigate();
  console.log(notification);
  // Get severity color and icon
  const getSeverityConfig = (severity: NotificationSeverity) => {
    switch (severity) {
      case NotificationSeverity.Info:
        return {
          color: 'blue',
          icon: <InfoCircleOutlined className='text-blue-500' />,
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
        };
      case NotificationSeverity.Success:
        return {
          color: 'green',
          icon: <CheckOutlined className='text-green-500' />,
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
        };
      case NotificationSeverity.Warning:
        return {
          color: 'orange',
          icon: <WarningOutlined className='text-orange-500' />,
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
        };
      case NotificationSeverity.Error:
        return {
          color: 'red',
          icon: <ExclamationCircleOutlined className='text-red-500' />,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
        };
      default:
        return {
          color: 'gray',
          icon: <BellOutlined className='text-gray-500' />,
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
        };
    }
  };

  // Get type icon and color
  const getTypeConfig = (type: NotificationType) => {
    switch (type) {
      case NotificationType.Order:
        return {
          icon: <ShoppingCartOutlined className='text-blue-600' />,
          color: 'blue',
          bgColor: 'bg-blue-100',
        };
      case NotificationType.User:
        return {
          icon: <UserOutlined className='text-green-600' />,
          color: 'green',
          bgColor: 'bg-green-100',
        };
      case NotificationType.Staff:
        return {
          icon: <TeamOutlined className='text-purple-600' />,
          color: 'purple',
          bgColor: 'bg-purple-100',
        };
      case NotificationType.Counter:
        return {
          icon: <DesktopOutlined className='text-orange-600' />,
          color: 'orange',
          bgColor: 'bg-orange-100',
        };
      case NotificationType.System:
        return {
          icon: <SettingOutlined className='text-gray-600' />,
          color: 'gray',
          bgColor: 'bg-gray-100',
        };
      case NotificationType.Warning:
        return {
          icon: <WarningOutlined className='text-yellow-600' />,
          color: 'yellow',
          bgColor: 'bg-yellow-100',
        };
      case NotificationType.Info:
        return {
          icon: <InfoCircleOutlined className='text-blue-600' />,
          color: 'blue',
          bgColor: 'bg-blue-100',
        };
      case NotificationType.Announcement:
        return {
          icon: <BellOutlined className='text-indigo-600' />,
          color: 'indigo',
          bgColor: 'bg-indigo-100',
        };
      default:
        return {
          icon: <BellOutlined className='text-gray-600' />,
          color: 'gray',
          bgColor: 'bg-gray-100',
        };
    }
  };

  // Get type label
  const getTypeLabel = (type: NotificationType) => {
    switch (type) {
      case NotificationType.Order:
        return 'Order';
      case NotificationType.User:
        return 'User';
      case NotificationType.Staff:
        return 'Staff';
      case NotificationType.Counter:
        return 'Counter';
      case NotificationType.System:
        return 'System';
      case NotificationType.Warning:
        return 'Warning';
      case NotificationType.Info:
        return 'Info';
      case NotificationType.Announcement:
        return 'Announcement';
      default:
        return 'Notification';
    }
  };

  const severityConfig = getSeverityConfig(notification.severity);
  const typeConfig = getTypeConfig(notification.type);
  const typeLabel = getTypeLabel(notification.type);

  // Helper function to get border color classes (to avoid dynamic class issues)
  const getBorderColorClass = (color: string) => {
    const colorMap = {
      blue: 'border-l-blue-500 bg-blue-50',
      green: 'border-l-green-500 bg-green-50',
      orange: 'border-l-orange-500 bg-orange-50',
      red: 'border-l-red-500 bg-red-50',
      gray: 'border-l-gray-500 bg-gray-50',
      yellow: 'border-l-yellow-500 bg-yellow-50',
      purple: 'border-l-purple-500 bg-purple-50',
      indigo: 'border-l-indigo-500 bg-indigo-50',
    };
    return (
      colorMap[color as keyof typeof colorMap] || 'border-l-gray-500 bg-gray-50'
    );
  };

  // Helper function to get button color classes
  const getButtonColorClass = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600',
      green:
        'bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600',
      orange:
        'bg-orange-500 hover:bg-orange-600 border-orange-500 hover:border-orange-600',
      red: 'bg-red-500 hover:bg-red-600 border-red-500 hover:border-red-600',
      gray: 'bg-gray-500 hover:bg-gray-600 border-gray-500 hover:border-gray-600',
      yellow:
        'bg-yellow-500 hover:bg-yellow-600 border-yellow-500 hover:border-yellow-600',
      purple:
        'bg-purple-500 hover:bg-purple-600 border-purple-500 hover:border-purple-600',
      indigo:
        'bg-indigo-500 hover:bg-indigo-600 border-indigo-500 hover:border-indigo-600',
    };
    return (
      colorMap[color as keyof typeof colorMap] ||
      'bg-gray-500 hover:bg-gray-600 border-gray-500 hover:border-gray-600'
    );
  };
  //
  // // Helper function to check if field is ID-related
  // const isIdField = (key: string) => {
  //   const idPatterns = ['id', 'Id', 'ID', 'code', 'Code', 'number', 'Number'];
  //   return idPatterns.some((pattern) =>
  //     key.toLowerCase().includes(pattern.toLowerCase()),
  //   );
  // };
  //
  // // Helper function to check if field is date-related
  // const isDateField = (key: string) => {
  //   const datePatterns = [
  //     'date',
  //     'Date',
  //     'time',
  //     'Time',
  //     'at',
  //     'At',
  //     'created',
  //     'updated',
  //     'modified',
  //   ];
  //   return datePatterns.some((pattern) =>
  //     key.toLowerCase().includes(pattern.toLowerCase()),
  //   );
  // };
  //
  // // Helper function to format date values
  // const formatDateValue = (value: any) => {
  //   try {
  //     const date = dayjs(value);
  //     if (date.isValid()) {
  //       return (
  //         <div className='space-y-1'>
  //           <div className='text-sm font-semibold text-gray-900'>
  //             {date.format('MMM DD, YYYY')}
  //           </div>
  //           <div className='text-xs text-gray-500'>
  //             {date.format('HH:mm:ss')} • {date.fromNow()}
  //           </div>
  //         </div>
  //       );
  //     }
  //   } catch (error) {
  //     // If not a valid date, return as string
  //   }
  //   return String(value);
  // };

  const handleAction = () => {
    if (onAction) {
      onAction(notification);
    } else if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }

    if (!notification.isRead && onMarkAsRead) {
      onMarkAsRead(notification.notificationId);
    }
  };

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMarkAsRead) {
      onMarkAsRead(notification.notificationId);
    }
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onRemove) {
      onRemove(notification.notificationId);
    }
  };

  if (compact) {
    return (
      <div
        className={`
          flex items-center gap-3 p-3 rounded-lg border transition-all duration-200 cursor-pointer
          ${notification.isRead ? 'bg-gray-50 border-gray-200' : `${severityConfig.bgColor} ${severityConfig.borderColor}`}
          hover:shadow-md hover:scale-[1.02]
        `}
        onClick={handleAction}
      >
        <Avatar
          size='small'
          icon={typeConfig.icon}
          className={`flex-shrink-0 ${typeConfig.bgColor}`}
        />

        <div className='flex-1 min-w-0'>
          <div className='flex items-center gap-2 mb-1'>
            <Text strong className='text-sm truncate'>
              {notification.title}
            </Text>
            {!notification.isRead && (
              <div className='w-2 h-2 bg-blue-500 rounded-full flex-shrink-0' />
            )}
          </div>
          <Text type='secondary' className='text-xs'>
            {dayjs(notification.createdAt).fromNow()}
          </Text>
        </div>

        {showActions && (
          <div className='flex items-center gap-1'>
            {!notification.isRead && (
              <Button
                type='text'
                size='small'
                icon={<CheckOutlined />}
                onClick={handleMarkAsRead}
                className='text-green-600 hover:bg-green-100'
              />
            )}
            <Button
              type='text'
              size='small'
              icon={<CloseOutlined />}
              onClick={handleRemove}
              className='text-gray-400 hover:bg-gray-100'
            />
          </div>
        )}
      </div>
    );
  }

  return (
    <Card
      className={`
        transition-all duration-300 cursor-pointer border-0 shadow-md
        ${notification.isRead ? 'bg-gray-50' : 'bg-white'}
        hover:shadow-xl hover:scale-[1.01]
      `}
      styles={{
        body: { padding: '0' },
      }}
      onClick={handleAction}
    >
      {/* Header Section */}
      <div
        className={`
        p-6 border-l-4
        ${
          notification.isRead
            ? 'border-l-gray-300 bg-gray-50'
            : getBorderColorClass(severityConfig.color)
        }
      `}
      >
        <div className='flex items-start gap-4'>
          {/*<Avatar*/}
          {/*  size={56}*/}
          {/*  icon={typeConfig.icon}*/}
          {/*  className={`flex-shrink-0 ${typeConfig.bgColor} border-2 border-white shadow-sm`}*/}
          {/*/>*/}

          <div className='flex-1 min-w-0'>
            {/* Title Row */}
            <div className='flex items-start justify-between mb-3'>
              <div className='flex items-center gap-3 flex-wrap'>
                <Title
                  level={4}
                  className='!mb-0 !text-lg font-semibold text-gray-900'
                >
                  {notification.title}
                </Title>
                {!notification.isRead && (
                  <div className='w-3 h-3 bg-blue-500 rounded-full animate-pulse' />
                )}
                <Tag
                  color={severityConfig.color}
                  className='text-xs font-medium px-2 py-1'
                >
                  {typeLabel}
                </Tag>
              </div>

              {/* Action Buttons */}
              {showActions && (
                <div className='flex items-center gap-2 ml-4 flex-shrink-0'>
                  {!notification.isRead && (
                    <Button
                      type='text'
                      size='small'
                      icon={<CheckOutlined />}
                      onClick={handleMarkAsRead}
                      className='text-green-600 hover:bg-green-100 hover:text-green-700 transition-colors'
                      title='Mark as read'
                    />
                  )}
                  <Button
                    type='text'
                    size='small'
                    icon={<CloseOutlined />}
                    onClick={handleRemove}
                    className='text-gray-400 hover:bg-gray-100 hover:text-gray-600 transition-colors'
                    title='Remove'
                  />
                </div>
              )}
            </div>

            {/* Description */}
            <div className='mb-4'>
              <Text className=' text-gray-800 text-sm leading-relaxed block'>
                {notification.description || 'You have a new notification'}
              </Text>
            </div>

            {/* Info Grid */}
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-4'>
              {/* Severity */}
              <div className='space-y-2'>
                <div className='flex items-center gap-2 text-gray-500 text-sm font-medium'>
                  {severityConfig.icon}
                  <span>Severity</span>
                </div>
                <Tag
                  color={severityConfig.color}
                  className='font-semibold text-sm px-3 py-1 rounded-full border-0'
                >
                  {notification.severity}
                </Tag>
              </div>

              {/* Time */}
              <div className='space-y-2'>
                <div className='space-y-1'>
                  <div className='text-sm font-semibold text-gray-900'>
                    {dayjs(notification.createdAt).format('MMM DD, YYYY')}
                  </div>
                  <div className='text-xs text-gray-500'>
                    {dayjs(notification.createdAt).format('HH:mm:ss')} •{' '}
                    {dayjs(notification.createdAt).fromNow()}
                  </div>
                </div>
              </div>

              {/*/!* Status *!/*/}
              {/*<div className='space-y-2'>*/}
              {/*  <div className='flex items-center gap-2 text-gray-500 text-sm font-medium'>*/}
              {/*    <EyeOutlined />*/}
              {/*    <span>Status</span>*/}
              {/*  </div>*/}
              {/*  <div className='flex items-center gap-2'>*/}
              {/*    <div className={`w-3 h-3 rounded-full ${notification.isRead ? 'bg-gray-400' : 'bg-blue-500 animate-pulse'}`} />*/}
              {/*    <span className={`text-sm font-semibold ${notification.isRead ? 'text-gray-600' : 'text-blue-600'}`}>*/}
              {/*      {notification.isRead ? 'Read' : 'Unread'}*/}
              {/*    </span>*/}
              {/*  </div>*/}
              {/*</div>*/}
            </div>

            {/*/!* Metadata Section - Expandable *!/*/}
            {/*{notification.metadata && Object.keys(notification.metadata).length > 0 && (*/}
            {/*  <div className='mt-4 pt-4 border-t border-gray-200'>*/}
            {/*    <Collapse*/}
            {/*      ghost*/}
            {/*      size='small'*/}
            {/*      style={{padding: '0'}}*/}
            {/*      expandIcon={({ isActive }) => (*/}
            {/*        <DownOutlined*/}
            {/*          className={`text-gray-500 transition-transform duration-200 ${isActive ? 'rotate-180' : ''}`}*/}
            {/*        />*/}
            {/*      )}*/}
            {/*      items={[*/}
            {/*        {*/}
            {/*          key: 'metadata',*/}
            {/*          label: (*/}
            {/*            <div className='flex items-center gap-2 text-gray-500 text-sm font-medium'>*/}
            {/*              <InfoCircleOutlined />*/}
            {/*              <span>Additional Details ({Object.keys(notification.metadata).length} items)</span>*/}
            {/*            </div>*/}
            {/*          ),*/}
            {/*          children: (*/}
            {/*            <div className='bg-gray-50 rounded-lg  mt-2'>*/}
            {/*              <Descriptions*/}
            {/*                bordered*/}
            {/*                size='small'*/}
            {/*                column={1}*/}
            {/*                className='notification-metadata'*/}
            {/*                labelStyle={{*/}
            {/*                  backgroundColor: '#f9fafb',*/}
            {/*                  color: '#6b7280',*/}
            {/*                  fontWeight: 600,*/}
            {/*                  fontSize: '12px',*/}
            {/*                  textTransform: 'uppercase',*/}
            {/*                  letterSpacing: '0.5px',*/}
            {/*                  width: '40%',*/}
            {/*                  padding: '8px 12px',*/}
            {/*                }}*/}
            {/*                contentStyle={{*/}
            {/*                  backgroundColor: '#ffffff',*/}
            {/*                  color: '#111827',*/}
            {/*                  fontWeight: 500,*/}
            {/*                  fontSize: '14px',*/}
            {/*                  padding: '8px 12px',*/}
            {/*                }}*/}
            {/*              >*/}
            {/*                {Object.entries(notification.metadata).map(([key, value]) => {*/}
            {/*                  const isId = isIdField(key);*/}
            {/*                  const isDate = isDateField(key);*/}

            {/*                  return (*/}
            {/*                    <Descriptions.Item*/}
            {/*                      key={key}*/}
            {/*                      label={key.replace(/([A-Z])/g, ' $1').trim()}*/}
            {/*                      span={isId ? 1 : 1}*/}
            {/*                    >*/}
            {/*                      {isDate ? (*/}
            {/*                        <div className='space-y-1'>*/}
            {/*                          <div className='text-sm font-medium text-gray-900'>*/}
            {/*                            {dayjs(value).format('MMM DD, YYYY HH:mm:ss')}*/}
            {/*                          </div>*/}
            {/*                          <div className='text-xs text-gray-500'>*/}
            {/*                            {dayjs(value).fromNow()}*/}
            {/*                          </div>*/}
            {/*                        </div>*/}
            {/*                      ) : isId ? (*/}
            {/*                        <TextCompression*/}
            {/*                          text={String(value)}*/}
            {/*                          maxLength={12}*/}
            {/*                          size='small'*/}
            {/*                          showCopy={true}*/}
            {/*                          className='w-full'*/}
            {/*                        />*/}
            {/*                      ) : (*/}
            {/*                        <span className='text-sm font-medium text-gray-900'>*/}
            {/*                          {String(value)}*/}
            {/*                        </span>*/}
            {/*                      )}*/}
            {/*                    </Descriptions.Item>*/}
            {/*                  );*/}
            {/*                })}*/}
            {/*              </Descriptions>*/}
            {/*            </div>*/}
            {/*          ),*/}
            {/*        },*/}
            {/*      ]}*/}
            {/*    />*/}
            {/*  </div>*/}
            {/*)}*/}

            {/* Action Button */}
            {notification.actionText && notification.actionUrl && (
              <div className='mt-4 pt-4 border-t border-gray-200'>
                <div className='flex items-center gap-2 text-gray-500 text-sm font-medium mb-3'>
                  <LinkOutlined />
                  <span>Available Action</span>
                </div>
                <Button
                  type='primary'
                  size='middle'
                  className={`
                    ${getButtonColorClass(severityConfig.color)}
                    shadow-md hover:shadow-lg transition-all duration-200
                    font-semibold px-6 py-2 h-auto rounded-lg
                  `}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAction();
                  }}
                >
                  {notification.actionText}
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default NotificationItem;
