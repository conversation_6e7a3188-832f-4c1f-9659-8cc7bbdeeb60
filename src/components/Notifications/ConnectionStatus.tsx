import {
  DisconnectOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { Space, Typography, Button, Tooltip } from 'antd';
import React from 'react';
import { useSignalRConnectionState } from '@/hooks/useSignalR';
import { signalRService } from '@/services/signalRService';

const { Text } = Typography;

const ConnectionStatus: React.FC = () => {
  const { isConnected } = useSignalRConnectionState();

  const statusConfig = isConnected
    ? {
        icon: <CheckCircleOutlined className='text-green-500' />,
        color: 'success' as const,
        text: 'Connected',
        description: 'Real-time notifications active',
      }
    : {
        icon: <DisconnectOutlined className='text-red-500' />,
        color: 'error' as const,
        text: 'Disconnected',
        description: 'No real-time updates',
      };

  const handleReconnect = async () => {
    try {
      await signalRService.start();
    } catch (error) {
      console.error('Failed to reconnect:', error);
    }
  };

  const handleDisconnect = async () => {
    try {
      await signalRService.stop();
    } catch (error) {
      console.error('Failed to disconnect:', error);
    }
  };

  return (
    <div className='bg-gray-50 rounded-lg p-3'>
      <div className='flex items-center justify-between'>
        <Space>
          {statusConfig.icon}
          <div>
            <div className='flex items-center gap-2'>
              <Text strong className='text-sm'>
                {statusConfig.text}
              </Text>
            </div>
            <Text type='secondary' className='text-xs'>
              {statusConfig.description}
            </Text>
          </div>
        </Space>

        <div className='flex items-center gap-1'>
          {!isConnected && (
            <Tooltip title='Reconnect'>
              <Button
                type='text'
                size='small'
                icon={<ReloadOutlined />}
                onClick={handleReconnect}
                className='text-blue-600 hover:bg-blue-100'
              />
            </Tooltip>
          )}

          {isConnected && (
            <Tooltip title='Disconnect'>
              <Button
                type='text'
                size='small'
                icon={<DisconnectOutlined />}
                onClick={handleDisconnect}
                className='text-red-600 hover:bg-red-100'
              />
            </Tooltip>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConnectionStatus;
