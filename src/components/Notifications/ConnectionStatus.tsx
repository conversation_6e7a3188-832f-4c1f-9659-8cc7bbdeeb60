import React from 'react';
import { Space, Typography, Button, Tooltip, Tag } from 'antd';
import {
  WifiOutlined,
  DisconnectOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { useSignalRConnectionState } from '@/hooks/useSignalR';
import { signalRService } from '@/services/signalRService';

const { Text } = Typography;

const ConnectionStatus: React.FC = () => {
  const { isConnected } = useSignalRConnectionState();

  const getStatusConfig = (state: SignalRConnectionState) => {
    switch (state) {
      case 'Connected':
        return {
          icon: <CheckCircleOutlined className="text-green-500" />,
          color: 'success' as const,
          text: 'Connected',
          description: 'Real-time notifications active',
        };
      case 'Connecting':
        return {
          icon: <LoadingOutlined className="text-blue-500" spin />,
          color: 'processing' as const,
          text: 'Connecting',
          description: 'Establishing connection...',
        };
      case 'Reconnecting':
        return {
          icon: <LoadingOutlined className="text-orange-500" spin />,
          color: 'warning' as const,
          text: 'Reconnecting',
          description: `Attempt ${reconnectAttempts + 1}`,
        };
      case 'Disconnecting':
        return {
          icon: <LoadingOutlined className="text-gray-500" spin />,
          color: 'default' as const,
          text: 'Disconnecting',
          description: 'Closing connection...',
        };
      case 'Disconnected':
      default:
        return {
          icon: <DisconnectOutlined className="text-red-500" />,
          color: 'error' as const,
          text: 'Disconnected',
          description: 'No real-time updates',
        };
    }
  };

  const statusConfig = getStatusConfig(connectionState);

  const handleReconnect = async () => {
    try {
      await signalRService.start();
    } catch (error) {
      console.error('Failed to reconnect:', error);
    }
  };

  const handleDisconnect = async () => {
    try {
      await signalRService.stop();
    } catch (error) {
      console.error('Failed to disconnect:', error);
    }
  };

  return (
    <div className="bg-gray-50 rounded-lg p-3">
      <div className="flex items-center justify-between">
        <Space>
          {statusConfig.icon}
          <div>
            <div className="flex items-center gap-2">
              <Text strong className="text-sm">
                {statusConfig.text}
              </Text>
              <Tag color={statusConfig.color} size="small">
                SignalR
              </Tag>
            </div>
            <Text type="secondary" className="text-xs">
              {statusConfig.description}
            </Text>
          </div>
        </Space>

        <div className="flex items-center gap-1">
          {!isConnected && !isConnecting && !isReconnecting && (
            <Tooltip title="Reconnect">
              <Button
                type="text"
                size="small"
                icon={<ReloadOutlined />}
                onClick={handleReconnect}
                className="text-blue-600 hover:bg-blue-100"
              />
            </Tooltip>
          )}

          {isConnected && (
            <Tooltip title="Disconnect">
              <Button
                type="text"
                size="small"
                icon={<DisconnectOutlined />}
                onClick={handleDisconnect}
                className="text-red-600 hover:bg-red-100"
              />
            </Tooltip>
          )}
        </div>
      </div>

      {/* Additional Info */}
      {lastConnectedAt && (
        <div className="mt-2 pt-2 border-t border-gray-200">
          <Text type="secondary" className="text-xs">
            Last connected: {dayjs(lastConnectedAt).format('MMM DD, HH:mm')}
          </Text>
        </div>
      )}

      {reconnectAttempts > 0 && !isConnected && (
        <div className="mt-2 pt-2 border-t border-gray-200">
          <div className="flex items-center gap-2">
            <ExclamationCircleOutlined className="text-orange-500 text-xs" />
            <Text type="secondary" className="text-xs">
              {reconnectAttempts} reconnect attempt{reconnectAttempts > 1 ? 's' : ''}
            </Text>
          </div>
        </div>
      )}
    </div>
  );
};

export default ConnectionStatus;
