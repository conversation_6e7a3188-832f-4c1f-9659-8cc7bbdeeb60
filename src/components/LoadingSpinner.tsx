import { Spin } from 'antd';
import React from 'react';

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large';
  tip?: string;
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  tip = 'Loading...',
  className = '',
}) => {
  return (
    <div
      className={`flex justify-center items-center h-64 ${className} bg-red-500`}
    >
      <Spin size={size} fullscreen tip={tip} />
    </div>
  );
};

export default LoadingSpinner;
