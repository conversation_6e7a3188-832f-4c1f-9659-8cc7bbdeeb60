import { useCallback, type HtmlHTMLAttributes } from 'react';
import useResizeObserver from '@/hooks/useResizeObserver';

type ObserveProps = {
  clientHeight: number;
  scrollHeight: number;
  clientWidth: number;
  scrollWidth: number;
};
interface IObserveBoxProps extends HtmlHTMLAttributes<HTMLDivElement> {
  onObserve?: (props: ObserveProps) => void;
}
const ObserveBox: React.FunctionComponent<IObserveBoxProps> = (props) => {
  const { onObserve, ...divProps } = props || {};
  const ref = useResizeObserver<HTMLDivElement>(
    useCallback(
      (rezEntry) => {
        const { clientHeight, scrollHeight, clientWidth, scrollWidth } =
          rezEntry.target;
        if (onObserve)
          onObserve({ clientHeight, scrollHeight, clientWidth, scrollWidth });
      },
      [onObserve],
    ),
  );
  return <div {...{ ...divProps, ref }} />;
};

export default ObserveBox;
export type { ObserveProps };
