import { Tag } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CounterStatusEnums, MemberStatusEnums } from '@/utils/enums';
import { memberStatusEnumsOptions } from '@/utils/options';

interface AlphaStatusTagProps {
  status: MemberStatusEnums | CounterStatusEnums;
}
const AlphaStatusTag = ({ status: type }: AlphaStatusTagProps) => {
  const { t } = useTranslation('options');

  const color = useMemo(() => {
    switch (type) {
      case MemberStatusEnums.Active:
        return 'success';
      case MemberStatusEnums.Inactive:
        return 'error';
      default:
        return 'default';
    }
  }, [type]);
  const label = useMemo(
    () =>
      t(
        memberStatusEnumsOptions.find((option) => option.value === type)
          ?.label || 'undefined',
      ),
    [type, t],
  );

  return (
    <Tag color={color} className='!m-0 capitalize w-fit'>
      {label}
    </Tag>
  );
};

export { AlphaStatusTag };
