import {
  IdcardOutlined,
  SafetyCertificateOutlined,
  SolutionOutlined,
  UserOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { Tag } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { PassportTypeEnum, passportTypeEnumsOptions } from '@/utils';

const PassportTypeTag = ({ type }: { type: PassportTypeEnum }) => {
  const { t } = useTranslation('options');

  const color = useMemo(() => {
    switch (type) {
      case PassportTypeEnum.Ordinary:
        return 'blue';
      case PassportTypeEnum.Diplomatic:
        return 'purple';
      case PassportTypeEnum.Official:
        return 'gold';
      case PassportTypeEnum.Special:
        return 'volcano';
      case PassportTypeEnum.Temporary:
        return 'orange';
      default:
        return 'default';
    }
  }, [type]);

  const icon = useMemo(() => {
    switch (type) {
      case PassportTypeEnum.Ordinary:
        return <IdcardOutlined />;
      case PassportTypeEnum.Diplomatic:
        return <SafetyCertificateOutlined />;
      case PassportTypeEnum.Official:
        return <SolutionOutlined />;
      case PassportTypeEnum.Special:
        return <UserOutlined />;
      case PassportTypeEnum.Temporary:
        return <ClockCircleOutlined />;
      default:
        return '';
    }
  }, [type]);

  const label = useMemo(() => {
    return t(
      passportTypeEnumsOptions.find((option) => option.value === type)?.label ||
        'undefined',
    );
  }, [t, type]);

  return (
    <Tag color={color} icon={icon} className='!m-0 capitalize w-fit'>
      {label}
    </Tag>
  );
};

export { PassportTypeTag };
