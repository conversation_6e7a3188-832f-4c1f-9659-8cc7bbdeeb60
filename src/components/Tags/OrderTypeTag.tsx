import { Tag } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { OrderTypeEnums } from '@/utils/enums';
import { orderTypeEnumsOptions } from '@/utils/options';

interface OrderTypeTagProps {
  type: OrderTypeEnums;
}
const OrderTypeTag = ({ type }: OrderTypeTagProps) => {
  const { t } = useTranslation('options');

  const color = useMemo(() => {
    switch (type) {
      case OrderTypeEnums.Buy:
        return 'success';
      case OrderTypeEnums.Sell:
        return 'error';
      default:
        return 'default';
    }
  }, [type]);
  const label = useMemo(
    () =>
      t(
        orderTypeEnumsOptions.find((option) => option.value === type)?.label ||
          'undefined',
      ),
    [type, t],
  );

  return (
    <Tag color={color} className='!m-0 capitalize w-fit'>
      {label}
    </Tag>
  );
};

export { OrderTypeTag };
