import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  StopOutlined,
  SyncOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Tag } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { OrderStatusEnums, orderStatusEnumsOptions } from '@/utils';

const OrderStatusTag = ({ status }: { status: OrderStatusEnums }) => {
  const { t } = useTranslation('options');

  const color = useMemo(() => {
    switch (status) {
      case OrderStatusEnums.Pending:
        return 'warning';
      case OrderStatusEnums.Confirmed:
        return 'cyan';
      case OrderStatusEnums.Processing:
        return 'processing';
      case OrderStatusEnums.Completed:
        return 'success';
      case OrderStatusEnums.Cancelled:
        return 'volcano';
      case OrderStatusEnums.Rejected:
        return 'error';
      default:
        return 'default';
    }
  }, [status]);
  const icon = useMemo(() => {
    switch (status) {
      case OrderStatusEnums.Pending:
        return <ClockCircleOutlined />;
      case OrderStatusEnums.Confirmed:
        return <UserOutlined />;
      case OrderStatusEnums.Processing:
        return <SyncOutlined />;
      case OrderStatusEnums.Completed:
        return <CheckCircleOutlined />;
      case OrderStatusEnums.Cancelled:
        return <CloseCircleOutlined />;
      case OrderStatusEnums.Rejected:
        return <StopOutlined />;
      default:
        return '';
    }
  }, [status]);
  const label = useMemo(
    () =>
      t(
        orderStatusEnumsOptions.find((option) => option.value === status)
          ?.label || 'undefined',
      ),
    [t, status],
  );

  return (
    <Tag color={color} icon={icon} className='!m-0 capitalize w-fit'>
      {label}
    </Tag>
  );
};

export { OrderStatusTag };
