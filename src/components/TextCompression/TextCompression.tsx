import {
  ExpandOutlined,
  CompressOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import { Button, Tooltip, Typography } from 'antd';
import { message } from 'antd';
import type { BaseType } from 'antd/es/typography/Base';
import React, { useState } from 'react';

const { Text } = Typography;

interface TextCompressionProps {
  text: string;
  maxLength?: number;
  showCopy?: boolean;
  className?: string;
  style?: React.CSSProperties;
  size?: 'small' | 'default' | 'large';
  type?: BaseType;
}

const TextCompression: React.FC<TextCompressionProps> = ({
  text,
  maxLength = 8,
  showCopy = true,
  className = '',
  style,
  size = 'default',
  type,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const shouldCompress = text.length > maxLength;
  const displayText =
    shouldCompress && !isExpanded ? `${text.slice(0, maxLength)}...` : text;

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(text);
      message.success('Copied to clipboard');
    } catch (error) {
      console.log(error);
      message.error('Failed to copy');
    }
  };

  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  const getSizeClass = () => {
    switch (size) {
      case 'small':
        return 'text-xs';
      case 'large':
        return 'text-lg';
      default:
        return 'text-sm';
    }
  };

  const getButtonSize = () => {
    switch (size) {
      case 'small':
        return 'small' as const;
      case 'large':
        return 'middle' as const;
      default:
        return 'small' as const;
    }
  };

  if (!shouldCompress && !showCopy) {
    return (
      <Text
        type={type}
        className={`font-mono ${getSizeClass()} ${className}`}
        style={style}
      >
        {text}
      </Text>
    );
  }

  return (
    <div
      className={`inline-flex items-center gap-1 ${className}`}
      style={style}
    >
      <Text
        type={type}
        className={`font-mono ${getSizeClass()} select-all`}
        title={text}
      >
        {displayText}
      </Text>

      <div className='flex items-center gap-1'>
        {shouldCompress && (
          <Tooltip title={isExpanded ? 'Compress' : 'Expand'}>
            <Button
              type='text'
              size={getButtonSize()}
              icon={isExpanded ? <CompressOutlined /> : <ExpandOutlined />}
              onClick={handleToggle}
              className='text-gray-400 hover:text-blue-600 p-0 h-auto min-w-0 border-0'
              style={{ fontSize: size === 'small' ? '10px' : '12px' }}
            />
          </Tooltip>
        )}

        {showCopy && (
          <Tooltip title='Copy to clipboard'>
            <Button
              type='text'
              size={getButtonSize()}
              icon={<CopyOutlined />}
              onClick={handleCopy}
              className='text-gray-400 hover:text-green-600 p-0 h-auto min-w-0 border-0'
              style={{ fontSize: size === 'small' ? '10px' : '12px' }}
            />
          </Tooltip>
        )}
      </div>
    </div>
  );
};

export default TextCompression;
