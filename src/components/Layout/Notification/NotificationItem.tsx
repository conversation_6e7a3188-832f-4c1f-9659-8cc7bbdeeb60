import { Button, Flex, Typography } from 'antd';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { NotificationItem } from './NotificationDropdown';
import { OrderStatusTag } from '@/components/Tags';
import { useUserStore } from '@/stores';
import { OrderStatusEnums } from '@/utils';
import { dateFormator } from '@/utils/dateUtils';

const { Text } = Typography;

interface NotificationItemProps {
  notification: NotificationItem;
  onClick: (orderId: NotificationItem['orderId']) => void;
  isMobile?: boolean;
}
export default function NotificationItem(props: NotificationItemProps) {
  const { notification, onClick, isMobile = false } = props || {};
  const { t } = useTranslation('notificationItem');
  const { isDark } = useUserStore();
  const color = useMemo(() => {
    switch (notification.orderStatus) {
      case OrderStatusEnums.Pending:
        return 'border-blue-400';
      case OrderStatusEnums.Completed:
        return 'border-green-400';
      case OrderStatusEnums.Cancelled:
        return 'border-yellow-400';
      case OrderStatusEnums.Rejected:
        return 'border-red-400';
    }
  }, [notification.orderStatus]);

  return (
    <Flex
      vertical
      gap={isMobile ? 6 : 8}
      className={`${color} !-mx-2 rounded-md border-l-4 !px-2 !py-2 transition-colors ${isDark ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
    >
      <Flex
        align='center'
        justify='space-between'
        gap={isMobile ? 6 : 8}
        className='w-full'
      >
        <Flex vertical className={`${isMobile ? 'ml-2' : 'ml-4'}`}>
          {/* <Text strong className={`${isMobile ? 'text-sm' : ''}`}> */}
          {/*   {notification.memberName} */}
          {/* </Text> */}
          {/* <Text type='secondary' className={`${isMobile ? 'text-xs' : ''}`}> */}
          {/*   {notification.description} */}
          {/* </Text> */}
          <Text type='secondary' className={`${isMobile ? 'text-xs' : ''}`}>
            {dayjs(notification.createdAt).format(dateFormator.accurate)}
          </Text>
        </Flex>
        <OrderStatusTag status={notification.orderStatus} />
      </Flex>
      <Button
        key='mark'
        size={isMobile ? 'small' : 'small'}
        className='w-fit'
        onClick={() => onClick(notification.orderId)}
      >
        <span className={`${isMobile ? 'text-xs' : ''}`}>
          {t('markAsRead')}
        </span>
      </Button>
    </Flex>
  );
}
