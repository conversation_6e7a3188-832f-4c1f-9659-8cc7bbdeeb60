import { BellOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Dropdown, Typography } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import NotificationItem from './NotificationItem';
import type { Order } from '@/types';

export interface NotificationItem extends Order {
  isRead: boolean;
}

interface NotificationDropdownProps {
  isMobile?: boolean;
}

export default function NotificationDropdown({
  isMobile = false,
}: NotificationDropdownProps) {
  const { t } = useTranslation('notificationDropdown');

  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  // useEffect(() => {
  //   const enriched = mockOrders.map((order) => ({
  //     ...order,
  //     isRead: false,
  //   }));
  //   setNotifications(enriched);
  // }, []);

  const unreadCount = notifications.filter((n) => !n.isRead).length;

  const markAsRead = (id: string) => {
    setNotifications((prev) => prev.filter((n) => n.orderId !== id));
  };

  const popupRender = () => (
    <Card
      className={`no-scrollbar max-h-96 w-96 max-w-full overflow-y-auto overscroll-contain rounded-lg bg-white`}
    >
      <div className='flex flex-col gap-y-4'>
        {notifications.length > 0 ? (
          notifications.map((notification) => (
            <NotificationItem
              key={notification.orderId}
              notification={notification}
              onClick={markAsRead}
              isMobile={isMobile}
            />
          ))
        ) : (
          <Typography.Text type='secondary' className='text-center'>
            {t('emptyNotification')}
          </Typography.Text>
        )}
      </div>
    </Card>
  );

  return (
    <Dropdown
      popupRender={popupRender}
      trigger={['click']}
      placement={isMobile ? 'bottomCenter' : 'bottomRight'}
    >
      <Badge count={unreadCount} size='small'>
        <Button
          type='text'
          icon={<BellOutlined />}
          size={isMobile ? 'middle' : 'large'}
          className='hover:bg-gray-50'
        />
      </Badge>
    </Dropdown>
  );
}
