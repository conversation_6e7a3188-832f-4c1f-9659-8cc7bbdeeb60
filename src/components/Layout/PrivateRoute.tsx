import { Navigate, Outlet } from 'react-router-dom';
import LoadingPage from '@/components/LoadingPage';
import { ROUTES } from '@/constants/routes';
import { useAuthStore } from '@/stores';

export default function PrivateRoute() {
  const { isAuthenticated, hydrated } = useAuthStore();

  if (!hydrated) return <LoadingPage />;

  return isAuthenticated ? <Outlet /> : <Navigate to={ROUTES.LOGIN} replace />;
}
