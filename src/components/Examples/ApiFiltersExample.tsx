import { Button, Input, Select, Space, Tag, Card, Typography } from 'antd';
import React from 'react';
import { useOrdersWithFilters } from '@/hooks/useOrders';
import { OrderStatusEnums } from '@/utils';

const { Title, Text } = Typography;
const { Option } = Select;

/**
 * Example component demonstrating how to use the dynamic API filtering hook
 * This shows how to integrate the useOrdersWithFilters hook with UI components
 */
const ApiFiltersExample: React.FC = () => {
  const {
    // Data and loading states
    data,
    isLoading,
    error,

    // Filter and sort state
    filters,
    sorts,
    page,
    pageSize,
    hasActiveFiltering,
    queryString,

    // Filter management methods
    addFilter,
    removeFilter,
    clearFilters,

    // Sort management methods
    toggleSort,
    clearSorts,

    // Pagination methods
    updatePage,
    updatePageSize,

    // Convenience methods
    filterByStatus,
    clearStatusFilter,

    // Reset all
    resetAll,
  } = useOrdersWithFilters();

  const handleStatusFilter = (status: string) => {
    if (status) {
      // This will be sent as OrderStatus~eq~COMPLETED to the server
      filterByStatus(status);
    } else {
      clearStatusFilter();
    }
  };

  const handleAmountFilter = (min: string, max: string) => {
    removeFilter('amount'); // Clear existing amount filters

    if (min) {
      addFilter({ key: 'amount', operator: 'gte', value: Number(min) });
    }
    if (max) {
      addFilter({ key: 'amount', operator: 'lte', value: Number(max) });
    }
  };

  return (
    <div className='p-6 space-y-6'>
      <Card>
        <Title level={3}>Dynamic API Filters Example</Title>
        <Text type='secondary'>
          This example demonstrates the dynamic filtering and sorting
          capabilities.
        </Text>
      </Card>

      {/* Filter Controls */}
      <Card title='Filter Controls'>
        <Space direction='vertical' size='middle' className='w-full'>
          {/* Status Filter */}
          <div>
            <Text strong>Status Filter:</Text>
            <Select
              placeholder='Select status'
              allowClear
              onChange={handleStatusFilter}
              className='ml-2 w-48'
            >
              <Option value='pending'>Pending</Option>
              <Option value='completed'>Completed</Option>
              <Option value='failed'>Failed</Option>
              <Option value='cancelled'>Cancelled</Option>
            </Select>
          </div>

          {/* Amount Range */}
          <div>
            <Text strong>Amount Range:</Text>
            <Space className='ml-2'>
              <Input
                placeholder='Min amount'
                type='number'
                onChange={(e) => {
                  const max = (
                    e.target.form?.elements.namedItem(
                      'maxAmount',
                    ) as HTMLInputElement
                  )?.value;
                  handleAmountFilter(e.target.value, max || '');
                }}
                name='minAmount'
              />
              <Text>to</Text>
              <Input
                placeholder='Max amount'
                type='number'
                onChange={(e) => {
                  const min = (
                    e.target.form?.elements.namedItem(
                      'minAmount',
                    ) as HTMLInputElement
                  )?.value;
                  handleAmountFilter(min || '', e.target.value);
                }}
                name='maxAmount'
              />
            </Space>
          </div>

          {/* Sort Controls */}
          <div>
            <Text strong>Sort by:</Text>
            <Space className='ml-2'>
              <Button onClick={() => toggleSort('date')}>
                Date{' '}
                {sorts.find((s) => s.key === 'date')?.direction === 'asc'
                  ? '↑'
                  : sorts.find((s) => s.key === 'date')?.direction === 'desc'
                    ? '↓'
                    : ''}
              </Button>
              <Button onClick={() => toggleSort('amount')}>
                Amount{' '}
                {sorts.find((s) => s.key === 'amount')?.direction === 'asc'
                  ? '↑'
                  : sorts.find((s) => s.key === 'amount')?.direction === 'desc'
                    ? '↓'
                    : ''}
              </Button>
              <Button onClick={() => toggleSort('memberName')}>
                Member{' '}
                {sorts.find((s) => s.key === 'memberName')?.direction === 'asc'
                  ? '↑'
                  : sorts.find((s) => s.key === 'memberName')?.direction ===
                      'desc'
                    ? '↓'
                    : ''}
              </Button>
            </Space>
          </div>

          {/* Action Buttons */}
          <Space>
            <Button onClick={clearFilters} disabled={filters.length === 0}>
              Clear Filters
            </Button>
            <Button onClick={clearSorts} disabled={sorts.length === 0}>
              Clear Sorts
            </Button>
            <Button onClick={resetAll} type='primary' ghost>
              Reset All
            </Button>
          </Space>
        </Space>
      </Card>

      {/* Active Filters Display */}
      {hasActiveFiltering && (
        <Card title='Active Filters & Sorts' size='small'>
          <Space direction='vertical' size='small' className='w-full'>
            {filters.length > 0 && (
              <div>
                <Text strong>Filters:</Text>
                <div className='mt-2'>
                  {filters.map((filter, index) => (
                    <Tag
                      key={`${filter.key}-${filter.operator}-${index}`}
                      closable
                      onClose={() => removeFilter(filter.key, filter.operator)}
                      className='mb-1'
                    >
                      {filter.key} {filter.operator} {String(filter.value)}
                    </Tag>
                  ))}
                </div>
              </div>
            )}

            {sorts.length > 0 && (
              <div>
                <Text strong>Sorts:</Text>
                <div className='mt-2'>
                  {sorts.map((sort, index) => (
                    <Tag
                      key={`${sort.key}-${index}`}
                      closable
                      onClose={() => removeFilter(sort.key)}
                      color='blue'
                      className='mb-1'
                    >
                      {sort.key} {sort.direction}
                    </Tag>
                  ))}
                </div>
              </div>
            )}
          </Space>
        </Card>
      )}

      {/* Query String Display */}
      <Card title='Generated Query String (PascalCase Parameters)' size='small'>
        <div className='space-y-2'>
          <Text code>{decodeURIComponent(queryString) || 'No parameters'}</Text>
          {queryString && (
            <div className='text-sm text-gray-600'>
              <Text type='secondary'>
                Note: Parameters are automatically converted to PascalCase for
                server communication.
                <br />
                Example: orderStatus → OrderStatus, userId → UserId, createdAt →
                CreatedAt
              </Text>
            </div>
          )}
        </div>
      </Card>

      {/* Data Display */}
      <Card title='Orders Data'>
        {isLoading && <Text>Loading...</Text>}
        {error && <Text type='danger'>Error: {error.message}</Text>}
        {data && (
          <div>
            <Text>
              Showing {data.data.data.length} of {data.data.total} orders (Page{' '}
              {page} of {Math.ceil(data.data.total / pageSize)})
            </Text>
            <div className='mt-4 space-y-2'>
              {data.data.data.map((order) => (
                <div key={order.orderCode} className='p-3 border rounded'>
                  <Space>
                    <Text strong>Order: {order.orderCode.slice(0, 8)}...</Text>
                    <Tag
                      color={
                        order.orderStatus === OrderStatusEnums.Completed
                          ? 'green'
                          : 'orange'
                      }
                    >
                      {order.orderStatus}
                    </Tag>
                    <Text>
                      {order.quantity} × ${order.pricePerUnit} = $
                      {order.quantity * order.pricePerUnit} {order.currencyUnit}
                    </Text>
                    <Text type='secondary'>
                      {new Date(order.createdAt).toLocaleDateString()}
                    </Text>
                  </Space>
                </div>
              ))}
            </div>

            {/* Pagination */}
            <div className='mt-4 flex justify-between items-center'>
              <Space>
                <Button
                  disabled={page <= 1}
                  onClick={() => updatePage(page - 1)}
                >
                  Previous
                </Button>
                <Text>Page {page}</Text>
                <Button
                  disabled={page >= Math.ceil(data.data.total / pageSize)}
                  onClick={() => updatePage(page + 1)}
                >
                  Next
                </Button>
              </Space>

              <Select
                value={pageSize}
                onChange={updatePageSize}
                style={{ width: 120 }}
              >
                <Option value={5}>5 per page</Option>
                <Option value={10}>10 per page</Option>
                <Option value={20}>20 per page</Option>
                <Option value={50}>50 per page</Option>
              </Select>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default ApiFiltersExample;
