import { ReloadOutlined } from '@ant-design/icons';
import { Alert, Button } from 'antd';
import { Component } from 'react';
import { withTranslation } from 'react-i18next';
import type { WithTranslation } from 'react-i18next';

interface State {
  hasError: boolean;
  error?: Error;
}

interface Props extends WithTranslation {
  children: React.ReactNode;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: any) {
    console.error('Uncaught error:', error, errorInfo);
  }

  private handleReload = () => {
    window.location.reload();
  };

  public render() {
    const { t } = this.props;

    if (this.state.hasError) {
      return (
        <div className='min-h-screen flex items-center justify-center bg-gray-50 p-4'>
          <div className='max-w-md w-full'>
            <Alert
              message={t('errMess')}
              description={
                <div className='space-y-4'>
                  <p>{t('errToReload')}</p>
                  {this.state.error && (
                    <details className='text-xs text-gray-600'>
                      <summary>{t('errorD')}</summary>
                      <pre className='mt-2 whitespace-pre-wrap'>
                        {this.state.error.message}
                      </pre>
                    </details>
                  )}
                  <Button
                    type='primary'
                    icon={<ReloadOutlined />}
                    onClick={this.handleReload}
                  >
                    {t('reload')}
                  </Button>
                </div>
              }
              type='error'
              showIcon
            />
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

const TranslatedErrorBoundary = withTranslation('errorBoundary')(ErrorBoundary);

export default TranslatedErrorBoundary;
