import { Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Logo } from './Logo';
import { useUserStore } from '@/stores';

const { Text } = Typography;

interface LoadingPageProps {
  tip?: string;
  size?: 'small' | 'default' | 'large';
  className?: string;
  showLogo?: boolean;
  overlay?: boolean;
}

const LoadingPage: React.FC<LoadingPageProps> = ({
  tip,
  size = 'large',
  className,
  showLogo = false,
  overlay = true,
}) => {
  const { t } = useTranslation('loadingPage');
  const [dots, setDots] = useState('');
  const { isDark } = useUserStore();

  // Animated dots effect
  useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, []);

  const loadingText = tip || t('loading');

  const sizeClasses = {
    small: 'w-8 h-8',
    default: 'w-12 h-12',
    large: 'w-16 h-16',
  };

  const textSizeClasses = {
    small: 'text-sm',
    default: 'text-base',
    large: 'text-lg',
  };

  const baseClasses = overlay
    ? 'fixed inset-0 z-50 flex items-center justify-center'
    : 'flex items-center justify-center h-screen';

  return (
    <div className={className || baseClasses}>
      {/* Overlay Background with Blur */}
      {overlay && (
        <div
          className={`${isDark ? 'bg-gradient-to-br from-slate-900/95 to-indigo-900/60' : 'bg-gradient-to-br from-rose-100/10 to-amber-900/50'} absolute inset-0 backdrop-blur-md`}
        ></div>
      )}

      {/* Enhanced Background Pattern */}
      <div className='absolute inset-0 overflow-hidden pointer-events-none'>
        {/* Animated gradient orbs */}
        <div className='absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-blue-400/30 to-purple-600/30 rounded-full blur-3xl animate-pulse'></div>
        <div className='absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-purple-400/30 to-blue-900/100 rounded-full blur-3xl animate-pulse delay-1000'></div>
        <div className='absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-cyan-300/20 to-pink-300/20 rounded-full blur-2xl animate-pulse delay-500'></div>

        {/* Floating geometric shapes */}
        <div className='absolute top-20 left-20 w-4 h-4 bg-blue-400/40 rounded-full animate-float'></div>
        <div className='absolute top-32 right-32 w-3 h-3 bg-purple-400/40 rounded-full animate-float delay-1000'></div>
        <div className='absolute bottom-20 left-32 w-2 h-2 bg-cyan-400/40 rounded-full animate-float delay-2000'></div>
        <div className='absolute bottom-32 right-20 w-5 h-5 bg-pink-400/40 rounded-full animate-float delay-1500'></div>

        {/* Subtle grid pattern */}
        <div className='absolute inset-0 opacity-5'>
          <div
            className='w-full h-full'
            style={{
              backgroundImage: `
              linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)
            `,
              backgroundSize: '50px 50px',
            }}
          ></div>
        </div>
      </div>

      {/* Loading Content */}
      <div className='relative z-10 flex flex-col items-center space-y-8'>
        {/* Glass-morphism container */}
        <div className='relative p-8 rounded-3xl bg-white/20 backdrop-blur-xl border border-white/30 shadow-2xl'>
          {/* Inner glow effect */}
          <div className='absolute inset-0 rounded-3xl bg-gradient-to-br from-white/10 to-transparent'></div>

          <div className='relative flex flex-col items-center space-y-6'>
            {/* Logo */}
            {showLogo && <Logo />}

            {/* Spinning Loader */}
            <div className='relative'>
              {/* Outer ring */}
              <div
                className={`${sizeClasses[size]} border-4 border-white/20 rounded-full animate-spin`}
              >
                <div className='absolute inset-0 border-4 border-transparent border-t-blue-400 border-r-purple-400 rounded-full animate-spin'></div>
              </div>
            </div>

            {/* Loading Text */}
            <div className='text-center space-y-3'>
              <Text
                className={`${textSizeClasses[size]} font-medium text-gray-800 block`}
              >
                {loadingText}
                {dots}
              </Text>

              {/* Enhanced Progress bar */}
              <div className='w-56 h-2 bg-white/20 rounded-full overflow-hidden backdrop-blur-sm'>
                <div className='h-full bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 rounded-full animate-pulse shadow-sm'></div>
              </div>

              {/* Subtle loading indicator */}
              <div className='flex justify-center space-x-1 mt-2'>
                {[0, 1, 2].map((i) => (
                  <div
                    key={i}
                    className='w-1 h-1 bg-white/40 rounded-full animate-pulse'
                    style={{
                      animationDelay: `${i * 0.3}s`,
                      animationDuration: '1.5s',
                    }}
                  ></div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Floating particles */}
        <div className='absolute inset-0 pointer-events-none overflow-hidden'>
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className={`absolute rounded-full animate-float opacity-30`}
              style={{
                width: `${2 + (i % 3)}px`,
                height: `${2 + (i % 3)}px`,
                background:
                  i % 3 === 0
                    ? 'rgba(59, 130, 246, 0.6)'
                    : i % 3 === 1
                      ? 'rgba(147, 51, 234, 0.6)'
                      : 'rgba(6, 182, 212, 0.6)',
                left: `${10 + i * 12}%`,
                top: `${20 + (i % 3) * 25}%`,
                animationDelay: `${i * 0.7}s`,
                animationDuration: `${3 + (i % 2)}s`,
                filter: 'blur(0.5px)',
              }}
            ></div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LoadingPage;
