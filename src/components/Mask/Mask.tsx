import type { HtmlHTMLAttributes } from 'react';

interface IMaskProps extends HtmlHTMLAttributes<HTMLDivElement> {
  open: boolean;
}
const Mask: React.FunctionComponent<IMaskProps> = (props) => {
  const { open, style, ...divProps } = props || {};

  return (
    <div
      {...{
        ...divProps,
        style: {
          transition: 'opacity 0.3s linear',
          opacity: open ? 0.6 : 0,
          pointerEvents: 'none',
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: 700,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#000000',
          ...style,
        },
      }}
    />
  );
};

export default Mask;
