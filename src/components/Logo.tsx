import { useMemo } from 'react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
}
const Logo = (props: LogoProps) => {
  const { size = 'md', ...restProps } = props || {};

  const getWidthFromSize = useMemo(() => {
    switch (size) {
      case 'sm':
        return 'w-12';
      case 'md':
        return 'w-16';
      case 'lg':
        return 'w-24';
      default:
        return 'w-24';
    }
  }, [size]);

  return (
    <img
      src='/images/logo.png'
      alt='logo'
      className={getWidthFromSize}
      {...restProps}
    />
  );
};

export { Logo };
