import { GlobalOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import type { DropdownAlphaRef, IDropdownAlphaProps } from './DropdownAlpha';
import DropdownAlpha from './DropdownAlpha';
import { useUserStore } from '@/stores';
import { WebLanguageGalaxyEnum } from '@/utils/enums';

type AllLocaleDefinition = Record<
  WebLanguageGalaxyEnum,
  { locale: WebLanguageGalaxyEnum; label: React.ReactNode }
>;
type IDropdownLocaleProps = Omit<IDropdownAlphaProps, 'items'>;

const DropdownLocale: React.FunctionComponent<IDropdownLocaleProps> = (
  props,
) => {
  // props
  const { ...dropdownProps } = props || {};

  // refs
  const dropRef = useRef<DropdownAlphaRef>(null);

  // hooks
  const { i18n, t } = useTranslation('dropdownLocale');
  const { language, changeLanguage } = i18n;
  const { isDark } = useUserStore();

  // compute
  const allLocaleDefinition: AllLocaleDefinition = useMemo(() => {
    return {
      'en-US': {
        locale: WebLanguageGalaxyEnum.En_US,
        label: t('english'),
      },
      'zh-TW': {
        locale: WebLanguageGalaxyEnum.Zh_TW,
        label: t('taiwan'),
      },
    };
  }, [t]);
  const allLocaleItem = useMemo(() => {
    return Object.values(allLocaleDefinition).map((mapD) => {
      const { label, locale } = mapD;
      const isActive = language === locale;

      return {
        item: (
          <Button
            className={`${isDark ? 'text-white' : 'text-black'} w-full`}
            type={isActive ? 'primary' : 'link'}
            onClick={async () => {
              await changeLanguage(locale);
              useUserStore.getState().setLocale(locale);
              dropRef.current?.setIsOpen(false);
            }}
          >
            {label}
          </Button>
        ),
        key: locale,
        locale,
      };
    });
  }, [allLocaleDefinition, changeLanguage, isDark, language]);

  return (
    <DropdownAlpha
      buttonProps={{ type: 'text', style: { paddingInline: 8 } }}
      ref={dropRef}
      icon={<GlobalOutlined />}
      items={allLocaleItem}
      noUnderLink
      gap={8}
      itemHeight='fit-content'
      pannelMaxHeight={400}
      {...dropdownProps}
    />
  );
};

export default DropdownLocale;
