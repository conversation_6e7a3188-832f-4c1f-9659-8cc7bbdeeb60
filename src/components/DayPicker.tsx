import { Select, type SelectProps } from 'antd';
import { useTranslation } from 'react-i18next';
import type { DayValue } from '@/utils/dateUtils';

const { Option } = Select;

interface DayPickerProps extends SelectProps {
  value: DayValue;
  onChange?: () => void;
}

const daysOfWeek: { label: string; value: DayValue }[] = [
  { label: 'monday', value: 'mon' },
  { label: 'tuesday', value: 'tue' },
  { label: 'wednesday', value: 'wed' },
  { label: 'thursday', value: 'thu' },
  { label: 'friday', value: 'fri' },
  { label: 'saturday', value: 'sat' },
  { label: 'sunday', value: 'sun' },
];

export default function DayPicker({
  value,
  onChange,
  ...props
}: DayPickerProps) {
  const { t } = useTranslation('dayPicker');

  return (
    <Select value={value} onChange={onChange} style={{ width: 120 }} {...props}>
      {daysOfWeek.map((day) => (
        <Option key={day.value} value={day.value}>
          {t(day.label)}
        </Option>
      ))}
    </Select>
  );
}
