import { Table } from 'antd';
import type { TablePaginationConfig, TableProps } from 'antd';
import type {
  FilterValue,
  SorterResult,
  TableCurrentDataSource,
} from 'antd/es/table/interface';
import { useTranslation } from 'react-i18next';
import type { GalaxyTableProps } from '@/types';
import { sortByDate } from '@/utils/tableUtils.ts';

const GalaxyTable = <T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  customSort = false,
  pagination,
  onSortChange,
  onChange,
  ...props
}: GalaxyTableProps<T> & Omit<TableProps<T>, 'dataSource' | 'columns'>) => {
  const { t } = useTranslation('galaxyTable');
  // Apply custom sorting if enabled
  const processedColumns = customSort
    ? columns.map((column) => {
        if (
          column.dataIndex === 'registrationDate' ||
          column.dataIndex === 'date'
        ) {
          return {
            ...column,
            sorter: (a: T, b: T) =>
              sortByDate(a[column.dataIndex], b[column.dataIndex]),
            sortDirections: ['descend', 'ascend'] as const,
            defaultSortOrder: 'descend' as const,
          };
        }
        return {
          ...column,
          sorter: (a: T, b: T) =>
            String(a[column.dataIndex] || '').localeCompare(
              String(b[column.dataIndex] || ''),
              'vi',
              { sensitivity: 'base' },
            ),
          sortDirections: ['ascend', 'descend'] as const,
        };
      })
    : columns;

  const handleChange = (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<T> | SorterResult<T>[],
    extra: TableCurrentDataSource<T>,
  ) => {
    // Your default sort mapping logic
    let newSort: string[] | undefined;

    if (Array.isArray(sorter)) {
      newSort = sorter
        .filter((s) => s.order && s.field)
        .map((s) => `${s.field}:${s.order === 'ascend' ? 'asc' : 'desc'}`);
    } else if (sorter.order && sorter.field) {
      newSort = [
        `${sorter.field}:${sorter.order === 'ascend' ? 'asc' : 'desc'}`,
      ];
    }

    onSortChange?.(newSort?.length ? newSort : undefined);

    // Forward to parent in case it also wants to handle pagination/filter manually
    onChange?.(pagination, filters, sorter, extra);
  };

  return (
    <div className='galaxy-table-container'>
      <Table<T>
        dataSource={data}
        columns={processedColumns}
        loading={loading}
        pagination={
          pagination
            ? {
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                onChange: pagination.onChange,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: pagination.showTotal
                  ? pagination.showTotal
                  : (total, range) =>
                      `${range[0]}-${range[1]} ${t('of')} ${total} ${t('items')}`,
              }
            : false
        }
        className='shadow-md rounded-lg'
        scroll={{ x: 'max-content' }}
        size='middle'
        onChange={handleChange}
        {...props}
      />
    </div>
  );
};

export default GalaxyTable;
export { GalaxyTable };
