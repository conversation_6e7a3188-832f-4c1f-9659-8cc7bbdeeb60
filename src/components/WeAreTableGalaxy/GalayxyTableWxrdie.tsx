import { Table } from 'antd';
import type { TableProps, ColumnsType } from 'antd/es/table';
import { useTranslation } from 'react-i18next';
import type { GalaxyTableProps } from '@/types';
import { sortByDate } from '@/utils/tableUtils';

const GalaxyTableWrxdie = <T extends Record<string, unknown>>({
  data,
  columns,
  loading = false,
  customSort = false,
  pagination,
  ...props
}: GalaxyTableProps<T> & Omit<TableProps<T>, 'dataSource' | 'columns'>) => {
  const { t } = useTranslation('galaxyTable');

  const processedColumns: ColumnsType<T> = customSort
    ? columns.map((column) => {
        const dataIndex = column.dataIndex as keyof T;
        if (
          !dataIndex ||
          column.key === 'actions' ||
          column.key === 'isNew' ||
          column.key === 'passportNumber'
        )
          return column;
        if (
          dataIndex === 'passportNumber' ||
          dataIndex === 'date' ||
          dataIndex === 'createdAt'
        ) {
          return {
            ...column,
            sorter: (a: T, b: T) =>
              sortByDate(
                a[dataIndex] as string | null | undefined,
                b[dataIndex] as string | null | undefined,
              ),
            sortDirections: ['descend', 'ascend'] as const,
            defaultSortOrder: 'descend' as const,
          };
        }

        return {
          ...column,
          sorter: (a: T, b: T) => {
            const aVal = a[dataIndex];
            const bVal = b[dataIndex];
            return String(aVal ?? '').localeCompare(String(bVal ?? ''), 'vi', {
              sensitivity: 'base',
            });
          },
          sortDirections: ['ascend', 'descend'] as const,
        };
      })
    : columns;

  return (
    <div className='galaxy-table-container'>
      <Table<T>
        dataSource={data}
        columns={processedColumns}
        loading={loading}
        pagination={
          pagination
            ? {
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                onChange: pagination.onChange,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} ${t('of')} ${total} ${t('items')}`,
              }
            : false
        }
        className='shadow-md rounded-lg'
        scroll={{ x: 'max-content' }}
        size='middle'
        {...props}
      />
    </div>
  );
};

export default GalaxyTableWrxdie;
