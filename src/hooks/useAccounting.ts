import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import {
  fetchAccountingStats,
  fetchFinancialStatements,
  fetchTransactionSummaries,
  fetchReconciliationReports,
  generateFinancialStatement,
  generateTransactionSummary,
  generateReconciliationReport,
} from '../pages/Accounting/apis/accountingApis';
import type {
  AccountingFilters,
  FinancialStatement,
  // Transaction,
} from '@/types';

// Query keys for accounting data
export const queryKeys = {
  accounting: {
    all: ['accounting'] as const,
    stats: () => [...queryKeys.accounting.all, 'stats'] as const,
    statements: (filters?: AccountingFilters) =>
      [...queryKeys.accounting.all, 'statements', filters] as const,
    summaries: (filters?: AccountingFilters) =>
      [...queryKeys.accounting.all, 'summaries', filters] as const,
    reconciliation: (filters?: AccountingFilters) =>
      [...queryKeys.accounting.all, 'reconciliation', filters] as const,
  },
};

// Accounting stats hook
export const useAccountingStats = () => {
  return useQuery({
    queryKey: queryKeys.accounting.stats(),
    queryFn: fetchAccountingStats,
  });
};

// Financial statements hook
export const useFinancialStatements = (filters?: AccountingFilters) => {
  return useQuery({
    queryKey: queryKeys.accounting.statements(filters),
    queryFn: () => fetchFinancialStatements(filters),
  });
};

// Transaction summaries hook
export const useTransactionSummaries = (filters?: AccountingFilters) => {
  return useQuery({
    queryKey: queryKeys.accounting.summaries(filters),
    queryFn: () => fetchTransactionSummaries(filters),
  });
};

// Reconciliation reports hook
export const useReconciliationReports = (filters?: AccountingFilters) => {
  return useQuery({
    queryKey: queryKeys.accounting.reconciliation(filters),
    queryFn: () => fetchReconciliationReports(filters),
  });
};

// Generate financial statement hook
export const useGenerateFinancialStatement = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      type,
      period,
    }: {
      type: FinancialStatement['type'];
      period: FinancialStatement['period'];
      options?: {
        includeComparisons: boolean;
        includeNotes: boolean;
        detailedBreakdown: boolean;
      };
    }) => generateFinancialStatement(type, period),
    onSuccess: (response) => {
      message.success(
        response.message || 'Financial statement generated successfully',
      );
      // Invalidate and refetch financial statements
      queryClient.invalidateQueries({
        queryKey: queryKeys.accounting.statements({}),
      });
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to generate financial statement');
    },
  });
};

// Generate transaction summary hook
export const useGenerateTransactionSummary = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (
      period: { startDate: string; endDate: string },
      // options?: {
      //   includeBreakdowns: string[];
      //   minAmount: number;
      //   transactionTypes: Transaction['type'];
      // },
    ) => generateTransactionSummary(period),
    onSuccess: (response) => {
      message.success(
        response.message || 'Transaction summary generated successfully',
      );
      // Invalidate and refetch transaction summaries
      queryClient.invalidateQueries({
        queryKey: queryKeys.accounting.summaries({}),
      });
    },
    onError: (error: Error) => {
      message.error(error.message || 'Failed to generate transaction summary');
    },
  });
};

// Generate reconciliation report hook
export const useGenerateReconciliationReport = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      registerId,
      period,
    }: {
      registerId: string;
      period: { startDate: string; endDate: string };
      options?: {
        shiftDetails: string;
        reconciliationOptions: {
          includeDiscrepancies: boolean;
          autoReconcile: boolean;
        };
      };
    }) => generateReconciliationReport(registerId, period),
    onSuccess: (response) => {
      message.success(
        response.message || 'Reconciliation report generated successfully',
      );
      // Invalidate and refetch reconciliation reports
      queryClient.invalidateQueries({
        queryKey: queryKeys.accounting.reconciliation({}),
      });
    },
    onError: (error: Error) => {
      message.error(
        error.message || 'Failed to generate reconciliation report',
      );
    },
  });
};
