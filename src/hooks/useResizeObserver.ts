import { useEffect, useRef } from 'react';

/**
 * Custom hook to observe size changes of an HTML element using ResizeObserver.
 *
 * @template T - The type of the HTML element being observed.
 * @param {function} callback - A function to execute whenever the element's size changes.
 * @returns {React.RefObject<T>} - A ref object to be attached to the target element.
 */
const useResizeObserver = <T extends Element>(
  callback: (entry: ResizeObserverEntry) => void,
) => {
  /** Ref to store the target element for observation */
  const elementRef = useRef<T>(null);

  useEffect(() => {
    const elementCurrent = elementRef.current;
    if (!elementCurrent) return () => {};

    /** Create a ResizeObserver instance to monitor size changes */
    const resizeObserver = new ResizeObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.target === elementCurrent) {
          callback(entry);
        }
      });
    });

    resizeObserver.observe(elementCurrent);

    /** Cleanup function to unobserve the element when the component unmounts */
    return () => {
      resizeObserver.disconnect();
    };
  }, [callback]);

  return elementRef;
};

export default useResizeObserver;
