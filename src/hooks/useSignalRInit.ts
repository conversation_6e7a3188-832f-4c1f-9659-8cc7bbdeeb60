import { useEffect } from 'react';
import { useSignalR } from './useSignalR';

/**
 * Hook to ensure SignalR is initialized only once in the app
 * This should be called in the main App component or a high-level component
 * to ensure the main notification handler is registered only once
 */
export const useSignalRInit = () => {
  const signalR = useSignalR();

  useEffect(() => {
    // Start the SignalR connection
    signalR.startConnection();

    return () => {
      // Don't stop connection on unmount as other components might need it
      // Connection will be managed by the service itself
    };
  }, [signalR]);

  return {
    isConnected: signalR.isConnected,
    signalRService: signalR.signalRService,
  };
};

export default useSignalRInit;
