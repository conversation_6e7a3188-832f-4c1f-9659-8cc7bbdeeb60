import { useState } from 'react';

type UsePaginationProps = {
  currentPageInit?: number;
  pageSizeInit?: number;
};
const usePagination = (useProps: UsePaginationProps) => {
  const { currentPageInit = 1, pageSizeInit = 10 } = useProps;

  const [currentPage, setCurrentPage] = useState(currentPageInit);
  const [pageSize, setPageSize] = useState(pageSizeInit);

  return { currentPage, setCurrentPage, pageSize, setPageSize };
};

export { usePagination };
