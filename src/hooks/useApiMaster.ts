import {
  useMutation,
  useQuery,
  type QueryClient,
  type UseMutationOptions,
  type UseQueryOptions,
} from '@tanstack/react-query';
import type { AxiosError } from 'axios';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLogout } from '@/hooks';
import { useNotifyStore } from '@/stores';
import type { QueryKey, RequestError } from '@/types';

type MasterQueryProps<
  TDATA = unknown,
  TPARAMS = unknown,
  TERROR = AxiosError<RequestError>,
> = {
  onQuery?: (params?: TPARAMS) => void;
  onSuccess?: (data: TDATA, params?: TPARAMS) => void;
  onError?: (error: TERROR) => void;
  onSettled?: () => void;
  params?: TPARAMS;
  qf: (params?: TPARAMS) => Promise<TDATA>;
  blockNotify?: boolean;
  enabled?: boolean;
};

const useMasterQuery = <
  TDATA = unknown,
  TPARAMS = unknown,
  TERROR = AxiosError<RequestError>,
  TQueryKey extends QueryKey = QueryKey,
>(
  options: UseQueryOptions<TDATA, TERROR, TPARAMS, TQueryKey> &
    MasterQueryProps<TDATA, TPARAMS, TERROR>,
  client?: QueryClient,
) => {
  // status
  const {
    qf,
    params,
    onQuery,
    onSuccess,
    onError,
    onSettled,
    blockNotify,
    queryKey,
    enabled,
    ...originOptions
  } = options;
  const [isError, setIsError] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  // hooks
  const { pushBEQ } = useNotifyStore();
  const logout = useLogout();

  const { data: whyIsParam, ...result } = useQuery<
    TDATA,
    TERROR,
    TPARAMS,
    TQueryKey
  >(
    {
      ...originOptions,
      enabled,
      queryKey,
      queryFn: async () => {
        if (onQuery) onQuery(params);
        return new Promise<TDATA>((resolve) => {
          resolve(qf(params));
        })
          .then((value) => {
            setIsSuccess(true);
            setIsError(false);
            if (onSuccess) onSuccess(value, params);

            return value;
          })
          .catch((error) => {
            setIsSuccess(false);
            setIsError(true);
            const requestError = error as AxiosError<RequestError>;
            // notify
            if (
              !blockNotify &&
              requestError.response?.status &&
              [400].includes(requestError.response.status)
            )
              if (
                requestError.response?.data?.errors &&
                requestError.response?.status &&
                [400].includes(requestError.response.status)
              ) {
                pushBEQ([
                  {
                    title: import.meta.env.VITE_APP_TITLE || 'YF Pay Admin',
                    des: requestError.response.data.title,
                  },
                ]);

                Object.values(requestError.response.data.errors).forEach(
                  (eachError) => {
                    pushBEQ([
                      {
                        title: import.meta.env.VITE_APP_TITLE,
                        des: eachError.join(''),
                      },
                    ]);
                  },
                );
              }

            // auth
            if (requestError.response?.status === 401) {
              logout();
            }
            if (onError) onError(error);
            return error;
          })
          .finally(() => {
            if (onSettled) onSettled();
          });
      },
    },
    client,
  );
  const data = (whyIsParam as TDATA) ?? undefined;

  return { ...result, data, isSuccess, isError };
};

// mutation
type MasterMutationProps<
  TDATA = unknown,
  TPARAMS = unknown,
  TERROR = AxiosError<RequestError>,
> = {
  onSuccess?: (data: TDATA, params?: TPARAMS) => void;
  onError?: (error: TERROR) => void;
  onSettled?: () => void;
  blockNotify?: boolean;
};

const useMasterMutation = <
  TDATA = unknown,
  TPARAMS = void,
  TERROR = AxiosError<RequestError>,
  TContext = unknown,
>(
  options: UseMutationOptions<TDATA, TERROR, TPARAMS, TContext> &
    MasterMutationProps<TDATA, TPARAMS, TERROR>,
  queryClient?: QueryClient,
) => {
  const { mutationFn, onSuccess, onError, blockNotify, ...originOptions } =
    options;

  // Hooks
  const { pushBEQ } = useNotifyStore();
  const logout = useLogout();
  const { t } = useTranslation('useApiMaster');

  const newConfig: UseMutationOptions<TDATA, TERROR, TPARAMS, TContext> = {
    ...originOptions,
    mutationFn: (props) => {
      return new Promise((resolve) => {
        if (mutationFn) resolve(mutationFn(props));
      });
    },
    onSuccess: (data, parms, context) => {
      if (onSuccess) onSuccess(data, parms, context);
    },
    onError: (error, params, context) => {
      const requestError = error as AxiosError<RequestError>;
      if (!blockNotify) {
        if (!requestError.response) {
          pushBEQ([
            {
              title: import.meta.env.VITE_APP_TITLE || 'YF Pay Admin',
              des: t('networkErrorDescription'),
            },
          ]);
          logout();
          return;
        }

        if (
          requestError.response?.data &&
          requestError.response?.status &&
          [400].includes(requestError.response.status)
        )
          pushBEQ([
            {
              title: import.meta.env.VITE_APP_TITLE || 'YF Pay Admin',
              des: requestError.response?.data?.title,
            },
          ]);

        if (
          requestError.response?.data?.errors &&
          requestError.response?.status &&
          [400].includes(requestError.response.status)
        ) {
          Object.values(requestError.response.data.errors).forEach(
            (eachError) => {
              pushBEQ([
                {
                  title: import.meta.env.VITE_APP_TITLE || 'YF Pay Admin',
                  des: eachError.join(''),
                },
              ]);
            },
          );
        }
      }

      // auth
      if (requestError.response?.status === 401) {
        logout();
      }
      if (onError) onError(error, params, context);
    },
  };

  return useMutation<TDATA, TERROR, TPARAMS, TContext>(newConfig, queryClient);
};

type UseMasterQueryProps<
  Other = unknown,
  TPARAMS = unknown,
  TDATA = unknown,
  TERROR = AxiosError<RequestError>,
> = Other & {
  params?: TPARAMS;
  onQuery?: (params?: TPARAMS) => void;
  onSuccess?: (data: TDATA, params?: TPARAMS) => void;
  onError?: (error: TERROR) => void;
  onSettled?: () => void;
  blockNotify?: boolean;
  enabled?: boolean;
};
type UseMasterMutationProps<
  TDATA = unknown,
  TPARAMS = unknown,
  Other = unknown,
  TERROR = AxiosError<RequestError>,
> = {
  onSuccess?: (data: TDATA, params?: TPARAMS) => void;
  onError?: (error: TERROR) => void;
  onSettled?: () => void;
  blockNotify?: boolean;
} & Other;

export { useMasterQuery, useMasterMutation };
export type { UseMasterQueryProps, UseMasterMutationProps };
