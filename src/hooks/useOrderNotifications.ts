import { useEffect, useCallback } from 'react';
import { signalRService, SignalRHandlerType } from '@/services/signalRService';
import {
  NotificationType,
  NotificationMessageKey,
} from '@/types/notifications';
import type { NotificationPayload } from '@/types/notifications';

interface UseOrderNotificationsProps {
  onOrderUpdate?: () => void;
  enabled?: boolean;
  handlerType?: SignalRHandlerType;
}

/**
 * Custom hook to handle order-related SignalR notifications
 * Automatically refetches order data when order notifications are received
 */
export const useOrderNotifications = ({
  onOrderUpdate,
  enabled = true,
  handlerType = SignalRHandlerType.ORDER_REFETCH,
}: UseOrderNotificationsProps = {}) => {
  const handleOrderNotification = useCallback(
    (notification: NotificationPayload) => {
      // Only handle order-related notifications
      if (notification.type !== NotificationType.Order) {
        return;
      }

      console.log('Order notification received for refetch:', notification);

      // Check if it's an order-related message
      const orderMessageKeys = [
        NotificationMessageKey.Order_CreatedComplete,
        NotificationMessageKey.Order_CreatedFail,
        NotificationMessageKey.Order_StatusUpdated,
        NotificationMessageKey.Order_PaymentReceived,
        NotificationMessageKey.Order_Cancelled,
      ];

      if (orderMessageKeys.includes(notification.messageKey)) {
        console.log(
          'Triggering order data refetch due to notification:',
          notification.messageKey,
        );

        // Call the refetch callback if provided
        if (onOrderUpdate) {
          onOrderUpdate();
        }
      }
    },
    [onOrderUpdate],
  );

  useEffect(() => {
    if (!enabled) {
      return;
    }

    console.log('Setting up order notification handler for refetch');

    // Subscribe to order notifications with specific handler type
    const unsubscribe = signalRService.onNotification(
      handleOrderNotification,
      handlerType,
    );

    // Cleanup on unmount
    return () => {
      console.log('Cleaning up order notification handler for refetch');
      unsubscribe();
    };
  }, [handleOrderNotification, enabled]);

  return {
    // Could return additional utilities if needed
    isListening: enabled,
  };
};

export default useOrderNotifications;
