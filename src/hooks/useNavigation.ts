import { useNavigate, useLocation } from 'react-router-dom';
import { ROUTES } from '@/constants/routes.ts';

export const useNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const navigateTo = {
    dashboard: () => navigate('/dashboard'),
    userManagement: () => navigate('/users-management'),
    orders: () => navigate('/orders'),
    management: () => navigate('/management'),
    rolePermission: () => navigate('/role-permission'),
    settings: () => navigate('/settings'),
    profile: () => navigate('/profile'),
  };

  const getCurrentPage = () => {
    const path = location.pathname;
    if (path === ROUTES.ROOT || path === ROUTES.DASHBOARD) return 'dashboard';
    if (path.startsWith(ROUTES.USERMANAGEMENT)) return 'userManagement';
    if (path.startsWith(ROUTES.ORDERS)) return 'orders';
    if (path.startsWith(ROUTES.MANAGEMENT)) return 'management';
    if (path.startsWith(ROUTES.ACCOUNTING)) return 'accounting';
    if (path.startsWith(ROUTES.ROLE_PERMISSION)) return 'rolePermission';
    if (path.startsWith(ROUTES.SETTINGS)) return 'settings';
    return '';
  };

  const isCurrentPage = (page: string) => {
    return getCurrentPage() === page;
  };

  return {
    navigate,
    navigateTo,
    getCurrentPage,
    isCurrentPage,
    location,
  };
};
