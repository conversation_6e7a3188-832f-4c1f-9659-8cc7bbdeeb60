import { useEffect, useCallback, useRef } from 'react';
import {
  signalRService,
  SignalRHandlerType,
  type HandlerType,
} from '@/services/signalRService';
import { useAuthStore } from '@/stores/useAuthStore';
import { useSignalRNotificationStore } from '@/stores/useSignalRNotificationStore';
import {
  type NotificationPayload,
  type NotificationHandler,
  NotificationType,
  NotificationMessageKey,
} from '@/types/notifications';
import { NotificationSeverity } from '@/types/notifications';

export const useSignalR = () => {
  const { isAuthenticated } = useAuthStore();
  const {
    addNotification,
    markAsRead,
    soundEnabled,
    showDesktopNotifications,
    autoMarkAsRead,
  } = useSignalRNotificationStore();

  const notificationHandlerRef = useRef<(() => void) | null>(null);

  // Play notification sound
  const playNotificationSound = useCallback(() => {
    if (!soundEnabled) return;

    try {
      // Create a simple notification sound
      const audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(
        0.01,
        audioContext.currentTime + 0.3,
      );

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.3);
    } catch (error) {
      console.warn('Could not play notification sound:', error);
    }
  }, [soundEnabled]);

  // Show desktop notification
  const showDesktopNotification = useCallback(
    (notification: NotificationPayload) => {
      if (!showDesktopNotifications || !('Notification' in window)) return;

      if (Notification.permission === 'granted') {
        const title = getNotificationTitle(notification);
        const body = getNotificationDescription(notification);

        const desktopNotification = new Notification(title, {
          body,
          icon: '/favicon.ico',
          badge: '/favicon.ico',
          tag: notification.notificationId,
          requireInteraction:
            notification.severity === NotificationSeverity.Error ||
            notification.severity === NotificationSeverity.Warning, // Warning or Error
        });

        desktopNotification.onclick = () => {
          window.focus();
          desktopNotification.close();

          // Auto mark as read if enabled
          if (autoMarkAsRead) {
            markAsRead(notification.notificationId);
            signalRService
              .markNotificationAsRead(notification.notificationId)
              .catch(console.error);
          }
        };

        // Auto close after 5 seconds for non-critical notifications
        if (
          notification.severity !== NotificationSeverity.Error &&
          notification.severity !== NotificationSeverity.Warning
        ) {
          setTimeout(() => {
            desktopNotification.close();
          }, 5000);
        }
      } else if (Notification.permission === 'default') {
        Notification.requestPermission();
      }
    },
    [showDesktopNotifications, autoMarkAsRead, markAsRead],
  );

  // Get notification title based on type and message key
  const getNotificationTitle = (notification: NotificationPayload): string => {
    // This could be enhanced with i18n
    switch (notification.type) {
      case NotificationType.Order:
        return 'Order Update';
      case NotificationType.User:
        return 'User Update';
      case NotificationType.Staff:
        return 'Staff Update';
      case NotificationType.Counter:
        return 'Counter Update';
      case NotificationType.System:
        return 'System Notification';
      case NotificationType.Warning:
        return 'Warning';
      case NotificationType.Info:
        return 'Information';
      case NotificationType.Announcement:
        return 'Announcement';
      default:
        return 'Notification';
    }
  };

  // Get notification description based on message key
  const getNotificationDescription = (
    notification: NotificationPayload,
  ): string => {
    // This could be enhanced with i18n and message key mapping
    switch (notification.messageKey) {
      case NotificationMessageKey.Order_CreatedComplete:
        return 'Order has been created successfully';
      case NotificationMessageKey.Order_CreatedFail:
        return 'Failed to create order';
      case NotificationMessageKey.Order_StatusUpdated:
        return 'Order status has been updated';
      case NotificationMessageKey.User_Registered:
        return 'New user has been registered';
      case NotificationMessageKey.Staff_Added:
        return 'New staff member has been added';
      case NotificationMessageKey.Counter_Created:
        return 'New counter has been created';
      case NotificationMessageKey.System_Error:
        return 'System error occurred';
      default:
        return (
          notification.metadata?.description || 'You have a new notification'
        );
    }
  };

  // Handle incoming notifications
  const handleNotification = useCallback(
    (rawNotification: any) => {
      console.log('Handling raw notification:', rawNotification);

      // Parse and transform the notification data
      const notification: NotificationPayload = {
        notificationId: rawNotification.notificationId,
        messageKey: rawNotification.messageKey,
        type: rawNotification.type,
        severity: rawNotification.severity,
        metadata:
          typeof rawNotification.metadata === 'string'
            ? JSON.parse(rawNotification.metadata)
            : rawNotification.metadata || {},
        isRead: rawNotification.isRead,
        isBroadcast: rawNotification.isBroadcast,
        createdAt: rawNotification.createdAt,
      };

      console.log('Parsed notification:', notification);

      // Add to store
      addNotification(notification);

      // Play sound
      playNotificationSound();

      // Show desktop notification
      showDesktopNotification(notification);

      // Auto mark as read if enabled and not critical
      if (
        autoMarkAsRead &&
        notification.severity !== NotificationSeverity.Error &&
        notification.severity !== NotificationSeverity.Warning
      ) {
        setTimeout(() => {
          markAsRead(notification.notificationId);
          signalRService
            .markNotificationAsRead(notification.notificationId)
            .catch(console.error);
        }, 3000);
      }
    },
    [
      addNotification,
      playNotificationSound,
      showDesktopNotification,
      autoMarkAsRead,
      markAsRead,
    ],
  );

  // Start SignalR connection
  const startConnection = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      await signalRService.start();
    } catch (error) {
      console.error('Failed to start SignalR connection:', error);
    }
  }, [isAuthenticated]);

  // Stop SignalR connection
  const stopConnection = useCallback(async () => {
    try {
      await signalRService.stop();
    } catch (error) {
      console.error('Failed to stop SignalR connection:', error);
    }
  }, []);

  // Mark notification as read
  const markNotificationAsRead = useCallback(
    async (notificationId: string) => {
      try {
        markAsRead(notificationId);
        await signalRService.markNotificationAsRead(notificationId);
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    },
    [markAsRead],
  );

  // Setup SignalR event handlers (only once globally)
  useEffect(() => {
    console.log('Registering main SignalR notification handler');
    notificationHandlerRef.current = signalRService.onNotification(
      handleNotification,
      SignalRHandlerType.MAIN,
    );

    return () => {
      if (notificationHandlerRef.current) {
        notificationHandlerRef.current();
      }
    };
  }, [handleNotification]);

  // Auto-connect when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      startConnection();
    } else {
      stopConnection();
    }
  }, [isAuthenticated, startConnection, stopConnection]);

  // Request notification permission on mount
  useEffect(() => {
    if (
      showDesktopNotifications &&
      'Notification' in window &&
      Notification.permission === 'default'
    ) {
      Notification.requestPermission();
    }
  }, [showDesktopNotifications]);

  return {
    isConnected: signalRService.isConnected(),
    startConnection,
    stopConnection,
    markNotificationAsRead,
    signalRService,
  };
};

// Hook for adding custom notification handlers
// Note: This should only be used for additional handlers, not core functionality
export const useSignalRHandler = (
  handler: NotificationHandler,
  handlerType: HandlerType,
  enabled: boolean = true,
) => {
  const handlerRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    if (!enabled) return;

    console.log(`Setting up SignalR handler: ${handlerType}`);
    handlerRef.current = signalRService.onNotification(handler, handlerType);

    return () => {
      console.log(`Cleaning up SignalR handler: ${handlerType}`);
      if (handlerRef.current) {
        handlerRef.current();
      }
    };
  }, [handler, handlerType, enabled]);

  return {
    isEnabled: enabled,
  };
};

// Hook for connection state monitoring
export const useSignalRConnectionState = () => {
  const isConnected = signalRService.isConnected();

  return {
    isConnected,
  };
};
