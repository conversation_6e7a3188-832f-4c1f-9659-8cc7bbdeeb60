import { useEffect, useCallback, useRef } from 'react';
import { useSignalRNotificationStore } from '@/stores/useSignalRNotificationStore';
import { signalRService } from '@/services/signalRService';
import { useAuthStore } from '@/stores/useAuthStore';
import type { NotificationPayload, NotificationHandler } from '@/types/notifications';

export const useSignalR = () => {
  const { isAuthenticated } = useAuthStore();
  const {
    addNotification,
    markAsRead,
    soundEnabled,
    showDesktopNotifications,
    autoMarkAsRead,
  } = useSignalRNotificationStore();

  const notificationHandlerRef = useRef<(() => void) | null>(null);
  const connectionHandlerRef = useRef<(() => void) | null>(null);

  // Play notification sound
  const playNotificationSound = useCallback(() => {
    if (!soundEnabled) return;
    
    try {
      // Create a simple notification sound
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
      
      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.3);
    } catch (error) {
      console.warn('Could not play notification sound:', error);
    }
  }, [soundEnabled]);

  // Show desktop notification
  const showDesktopNotification = useCallback((notification: NotificationPayload) => {
    if (!showDesktopNotifications || !('Notification' in window)) return;

    if (Notification.permission === 'granted') {
      const title = getNotificationTitle(notification);
      const body = getNotificationDescription(notification);
      
      const desktopNotification = new Notification(title, {
        body,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: notification.notificationId,
        requireInteraction: notification.severity === 2 || notification.severity === 3, // Warning or Error
      });

      desktopNotification.onclick = () => {
        window.focus();
        desktopNotification.close();
        
        // Auto mark as read if enabled
        if (autoMarkAsRead) {
          markAsRead(notification.notificationId);
          signalRService.markNotificationAsRead(notification.notificationId).catch(console.error);
        }
      };

      // Auto close after 5 seconds for non-critical notifications
      if (notification.severity < 2) {
        setTimeout(() => {
          desktopNotification.close();
        }, 5000);
      }
    } else if (Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, [showDesktopNotifications, autoMarkAsRead, markAsRead]);

  // Get notification title based on type and message key
  const getNotificationTitle = (notification: NotificationPayload): string => {
    // This could be enhanced with i18n
    switch (notification.type) {
      case 4: // Order
        return 'Order Update';
      case 5: // User
        return 'User Update';
      case 6: // Staff
        return 'Staff Update';
      case 7: // Counter
        return 'Counter Update';
      case 0: // System
        return 'System Notification';
      case 1: // Warning
        return 'Warning';
      case 2: // Info
        return 'Information';
      case 3: // Announcement
        return 'Announcement';
      default:
        return 'Notification';
    }
  };

  // Get notification description based on message key
  const getNotificationDescription = (notification: NotificationPayload): string => {
    // This could be enhanced with i18n and message key mapping
    switch (notification.messageKey) {
      case 400: // Order_CreatedComplete
        return 'Order has been created successfully';
      case 401: // Order_CreatedFail
        return 'Failed to create order';
      case 402: // Order_StatusUpdated
        return 'Order status has been updated';
      case 500: // User_Registered
        return 'New user has been registered';
      case 600: // Staff_Added
        return 'New staff member has been added';
      case 700: // Counter_Created
        return 'New counter has been created';
      case 200: // System_Error
        return 'System error occurred';
      default:
        return notification.metadata?.description || 'You have a new notification';
    }
  };

  // Handle incoming notifications
  const handleNotification = useCallback((notification: NotificationPayload) => {
    console.log('Handling notification:', notification);
    
    // Add to store
    addNotification(notification);
    
    // Play sound
    playNotificationSound();
    
    // Show desktop notification
    showDesktopNotification(notification);
    
    // Auto mark as read if enabled and not critical
    if (autoMarkAsRead && notification.severity < 2) {
      setTimeout(() => {
        markAsRead(notification.notificationId);
        signalRService.markNotificationAsRead(notification.notificationId).catch(console.error);
      }, 3000);
    }
  }, [addNotification, playNotificationSound, showDesktopNotification, autoMarkAsRead, markAsRead]);

  // Start SignalR connection
  const startConnection = useCallback(async () => {
    if (!isAuthenticated) return;
    
    try {
      await signalRService.start();
    } catch (error) {
      console.error('Failed to start SignalR connection:', error);
    }
  }, [isAuthenticated]);

  // Stop SignalR connection
  const stopConnection = useCallback(async () => {
    try {
      await signalRService.stop();
    } catch (error) {
      console.error('Failed to stop SignalR connection:', error);
    }
  }, []);

  // Mark notification as read
  const markNotificationAsRead = useCallback(async (notificationId: string) => {
    try {
      markAsRead(notificationId);
      await signalRService.markNotificationAsRead(notificationId);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  }, [markAsRead]);

  // Setup SignalR event handlers
  useEffect(() => {
    // Clean up previous handlers
    if (notificationHandlerRef.current) {
      notificationHandlerRef.current();
    }
    if (connectionHandlerRef.current) {
      connectionHandlerRef.current();
    }

    // Setup new handlers
    notificationHandlerRef.current = signalRService.onNotification(handleNotification);
    connectionHandlerRef.current = signalRService.onConnectionStateChanged(setConnectionState);

    return () => {
      if (notificationHandlerRef.current) {
        notificationHandlerRef.current();
      }
      if (connectionHandlerRef.current) {
        connectionHandlerRef.current();
      }
    };
  }, [handleNotification, setConnectionState]);

  // Auto-connect when authenticated
  useEffect(() => {
    if (isAuthenticated && connectionState === 'Disconnected') {
      startConnection();
    } else if (!isAuthenticated && connectionState !== 'Disconnected') {
      stopConnection();
    }
  }, [isAuthenticated, connectionState, startConnection, stopConnection]);

  // Request notification permission on mount
  useEffect(() => {
    if (showDesktopNotifications && 'Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, [showDesktopNotifications]);

  return {
    connectionState,
    isConnected,
    startConnection,
    stopConnection,
    markNotificationAsRead,
    signalRService,
  };
};

// Hook for adding custom notification handlers
export const useSignalRHandler = (handler: NotificationHandler) => {
  const handlerRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    handlerRef.current = signalRService.onNotification(handler);

    return () => {
      if (handlerRef.current) {
        handlerRef.current();
      }
    };
  }, [handler]);
};

// Hook for connection state monitoring
export const useSignalRConnectionState = () => {
  const { connectionState, isConnected } = useSignalRNotificationStore();
  
  return {
    connectionState,
    isConnected,
    isConnecting: connectionState === 'Connecting',
    isReconnecting: connectionState === 'Reconnecting',
    isDisconnected: connectionState === 'Disconnected',
  };
};
