import { useState, useEffect } from 'react';

interface BreakpointConfig {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  '2xl': number;
}

const breakpoints: BreakpointConfig = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

export type Breakpoint = keyof BreakpointConfig;

interface ResponsiveState {
  width: number;
  height: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isXs: boolean;
  isSm: boolean;
  isMd: boolean;
  isLg: boolean;
  isXl: boolean;
  is2xl: boolean;
  breakpoint: Breakpoint;
}

export const useResponsive = (): ResponsiveState => {
  const [windowSize, setWindowSize] = useState<{
    width: number;
    height: number;
  }>({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
  });

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);

    // Call handler right away so state gets updated with initial window size
    handleResize();

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const getCurrentBreakpoint = (width: number): Breakpoint => {
    if (width >= breakpoints['2xl']) return '2xl';
    if (width >= breakpoints.xl) return 'xl';
    if (width >= breakpoints.lg) return 'lg';
    if (width >= breakpoints.md) return 'md';
    if (width >= breakpoints.sm) return 'sm';
    return 'xs';
  };

  const breakpoint = getCurrentBreakpoint(windowSize.width);

  return {
    width: windowSize.width,
    height: windowSize.height,
    isMobile: windowSize.width < breakpoints.md,
    isTablet:
      windowSize.width >= breakpoints.md && windowSize.width < breakpoints.lg,
    isDesktop: windowSize.width >= breakpoints.lg,
    isXs: breakpoint === 'xs',
    isSm: breakpoint === 'sm',
    isMd: breakpoint === 'md',
    isLg: breakpoint === 'lg',
    isXl: breakpoint === 'xl',
    is2xl: breakpoint === '2xl',
    breakpoint,
  };
};

// Hook for checking if current screen size matches a specific breakpoint or larger
export const useBreakpoint = (minBreakpoint: Breakpoint): boolean => {
  const { width } = useResponsive();
  return width >= breakpoints[minBreakpoint];
};

// Hook for getting responsive values based on current breakpoint
export const useResponsiveValue = <T>(
  values: Partial<Record<Breakpoint, T>>,
): T | undefined => {
  const { breakpoint } = useResponsive();

  // Find the best matching value for current breakpoint
  const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
  const currentIndex = breakpointOrder.indexOf(breakpoint);

  for (let i = currentIndex; i < breakpointOrder.length; i++) {
    const bp = breakpointOrder[i];
    if (values[bp] !== undefined) {
      return values[bp];
    }
  }

  return undefined;
};

// Utility function to get responsive classes
export const useResponsiveClasses = (
  classes: Partial<Record<Breakpoint, string>>,
): string => {
  const value = useResponsiveValue(classes);
  return value || '';
};

// Utility function for responsive spacing
export const useResponsiveSpacing = (
  type: 'padding' | 'margin' = 'padding',
) => {
  return useResponsiveValue({
    xs: type === 'padding' ? 'p-3' : 'm-3',
    sm: type === 'padding' ? 'p-4' : 'm-4',
    md: type === 'padding' ? 'p-5' : 'm-5',
    lg: type === 'padding' ? 'p-6' : 'm-6',
    xl: type === 'padding' ? 'p-6' : 'm-6',
    '2xl': type === 'padding' ? 'p-6' : 'm-6',
  });
};
