import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ConfigProvider, theme } from 'antd';
import { Suspense } from 'react';
import {
  Navigate,
  Route,
  BrowserRouter as Router,
  Routes,
} from 'react-router-dom';
import MainLayout from './components/Layout/MainLayout';
import PrivateRoute from './components/Layout/PrivateRoute';
import PublicLayout from './components/Layout/PublicLayout';
import LoadingPage from './components/LoadingPage';
import './i18n'; // Initialize i18n
//Don't need lazy loading
import LoadingSpinner from './components/LoadingSpinner';
import Mask from './components/Mask';
import NotifyProvider from './components/NotifyProvider';
import { NotificationToast } from './components/Notifications';
import { ROUTES } from './constants/routes';
import { useSignalR } from './hooks';
import Accounting from './pages/Accounting/Accounting';
import Dashboard from './pages/Dashboard/Dashboard';
import Login from './pages/Login/Login';
import Management from './pages/Management/Management';
import Orders from './pages/Orders/Orders';
// import RolePermissionManagement from './pages/RolePermissionManagement/RolePermissionManagement';
import UserManagement from './pages/UsersManage/UserManagement';
import { useAuthStore, useNotifyStore, useUserStore } from './stores';
import NotFound from '@/pages/NotFound/NotFound.tsx';
import Profile from '@/pages/Profile/Profile.tsx';
import Settings from '@/pages/Settings/Settings.tsx';

// Lazy load pages
// const Login = lazy(() => import('./pages/Login/Login'));
// const Dashboard = lazy(() => import('./pages/Dashboard/Dashboard'));
// const Members = lazy(() => import('./pages/Members/Members'));
// const Orders = lazy(() => import('./pages/Orders/Orders'));
// const Management = lazy(() => import('./pages/Management/Management'));
// const Accounting = lazy(() => import('./pages/Accounting/Accounting'));
// const RolePermissionManagement = lazy(
//   () => import('./pages/RolePermissionManagement/RolePermissionManagement'),
// );

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  const { isAuthenticated } = useAuthStore();
  const { basicErrorQue, shiftBEQ, basicSuccessQue, shiftBSQ } =
    useNotifyStore();
  const { isDark, isLoading } = useUserStore();

  // Initialize SignalR connection
  useSignalR();

  return (
    <QueryClientProvider client={queryClient}>
      <ConfigProvider
        theme={{
          algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
          token: {
            colorPrimary: '#1890ff',
            borderRadius: 6,
          },
          components: {
            Typography: {
              titleMarginTop: 0,
              titleMarginBottom: 0,
            },
          },
        }}
      >
        <Router>
          <Suspense fallback={<LoadingPage />}>
            <Routes>
              {/* Root redirect */}
              <Route
                path={ROUTES.ROOT}
                element={
                  isAuthenticated ? (
                    <Navigate to={ROUTES.DASHBOARD} replace />
                  ) : (
                    <Navigate to={ROUTES.LOGIN} replace />
                  )
                }
              />

              {/* Public pages */}
              <Route element={<PublicLayout />}>
                <Route path={ROUTES.LOGIN} element={<Login />} />
                <Route path='*' element={<NotFound />} />
              </Route>

              {/* Private pages */}
              <Route element={<PrivateRoute />}>
                <Route element={<MainLayout />}>
                  <Route path={ROUTES.DASHBOARD} element={<Dashboard />} />
                  <Route
                    path={ROUTES.USERMANAGEMENT}
                    element={<UserManagement />}
                  />
                  <Route path={ROUTES.ORDERS} element={<Orders />} />
                  <Route path={ROUTES.MANAGEMENT} element={<Management />} />
                  <Route path={ROUTES.ACCOUNTING} element={<Accounting />} />
                  {/*<Route*/}
                  {/*  path={ROUTES.ROLE_PERMISSION}*/}
                  {/*  element={<RolePermissionManagement />}*/}
                  {/*/>*/}
                  <Route path={ROUTES.SETTINGS} element={<Settings />} />
                  <Route path={ROUTES.PROFILE} element={<Profile />} />
                </Route>
              </Route>
            </Routes>
          </Suspense>
        </Router>

        <Mask open={isLoading}>
          <LoadingSpinner />
        </Mask>

        <NotifyProvider
          basicQue={basicErrorQue}
          type='error'
          shiftBsicQue={shiftBEQ}
        />

        <NotifyProvider
          basicQue={basicSuccessQue}
          type='success'
          shiftBsicQue={shiftBSQ}
        />

        {/* SignalR Notification Toast */}
        <NotificationToast />

        <ReactQueryDevtools initialIsOpen={false} />
      </ConfigProvider>
    </QueryClientProvider>
  );
}

export default App;
