# SignalR Notification System

A comprehensive real-time notification system for the Galaxy Admin interface using SignalR, React, TypeScript, Zustand, and Ant Design.

## Features

- ✅ **Real-time notifications** via SignalR WebSocket connection
- ✅ **Multiple handlers support** - easily add custom notification handlers
- ✅ **Global accessibility** via window object
- ✅ **Connection lifecycle management** - automatic reconnection with exponential backoff
- ✅ **Beautiful UI components** - modern notification dropdowns, panels, and toasts
- ✅ **Zustand state management** - persistent settings and notification history
- ✅ **Desktop notifications** - browser native notifications with permission handling
- ✅ **Sound notifications** - customizable audio alerts
- ✅ **Auto-mark as read** - configurable automatic read status
- ✅ **Responsive design** - mobile, tablet, and desktop support
- ✅ **Dark mode support** - seamless theme integration
- ✅ **TypeScript support** - full type safety

## Architecture

### Core Components

1. **SignalR Service** (`src/services/signalRService.ts`)
   - Singleton service managing SignalR connection
   - Handler-based event system
   - Automatic reconnection logic
   - Global window access

2. **Zustand Store** (`src/stores/useSignalRNotificationStore.ts`)
   - Notification state management
   - User preferences persistence
   - Connection state tracking

3. **React Hooks** (`src/hooks/useSignalR.ts`)
   - Connection management
   - Event handling
   - Sound and desktop notifications

4. **UI Components** (`src/components/Notifications/`)
   - `NotificationDropdown` - Header notification button with dropdown
   - `NotificationPanel` - Full notification management panel
   - `NotificationItem` - Individual notification display
   - `NotificationToast` - Real-time toast notifications
   - `ConnectionStatus` - SignalR connection status indicator

## Usage

### Basic Setup

The system is automatically initialized in the main App component:

```tsx
import { useSignalR } from '@/hooks';

function App() {
  // Initialize SignalR connection
  useSignalR();
  
  return (
    <div>
      {/* Your app content */}
      <NotificationToast />
    </div>
  );
}
```

### Adding Custom Handlers

```tsx
import { useSignalRHandler } from '@/hooks/useSignalR';

function MyComponent() {
  // Add custom notification handler
  useSignalRHandler((notification) => {
    if (notification.type === NotificationType.Order) {
      // Handle order notifications specifically
      console.log('Order notification:', notification);
    }
  });

  return <div>My Component</div>;
}
```

### Global Access

```tsx
// Access SignalR service globally
window.signalRService.markNotificationAsRead(notificationId);

// Check connection status
const isConnected = window.signalRService.isConnected();
```

### Using Notification Components

```tsx
import { NotificationDropdown, NotificationPanel } from '@/components/Notifications';

function Header() {
  const [panelOpen, setPanelOpen] = useState(false);

  return (
    <div>
      <NotificationDropdown onOpenPanel={() => setPanelOpen(true)} />
      <NotificationPanel 
        open={panelOpen} 
        onClose={() => setPanelOpen(false)} 
      />
    </div>
  );
}
```

## API Reference

### SignalR Hub Specification

**Hub Path:** `/hubs/notification`
**Role:** Admin (extracted from token)

#### Receive Method
- **Method:** `ReceiveNotification`
- **Payload:**
```typescript
{
  notificationId: string;
  messageKey: NotificationMessageKey;
  type: NotificationType;
  severity: NotificationSeverity;
  metadata: Record<string, any>;
  isRead: boolean;
  isBroadcast: boolean;
  createdAt: string;
}
```

#### Callback Method
- **Method:** `MarkNotificationAsRead`
- **Parameter:** `notificationId: string`
- **Response:** `void`

### Enums

#### NotificationMessageKey
```typescript
enum NotificationMessageKey {
  // System
  System_Error = 200,
  
  // Background Job
  Job_OrderCreated = 300,
  
  // Order module
  Order_CreatedComplete = 400,
  Order_CreatedFail = 401,
  
  // User module
  User_Registered = 500,
  User_StatusChanged = 501,
  
  // Staff module
  Staff_Added = 600,
  Staff_StatusChanged = 601,
  
  // Counter module
  Counter_Created = 700,
  Counter_StatusChanged = 701,
}
```

#### NotificationType
```typescript
enum NotificationType {
  System = 0,
  Warning = 1,
  Info = 2,
  Announcement = 3,
  Order = 4,
  User = 5,
  Staff = 6,
  Counter = 7,
}
```

#### NotificationSeverity
```typescript
enum NotificationSeverity {
  Info = 0,
  Success = 1,
  Warning = 2,
  Error = 3,
}
```

## Configuration

### Environment Variables

```env
VITE_API_BASE_URL=http://localhost:5000/api
```

### Store Settings

The notification system supports the following user preferences:

- `soundEnabled` - Enable/disable notification sounds
- `showDesktopNotifications` - Enable/disable browser notifications
- `autoMarkAsRead` - Automatically mark notifications as read
- `notificationDuration` - Toast notification display duration
- `maxNotifications` - Maximum notifications to keep in memory

## Styling

The system includes comprehensive CSS styling in `src/styles/notifications.css`:

- Modern animations and transitions
- Responsive design breakpoints
- Dark mode support
- Severity-based color coding
- Smooth scrollbars and hover effects

## Testing

Use the `NotificationDemo` component to test the notification system:

```tsx
import { NotificationDemo } from '@/components/Notifications/NotificationDemo';

function TestPage() {
  return <NotificationDemo />;
}
```

## Best Practices

1. **Handler Management**: Always clean up handlers to prevent memory leaks
2. **Error Handling**: Wrap SignalR calls in try-catch blocks
3. **Performance**: Limit the number of notifications in memory
4. **UX**: Use appropriate severity levels for different notification types
5. **Accessibility**: Ensure notifications are accessible to screen readers

## Troubleshooting

### Connection Issues
- Check if the SignalR hub is running on the server
- Verify authentication token is valid
- Check network connectivity
- Review browser console for WebSocket errors

### Notification Not Appearing
- Verify notification handlers are properly registered
- Check if notifications are being filtered
- Ensure notification panel/dropdown is properly integrated
- Review browser notification permissions

### Performance Issues
- Limit the number of active handlers
- Clear old notifications regularly
- Check for memory leaks in custom handlers
- Monitor WebSocket connection stability
