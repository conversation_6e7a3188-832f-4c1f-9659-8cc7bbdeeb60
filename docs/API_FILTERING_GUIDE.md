# Dynamic API Filtering and Sorting Guide

This guide explains how to use the dynamic API filtering and sorting system implemented in the Galaxy Admin interface.

## Overview

The system provides a flexible way to filter and sort data according to the API specification:

- **Filter Template**: `{PascalCase<PERSON>ey}~{operator}~{value}`
- **Sort Template**: `{<PERSON><PERSON>ase<PERSON>ey}:{direction}`
- **URL Template**: `{endpoint}?filter={<PERSON><PERSON>aseKey}~{operator}~{value}&sort={PascalCaseKey}:{direction}`

**Important**: All parameter keys are automatically converted to PascalCase when sent to the server.

## Available Operators

### String Operators
- `eq` - equals
- `ne` - not equals  
- `c` - contains
- `sw` - starts with
- `ew` - ends with

### Number/Date Operators
- `eq` - equals
- `ne` - not equals
- `gt` - greater than
- `gte` - greater than or equals
- `lt` - less than
- `lte` - less than or equals

### Boolean/UUID/Enum Operators
- `eq` - equals
- `ne` - not equals

## Core Components

### 1. Type Definitions (`src/types/index.ts`)

```typescript
interface ApiFilter {
  key: string;
  operator: FilterOperator;
  value: string | number | boolean;
}

interface ApiSort {
  key: string;
  direction: SortDirection;
}

interface ApiFiltersParams {
  filters?: ApiFilter[];
  sorts?: ApiSort[];
  page?: number;
  pageSize?: number;
}
```

### 2. Utility Functions (`src/utils/apiUtils.ts`)

- `toPascalCase(str)` - Converts camelCase to PascalCase
- `buildFilterString(filter)` - Converts filter to query string format with PascalCase keys
- `buildSortString(sort)` - Converts sort to query string format with PascalCase keys
- `buildApiQueryString(params)` - Builds complete query string
- `isValidOperatorForType(type, operator)` - Validates operators

### 3. Generic Hook (`src/hooks/useApiFilters.ts`)

The `useApiFilters` hook provides complete filter and sort management:

```typescript
const filterHook = useApiFilters({
  defaultFilters: [],
  defaultSorts: [{ key: 'createdAt', direction: 'desc' }],
  defaultPage: 1,
  defaultPageSize: 10,
  filterConfigs: [...],
  sortConfigs: [...]
});
```

## Usage Examples

### Basic Usage

```typescript
import { useApiFilters } from '@/hooks/useApiFilters';

const MyComponent = () => {
  const {
    filters,
    sorts,
    apiParams,
    queryString,
    addFilter,
    removeFilter,
    toggleSort,
    updatePage
  } = useApiFilters();

  // Add a filter (will be sent as OrderStatus~eq~COMPLETED)
  const handleStatusFilter = (status: string) => {
    addFilter({ key: 'orderStatus', operator: 'eq', value: status });
  };

  // Toggle sorting (will be sent as CreatedAt:desc)
  const handleSort = (key: string) => {
    toggleSort(key);
  };

  // Use apiParams in your API call
  const { data } = useQuery({
    queryKey: ['data', apiParams],
    queryFn: () => fetchData(apiParams)
  });
};
```

### Orders-Specific Hook

```typescript
import { useOrdersWithFilters } from '@/hooks/useOrders';

const OrdersPage = () => {
  const {
    data,
    isLoading,
    filters,
    sorts,
    filterByStatus,
    filterByUser,
    toggleSort,
    clearFilters,
    updatePage
  } = useOrdersWithFilters();

  return (
    <div>
      {/* Filter controls */}
      <Select onChange={filterByStatus}>
        <Option value="PENDING">Pending</Option>
        <Option value="COMPLETED">Completed</Option>
      </Select>

      <Input.Search onSearch={filterByUser} />

      {/* Sort controls */}
      <Button onClick={() => toggleSort('createdAt')}>
        Sort by Date
      </Button>

      {/* Data display */}
      {data?.data.data.map(order => (
        <div key={order.orderCode}>{order.userId}</div>
      ))}
    </div>
  );
};
```

## Supported Filter Fields (Orders)

All fields are automatically converted to PascalCase when sent to server:

- `orderType` → `OrderType`
- `orderStatus` → `OrderStatus`
- `userId` → `UserId`
- `counterId` → `CounterId`
- `quantity` → `Quantity`
- `staffId` → `StaffId`
- `pricePerUnit` → `PricePerUnit`
- `totalPrice` → `TotalPrice`
- `currencyUnit` → `CurrencyUnit`
- `updatedAt` → `UpdatedAt`
- `createdAt` → `CreatedAt`

## Generated Query Examples

### Single Filter
```
filter=OrderStatus~eq~COMPLETED
```

### Multiple Filters
```
filter=OrderStatus~eq~COMPLETED&filter=TotalPrice~gte~100&filter=UserId~eq~user123
```

### With Sorting
```
filter=OrderStatus~eq~COMPLETED&sort=CreatedAt:desc&sort=TotalPrice:asc
```

### Complete Example
```
filter=OrderStatus~eq~COMPLETED&filter=TotalPrice~gte~100&sort=CreatedAt:desc&page=2&pageSize=20
```

## Best Practices

1. **Use Correct Field Names**: Use camelCase in your code, PascalCase conversion is automatic
2. **Validate Operators**: Use `isValidOperatorForType` to ensure correct operators
3. **Reset Pagination**: Always reset to page 1 when filters change
4. **Provide Clear UI**: Show active filters and sorts to users
5. **Handle Loading States**: Show loading indicators during API calls
6. **Error Handling**: Implement proper error handling for invalid filters

## Environment Configuration

Switch between mock and real API:

```bash
# Use real API
VITE_MOCK_API=false

# Use mock data
VITE_MOCK_API=true
```

## Example Component

See `src/components/Examples/ApiFiltersExample.tsx` for a complete working example that demonstrates all features of the filtering system with PascalCase parameter conversion.
