/** Paste the run script
 * pm2 start ecosystem.config.cjs --only GA-3013 --attach
 */

const path = require('path');
const vitePath = path.normalize('./node_modules/vite/bin/vite.js');
const interpreterPath = path.normalize(
  '/home/<USER>/.nvm/versions/node/v18.20.2/bin/node',
); // -- ubuntu
// const interpreterPath = path.normalize('C:\\Program Files\\nodejs\\node.exe'); // -- windows

module.exports = {
  apps: [
    {
      name: 'GA-3013',
      script: vitePath,
      interpreter: interpreterPath,
      args: '--host',
    },
  ],
};
